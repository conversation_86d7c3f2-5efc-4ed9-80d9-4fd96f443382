#!/bin/bash

# Firebase deployment script for Talk Maxer

# Change to the Firebase directory
cd "$(dirname "$0")"

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "Firebase CLI is not installed. Installing..."
    npm install -g firebase-tools
fi

# Login to Firebase (if not already logged in)
firebase login

# Deploy functions
echo "Deploying Firebase Functions..."
firebase deploy --only functions

echo "Deployment complete!"
