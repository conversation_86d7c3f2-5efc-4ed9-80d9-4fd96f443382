rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read and write their own profile
    match /users/{userId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow create, update: if request.auth != null && request.auth.uid == userId;
    }

    // Allow authenticated users to read all posts
    match /posts/{postId} {
      allow read: if request.auth != null;

      // Allow users to create posts
      allow create: if request.auth != null &&
                     request.resource.data.authorId == request.auth.uid;

      // Allow users to update only their own posts
      allow update: if request.auth != null &&
                     resource.data.authorId == request.auth.uid;

      // Allow users to delete only their own posts
      allow delete: if request.auth != null &&
                     resource.data.authorId == request.auth.uid;

      // Special case: Allow any authenticated user to update the likeCount, dislikeCount,
      // commentCount, likedBy, and dislikedBy fields of any post
      allow update: if request.auth != null &&
                     request.resource.data.diff(resource.data).affectedKeys()
                     .hasOnly(['likeCount', 'dislikeCount', 'commentCount', 'likedBy', 'dislikedBy']);
    }

    // Allow authenticated users to read all comments
    match /comments/{commentId} {
      allow read: if request.auth != null;

      // Allow users to create comments
      allow create: if request.auth != null &&
                     request.resource.data.authorId == request.auth.uid;

      // Allow users to update only their own comments
      allow update: if request.auth != null &&
                     resource.data.authorId == request.auth.uid;

      // Allow users to delete only their own comments
      allow delete: if request.auth != null &&
                     resource.data.authorId == request.auth.uid;
    }

    // Allow authenticated users to read and write to the postLikes collection
    match /postLikes/{likeId} {
      // The likeId should be in the format: postId_userId
      allow read: if request.auth != null;

      // Allow users to create and update only their own likes
      allow create, update: if request.auth != null &&
                             likeId.split('_')[1] == request.auth.uid;

      // Allow users to delete only their own likes
      allow delete: if request.auth != null &&
                     likeId.split('_')[1] == request.auth.uid;
    }

    // Rate limits collection - only allow read/write by admin functions
    match /rateLimits/{document=**} {
      allow read, write: if false; // Only Cloud Functions can access this collection
    }
  }
}
