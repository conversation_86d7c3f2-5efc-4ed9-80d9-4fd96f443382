# Firebase Integration

This directory contains all Firebase-related files for the Talk Maxer application.

## Directory Structure

- `Functions/` - Contains the Firebase Cloud Functions code
- `.firebaserc` - Firebase project configuration
- `firebase.json` - Firebase deployment configuration

## Setup Instructions

### Prerequisites
- Node.js installed
- Firebase CLI installed (`npm install -g firebase-tools`)
- Xcode for iOS development

### Firebase Project Setup

1. **Create a Firebase Project** (if not already done):
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Click "Add project" and follow the instructions

2. **Enable Authentication**:
   - In your Firebase project, go to "Authentication" in the left menu
   - Click "Get started"
   - Enable "Email/Password" as a sign-in method

3. **Get Configuration File**:
   - In Firebase console, go to Project Settings (gear icon)
   - Add an iOS app if you haven't already
   - Enter your bundle ID (e.g., com.yourdomain.TalkMaxer)
   - Download the `GoogleService-Info.plist` file
   - Add this file to your Xcode project in the `Config` directory

### Firebase Functions Setup

1. **Initialize Firebase** (already done in this project):
   ```bash
   firebase init
   # Select Authentication and Functions
   ```

2. **Set API Key for Gemini**:
   ```bash
   firebase functions:config:set gemini.key="YOUR_GEMINI_API_KEY"
   ```

3. **Deploy Functions**:
   - This requires the Blaze (pay-as-you-go) plan
   ```bash
   firebase deploy --only functions
   ```

4. **Or Run Locally with Emulators**:
   ```bash
   firebase emulators:start
   ```

### Important Files

- `Functions/index.js` - The Cloud Functions code
- `../Services/FirebaseService.swift` - Swift service to interact with Firebase Functions
- `../Services/AuthService.swift` - Swift service for authentication

### For Production Use

1. **Upgrade to Blaze Plan**:
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Click on your project
   - Go to "Usage and Billing" > "Details & Settings"
   - Click "Modify plan" and select "Blaze"

2. **Deploy Functions**:
   ```bash
   firebase deploy --only functions
   ```

3. **Update the iOS App**:
   - Make sure `FirebaseService.swift` is configured correctly
   - For production, it should use the remote functions, not the emulator
