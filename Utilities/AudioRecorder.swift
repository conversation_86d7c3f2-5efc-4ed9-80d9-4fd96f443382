import Foundation
import AVFoundation
import SwiftUI

/// A class to handle audio recording functionality
class AudioRecorder: NSObject, ObservableObject, AVAudioRecorderDelegate {
    // MARK: - Published Properties

    /// Whether audio is currently being recorded
    @Published var isRecording = false

    /// List of recorded audio files
    @Published var recordings: [Recording] = []

    /// Current recording duration
    @Published var recordingDuration: TimeInterval = 0

    /// Current recording state
    @Published var recordingState: RecordingState = .ready

    /// Current audio power level (average)
    @Published var averagePower: Float = -160.0

    /// Current audio power level (peak)
    @Published var peakPower: Float = -160.0

    /// Array of recent power levels for visualization
    @Published var powerLevels: [Float] = Array(repeating: -160.0, count: 30)

    // MARK: - Enums

    /// Represents the recording state
    enum RecordingState: Equatable {
        case ready
        case recording
        case paused
        case error(String)

        // Implement Equatable for the error case
        static func == (lhs: RecordingState, rhs: RecordingState) -> Bool {
            switch (lhs, rhs) {
            case (.ready, .ready):
                return true
            case (.recording, .recording):
                return true
            case (.paused, .paused):
                return true
            case (.error(let lhsMessage), .error(let rhsMessage)):
                return lhsMessage == rhsMessage
            default:
                return false
            }
        }
    }

    // MARK: - Private Properties

    /// The AVAudioRecorder instance
    private var audioRecorder: AVAudioRecorder?

    /// Timer for updating recording duration
    private var durationTimer: Timer?

    /// The directory where recordings are stored
    private var recordingsDirectory: URL {
        FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0].appendingPathComponent("Recordings")
    }

    // MARK: - Initialization

    override init() {
        super.init()
        createRecordingsDirectory()
        fetchRecordings()
    }

    deinit {
        stopRecording()
    }

    // MARK: - Public Methods

    /// Start recording audio
    func startRecording() {
        // Set up the audio session
        let audioSession = AVAudioSession.sharedInstance()

        do {
            // Configure audio session for recording
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true)

            // Create a simple sequential name (Voice 1, Voice 2, etc.)
            let nextNumber = getNextVoiceNumber()
            let fileName = "Voice \(nextNumber).m4a"
            let fileURL = recordingsDirectory.appendingPathComponent(fileName)

            // Recording settings
            let settings: [String: Any] = [
                AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
                AVSampleRateKey: 44100.0,
                AVNumberOfChannelsKey: 2,
                AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
            ]

            // Create and configure the audio recorder
            audioRecorder = try AVAudioRecorder(url: fileURL, settings: settings)
            audioRecorder?.delegate = self
            audioRecorder?.isMeteringEnabled = true

            // Start recording
            if audioRecorder?.record() == true {
                isRecording = true
                recordingState = .recording
                recordingDuration = 0

                // Start duration timer
                startDurationTimer()

                print("Recording started at: \(fileURL.path)")
            } else {
                recordingState = .error("Failed to start recording")
                print("Failed to start recording")
            }
        } catch {
            recordingState = .error("Recording error: \(error.localizedDescription)")
            print("Recording error: \(error)")
        }
    }

    /// Pause the current recording
    func pauseRecording() {
        guard let recorder = audioRecorder, isRecording else { return }

        if recorder.isRecording {
            recorder.pause()
            isRecording = false
            recordingState = .paused

            // Stop duration timer
            durationTimer?.invalidate()
            durationTimer = nil

            print("Recording paused")
        }
    }

    /// Resume a paused recording
    func resumeRecording() {
        guard let recorder = audioRecorder, !isRecording, recordingState == .paused else { return }

        if recorder.record() {
            isRecording = true
            recordingState = .recording

            // Restart duration timer
            startDurationTimer()

            print("Recording resumed")
        }
    }

    /// Stop and save the current recording
    func stopRecording() {
        guard let recorder = audioRecorder else { return }

        recorder.stop()
        isRecording = false
        recordingState = .ready

        // Stop duration timer
        durationTimer?.invalidate()
        durationTimer = nil

        // Refresh the recordings list
        fetchRecordings()

        print("Recording stopped and saved")
    }

    /// Delete a recording
    func deleteRecording(at indexSet: IndexSet) {
        let fileManager = FileManager.default

        for index in indexSet {
            let recording = recordings[index]

            do {
                try fileManager.removeItem(at: recording.fileURL)
                print("Deleted recording: \(recording.fileURL.lastPathComponent)")
            } catch {
                print("Error deleting recording: \(error)")
            }
        }

        // Refresh the recordings list
        fetchRecordings()
    }

    /// Rename a recording
    func renameRecording(at index: Int, to newName: String) {
        guard index < recordings.count else { return }

        let recording = recordings[index]
        let fileManager = FileManager.default

        // Create a new URL with the new name
        let newFileName = "\(newName).m4a"
        let newFileURL = recordingsDirectory.appendingPathComponent(newFileName)

        do {
            try fileManager.moveItem(at: recording.fileURL, to: newFileURL)
            print("Renamed recording to: \(newFileName)")

            // Refresh the recordings list
            fetchRecordings()
        } catch {
            print("Error renaming recording: \(error)")
        }
    }

    // MARK: - Private Methods

    /// Get the next voice number for recording naming
    private func getNextVoiceNumber() -> Int {
        // Extract existing voice numbers from recordings
        var highestNumber = 0

        for recording in recordings {
            let name = recording.fileName
            if name.hasPrefix("Voice ") {
                // Try to extract the number part
                let numberPart = name.dropFirst(6) // Remove "Voice " prefix
                if let number = Int(numberPart) {
                    highestNumber = max(highestNumber, number)
                }
            }
        }

        // Return the next number in sequence
        return highestNumber + 1
    }

    /// Create the recordings directory if it doesn't exist
    private func createRecordingsDirectory() {
        let fileManager = FileManager.default

        if !fileManager.fileExists(atPath: recordingsDirectory.path) {
            do {
                try fileManager.createDirectory(at: recordingsDirectory, withIntermediateDirectories: true)
                print("Created recordings directory at: \(recordingsDirectory.path)")
            } catch {
                print("Error creating recordings directory: \(error)")
            }
        }
    }

    /// Fetch all recordings from the recordings directory
    private func fetchRecordings() {
        let fileManager = FileManager.default

        do {
            // Get all files in the recordings directory
            let files = try fileManager.contentsOfDirectory(at: recordingsDirectory, includingPropertiesForKeys: nil)

            // Filter for audio files
            let audioFiles = files.filter { $0.pathExtension == "m4a" }

            // Create Recording objects
            var newRecordings: [Recording] = []

            for file in audioFiles {
                if let attributes = try? fileManager.attributesOfItem(atPath: file.path),
                   let creationDate = attributes[.creationDate] as? Date,
                   let fileSize = attributes[.size] as? Int64 {

                    // Get audio duration
                    var duration: TimeInterval = 0
                    if let audioPlayer = try? AVAudioPlayer(contentsOf: file) {
                        duration = audioPlayer.duration
                    }

                    let recording = Recording(
                        fileURL: file,
                        creationDate: creationDate,
                        duration: duration,
                        fileSize: fileSize
                    )

                    newRecordings.append(recording)
                }
            }

            // Sort recordings by creation date (newest first)
            recordings = newRecordings.sorted(by: { $0.creationDate > $1.creationDate })

            print("Found \(recordings.count) recordings")
        } catch {
            print("Error fetching recordings: \(error)")
        }
    }

    /// Start the duration timer
    private func startDurationTimer() {
        // Stop any existing timer
        durationTimer?.invalidate()

        // Create a new timer that updates more frequently for smoother animation
        durationTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            guard let self = self, let recorder = self.audioRecorder else { return }

            // Update duration
            self.recordingDuration = recorder.currentTime

            // Update audio metering
            recorder.updateMeters()

            // Get the power levels
            self.averagePower = recorder.averagePower(forChannel: 0)
            self.peakPower = recorder.peakPower(forChannel: 0)

            // Update power levels array for visualization
            // Shift existing levels to make room for new value
            var newLevels = self.powerLevels
            newLevels.removeFirst()

            // Normalize the power level (convert from dB to a more usable range)
            // dB values typically range from -160 to 0, where 0 is loudest
            // First, bring the range from -160...0 to something more manageable like -50...0
            let adjustedPower = max(self.averagePower, -50) + 50 // Now in range 0...50

            // Then normalize to -1...1 range for easier use in visualization
            // We use 0.1 as a minimum to ensure there's always some visible activity
            let normalizedPower = max(0.1, min(1.0, adjustedPower / 50))

            // Scale to -1...1 range with some adjustment to make it more visually appealing
            let scaledPower = (normalizedPower * 2) - 1

            newLevels.append(Float(scaledPower))
            self.powerLevels = newLevels
        }
    }

    // MARK: - AVAudioRecorderDelegate

    func audioRecorderDidFinishRecording(_ recorder: AVAudioRecorder, successfully flag: Bool) {
        if flag {
            // Recording finished successfully
            isRecording = false
            recordingState = .ready

            // Refresh the recordings list
            fetchRecordings()

            print("Recording finished successfully")
        } else {
            recordingState = .error("Recording failed to finish")
            print("Recording failed to finish")
        }
    }

    func audioRecorderEncodeErrorDidOccur(_ recorder: AVAudioRecorder, error: Error?) {
        if let error = error {
            recordingState = .error("Recording error: \(error.localizedDescription)")
            print("Recording error: \(error)")
        }
    }
}

/// A struct representing a recorded audio file
struct Recording: Identifiable {
    let id = UUID()
    let fileURL: URL
    let creationDate: Date
    let duration: TimeInterval
    let fileSize: Int64

    /// The file name without extension
    var fileName: String {
        fileURL.deletingPathExtension().lastPathComponent
    }

    /// Formatted duration string (MM:SS)
    var formattedDuration: String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }

    /// Formatted file size string
    var formattedFileSize: String {
        ByteCountFormatter.string(fromByteCount: fileSize, countStyle: .file)
    }

    /// Formatted creation date string
    var formattedCreationDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: creationDate)
    }
}
