import SwiftUI
import AVFoundation

/// A class to handle audio playback functionality
class AudioEngine: NSObject, ObservableObject, AVAudioPlayerDelegate {
    // MARK: - Published Properties

    /// Whether audio is currently playing
    @Published var isPlaying = false

    /// Current playback time in seconds
    @Published var currentTime: TimeInterval = 0

    /// Total duration of the audio in seconds
    @Published var duration: TimeInterval = 0

    /// Playback progress from 0 to 1
    @Published var progress: Double = 0

    /// Current loading state of the audio
    @Published var loadingState: LoadingState = .notLoaded

    // MARK: - Enums

    /// Represents the loading state of the audio
    enum LoadingState: Equatable {
        case notLoaded
        case loading
        case loaded
        case error(String)

        // Implement Equatable for the error case
        static func == (lhs: LoadingState, rhs: LoadingState) -> Bool {
            switch (lhs, rhs) {
            case (.notLoaded, .notLoaded):
                return true
            case (.loading, .loading):
                return true
            case (.loaded, .loaded):
                return true
            case (.error(let lhsMessage), .error(let rhsMessage)):
                return lhsMessage == rhsMessage
            default:
                return false
            }
        }
    }

    // MARK: - Private Properties

    /// The AVAudioPlayer instance
    private var audioPlayer: AVAudioPlayer?

    /// Timer for updating progress
    private var progressTimer: Timer?

    // Reference to AudioFileManager
    private let audioFileManager = AudioFileManager.shared

    // MARK: - Initialization

    override init() {
        super.init()
        setupAudioSession()
    }

    deinit {
        cleanup()
    }

    // MARK: - Public Methods

    /// Clean up resources
    func cleanup() {
        progressTimer?.invalidate()
        progressTimer = nil
        audioPlayer?.stop()
        audioPlayer = nil

        // Deactivate audio session
        try? AVAudioSession.sharedInstance().setActive(false)
    }

    /// Load audio file by name
    func loadAudio(named fileName: String) {
        // Clean up any existing resources
        cleanup()

        // Update state
        loadingState = .loading

        // Debug bundle information
        debugBundleInfo()

        // First try to load from the documents directory
        if audioFileManager.fileExists(fileName: fileName) {
            let fileURL = audioFileManager.getFileURL(fileName: fileName)
            loadAudioFromURL(fileURL)
            return
        }

        // If not in documents, try to copy from bundle to documents
        audioFileManager.copyFileFromBundleToDocuments(fileName: fileName) { [weak self] fileURL in
            guard let self = self else { return }

            if let url = fileURL {
                // Successfully copied to documents, load from there
                self.loadAudioFromURL(url)
            } else {
                // Couldn't copy from bundle, try direct bundle access
                if let url = self.findAudioFile(named: fileName) {
                    self.loadAudioFromURL(url)
                } else {
                    // Try to load from data assets as a fallback
                    if let data = self.loadAudioDataFromAssets(named: fileName) {
                        self.loadAudioFromData(data, fileName: fileName)
                    } else {
                        // Try to extract from asset catalog to documents
                        if let url = audioFileManager.extractAudioFromAsset(named: fileName) {
                            self.loadAudioFromURL(url)
                        } else {
                            // All attempts failed
                            DispatchQueue.main.async {
                                self.loadingState = .error("Audio file not found: \(fileName)")
                            }
                            print("Audio file not found: \(fileName)")

                            // List all audio files in the bundle for debugging
                            self.listAllAudioFiles()

                            // List files in documents directory
                            let documentsFiles = audioFileManager.listAudioFilesInDocuments()
                            print("\nFiles in documents directory:")
                            for file in documentsFiles {
                                print("- \(file)")
                            }
                        }
                    }
                }
            }
        }
    }

    /// Start playback
    func play() {
        guard let player = audioPlayer, !isPlaying else { return }

        // Start playback
        if player.play() {
            isPlaying = true

            // Start progress timer
            startProgressTimer()
        }
    }

    /// Pause playback
    func pause() {
        guard let player = audioPlayer, isPlaying else { return }

        player.pause()
        isPlaying = false

        // Stop progress timer
        progressTimer?.invalidate()
        progressTimer = nil
    }

    /// Toggle between play and pause
    func togglePlayback() {
        if isPlaying {
            pause()
        } else {
            play()
        }
    }

    /// Seek to a specific time
    func seek(to time: TimeInterval) {
        guard let player = audioPlayer else { return }

        let targetTime = max(0, min(time, player.duration))
        player.currentTime = targetTime
        currentTime = targetTime
        progress = targetTime / player.duration
    }

    /// Seek relative to current position
    func seekRelative(by offset: TimeInterval) {
        guard let player = audioPlayer else { return }

        let targetTime = max(0, min(player.currentTime + offset, player.duration))
        player.currentTime = targetTime
        currentTime = targetTime
        progress = targetTime / player.duration
    }

    /// Set audio player directly from a URL
    func setAudioPlayer(with url: URL) -> Bool {
        do {
            // Clean up any existing resources
            cleanup()

            // Create audio player
            let player = try AVAudioPlayer(contentsOf: url)
            player.delegate = self
            let prepareResult = player.prepareToPlay()

            print("Direct player setup - Prepare result: \(prepareResult)")

            // Store player and update state
            audioPlayer = player
            duration = player.duration
            loadingState = .loaded

            print("Successfully loaded audio from URL: \(url.path)")
            print("- Duration: \(player.duration) seconds")

            return true
        } catch {
            loadingState = .error("Failed to load audio: \(error.localizedDescription)")
            print("Error loading audio from URL: \(error)")
            return false
        }
    }

    // MARK: - Private Methods

    /// Set up the audio session
    private func setupAudioSession() {
        let audioSession = AVAudioSession.sharedInstance()
        do {
            // Configure audio session for playback
            try audioSession.setCategory(.playback, mode: .default)
            try audioSession.setActive(true)
        } catch {
            print("Failed to set up audio session: \(error)")
        }
    }

    /// Load audio from a URL
    private func loadAudioFromURL(_ url: URL) {
        do {
            print("Loading audio from URL: \(url.path)")

            // Create audio player
            let player = try AVAudioPlayer(contentsOf: url)
            player.delegate = self
            let prepareResult = player.prepareToPlay()

            print("Prepare result: \(prepareResult)")

            // Store player and update state
            DispatchQueue.main.async { [self] in
                audioPlayer = player
                duration = player.duration
                loadingState = .loaded
            }

            print("Successfully loaded audio from URL")
            print("- Duration: \(player.duration) seconds")
            print("- Format: \(player.format)")
            print("- Channels: \(player.numberOfChannels)")
        } catch {
            DispatchQueue.main.async { [self] in
                loadingState = .error("Failed to load audio: \(error.localizedDescription)")
            }
            print("Error loading audio from URL: \(error)")
        }
    }

    /// Load audio from data
    private func loadAudioFromData(_ data: Data, fileName: String) {
        do {
            print("Loading audio from data asset: \(fileName), size: \(data.count) bytes")

            // Create audio player from data
            let player = try AVAudioPlayer(data: data)
            player.delegate = self
            let prepareResult = player.prepareToPlay()

            print("Prepare result: \(prepareResult)")

            // Store player and update state
            audioPlayer = player
            duration = player.duration
            loadingState = .loaded

            print("Successfully loaded audio from data asset: \(fileName)")
            print("- Duration: \(player.duration) seconds")
            print("- Format: \(player.format)")
            print("- Channels: \(player.numberOfChannels)")
        } catch {
            loadingState = .error("Failed to load audio from data: \(error.localizedDescription)")
            print("Error loading audio from data: \(error)")
        }
    }

    /// Find an audio file in the bundle
    private func findAudioFile(named fileName: String) -> URL? {
        print("Searching for audio file: \(fileName)")

        // Try to find the file in the bundle
        if let url = Bundle.main.url(forResource: fileName, withExtension: nil) {
            print("Found file without extension: \(url.path)")
            return url
        }

        // Try with common audio extensions
        for ext in ["wav", "mp3", "m4a", "aac"] {
            if let url = Bundle.main.url(forResource: fileName, withExtension: ext) {
                print("Found file with .\(ext) extension: \(url.path)")
                return url
            }
        }

        // Try with spaces replaced by underscores
        let fileNameWithUnderscores = fileName.replacingOccurrences(of: " ", with: "_")
        if let url = Bundle.main.url(forResource: fileNameWithUnderscores, withExtension: nil) {
            print("Found file with underscores: \(url.path)")
            return url
        }

        // Try with underscores and extensions
        for ext in ["wav", "mp3", "m4a", "aac"] {
            if let url = Bundle.main.url(forResource: fileNameWithUnderscores, withExtension: ext) {
                print("Found file with underscores and .\(ext) extension: \(url.path)")
                return url
            }
        }

        // Try with spaces removed
        let fileNameNoSpaces = fileName.replacingOccurrences(of: " ", with: "")
        if let url = Bundle.main.url(forResource: fileNameNoSpaces, withExtension: nil) {
            print("Found file with no spaces: \(url.path)")
            return url
        }

        // Try with no spaces and extensions
        for ext in ["wav", "mp3", "m4a", "aac"] {
            if let url = Bundle.main.url(forResource: fileNameNoSpaces, withExtension: ext) {
                print("Found file with no spaces and .\(ext) extension: \(url.path)")
                return url
            }
        }

        // Try with lowercase
        let fileNameLower = fileName.lowercased()
        if let url = Bundle.main.url(forResource: fileNameLower, withExtension: nil) {
            print("Found file with lowercase: \(url.path)")
            return url
        }

        // Try with lowercase and extensions
        for ext in ["wav", "mp3", "m4a", "aac"] {
            if let url = Bundle.main.url(forResource: fileNameLower, withExtension: ext) {
                print("Found file with lowercase and .\(ext) extension: \(url.path)")
                return url
            }
        }

        print("Could not find audio file: \(fileName)")
        return nil
    }

    /// Load audio data from assets
    private func loadAudioDataFromAssets(named name: String) -> Data? {
        // Try to load the audio file from Assets.xcassets
        print("Trying to load from asset catalog: \(name)")

        if let asset = NSDataAsset(name: name) {
            print("Found asset: \(name), size: \(asset.data.count) bytes")
            return asset.data
        }

        // Try with different path formats
        let variations = [
            "Audios/\(name)",
            name.replacingOccurrences(of: " ", with: "_"),
            name.replacingOccurrences(of: " ", with: ""),
            "audio/\(name)",
            "Audio/\(name)"
        ]

        for variation in variations {
            if let asset = NSDataAsset(name: variation) {
                print("Found asset with variation: \(variation), size: \(asset.data.count) bytes")
                return asset.data
            }
        }

        // If we can't find the asset, try to load from the bundle as a fallback
        if let url = findAudioFile(named: name), let data = try? Data(contentsOf: url) {
            print("Found audio file in bundle: \(url.path), size: \(data.count) bytes")
            return data
        }

        print("Could not find asset: \(name)")
        return nil
    }

    /// Print debug information about the bundle
    private func debugBundleInfo() {
        print("\n--- BUNDLE DEBUG INFO ---")
        print("Bundle identifier: \(Bundle.main.bundleIdentifier ?? "unknown")")
        print("Bundle path: \(Bundle.main.bundlePath)")
        print("Resource path: \(Bundle.main.resourcePath ?? "unknown")")

        // Check if the bundle has a specific structure
        let fileManager = FileManager.default
        if let resourcePath = Bundle.main.resourcePath,
           let contents = try? fileManager.contentsOfDirectory(atPath: resourcePath) {
            print("\nBundle root contents (\(contents.count) items):")
            for item in contents.prefix(10) {
                print("- \(item)")
            }
            if contents.count > 10 {
                print("... and \(contents.count - 10) more items")
            }
        }
        print("--- END BUNDLE DEBUG INFO ---\n")
    }

    /// List all audio files in the bundle
    private func listAllAudioFiles() {
        print("\n--- AUDIO FILES IN BUNDLE ---")
        let fileManager = FileManager.default
        var audioFiles: [String] = []

        // Search in the main bundle
        if let resourcePath = Bundle.main.resourcePath {
            let enumerator = fileManager.enumerator(atPath: resourcePath)
            while let file = enumerator?.nextObject() as? String {
                let ext = (file as NSString).pathExtension.lowercased()
                if ["wav", "mp3", "m4a", "aac"].contains(ext) {
                    audioFiles.append(file)
                }
            }
        }

        if audioFiles.isEmpty {
            print("No audio files found in bundle")
        } else {
            print("Found \(audioFiles.count) audio files:")
            for file in audioFiles {
                print("- \(file)")
            }
        }

        // Check for audio assets in asset catalog
        print("\nChecking asset catalog for audio files...")
        let assetNames = ["Audios", "Fundamentals of Effective Communication",
                         "The Art and Practice of Storytelling", "Understanding and Reducing Filler Words",
                         "Key Story Elements and Structure"]

        var foundAssets = false
        for name in assetNames {
            if let asset = NSDataAsset(name: name) {
                print("Found asset: \(name), size: \(asset.data.count) bytes")
                foundAssets = true
            }
        }

        if !foundAssets {
            print("No audio assets found in asset catalog")
        }

        print("--- END AUDIO FILES IN BUNDLE ---\n")
    }

    /// Start the progress timer
    private func startProgressTimer() {
        // Stop any existing timer
        progressTimer?.invalidate()

        // Create a new timer that updates less frequently (4 times per second)
        progressTimer = Timer.scheduledTimer(withTimeInterval: 0.25, repeats: true) { [weak self] _ in
            guard let self = self, let player = self.audioPlayer else { return }

            // Update progress
            self.currentTime = player.currentTime
            self.progress = player.currentTime / player.duration
        }
    }

    // MARK: - AVAudioPlayerDelegate

    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        // Update state on main thread
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            self.isPlaying = false
            self.progressTimer?.invalidate()
            self.progressTimer = nil
            self.progress = 1.0
            self.currentTime = self.duration
        }
    }
}
