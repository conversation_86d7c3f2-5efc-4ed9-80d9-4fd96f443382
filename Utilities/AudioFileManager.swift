import Foundation
import AVFoundation

class AudioFileManager {
    static let shared = AudioFileManager()

    private init() {}

    // Get the documents directory URL
    private var documentsDirectory: URL {
        FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
    }

    // Check if a file exists in the documents directory
    func fileExists(fileName: String) -> Bool {
        let fileURL = documentsDirectory.appendingPathComponent(fileName)
        return FileManager.default.fileExists(atPath: fileURL.path)
    }

    // Copy a file from the bundle to the documents directory
    func copyFileFromBundleToDocuments(fileName: String, completion: @escaping (URL?) -> Void) {
        // First check if the file already exists in documents
        let destinationURL = documentsDirectory.appendingPathComponent(fileName)
        if FileManager.default.fileExists(atPath: destinationURL.path) {
            print("File already exists in documents: \(destinationURL.path)")
            completion(destinationURL)
            return
        }

        // Try to find the file in the bundle
        guard let bundleURL = findFileInBundle(fileName: fileName) else {
            print("Could not find file in bundle: \(fileName)")
            completion(nil)
            return
        }

        do {
            // Copy the file to the documents directory
            try FileManager.default.copyItem(at: bundleURL, to: destinationURL)
            print("Successfully copied file to documents: \(destinationURL.path)")
            completion(destinationURL)
        } catch {
            print("Error copying file to documents: \(error)")
            completion(nil)
        }
    }

    // Find a file in the bundle with various naming conventions
    private func findFileInBundle(fileName: String) -> URL? {
        print("Searching for audio file: \(fileName)")

        // Try direct match
        if let url = Bundle.main.url(forResource: fileName, withExtension: nil) {
            print("Found file without extension: \(url.path)")
            return url
        }

        // Try with common audio extensions
        for ext in ["wav", "mp3", "m4a", "aac"] {
            if let url = Bundle.main.url(forResource: fileName, withExtension: ext) {
                print("Found file with .\(ext) extension: \(url.path)")
                return url
            }
        }

        // Try with spaces replaced by underscores
        let fileNameWithUnderscores = fileName.replacingOccurrences(of: " ", with: "_")
        if let url = Bundle.main.url(forResource: fileNameWithUnderscores, withExtension: nil) {
            print("Found file with underscores: \(url.path)")
            return url
        }

        // Try with underscores and extensions
        for ext in ["wav", "mp3", "m4a", "aac"] {
            if let url = Bundle.main.url(forResource: fileNameWithUnderscores, withExtension: ext) {
                print("Found file with underscores and .\(ext) extension: \(url.path)")
                return url
            }
        }

        // Try with spaces removed
        let fileNameNoSpaces = fileName.replacingOccurrences(of: " ", with: "")
        if let url = Bundle.main.url(forResource: fileNameNoSpaces, withExtension: nil) {
            print("Found file with no spaces: \(url.path)")
            return url
        }

        // Try with no spaces and extensions
        for ext in ["wav", "mp3", "m4a", "aac"] {
            if let url = Bundle.main.url(forResource: fileNameNoSpaces, withExtension: ext) {
                print("Found file with no spaces and .\(ext) extension: \(url.path)")
                return url
            }
        }

        // Try with lowercase
        let fileNameLower = fileName.lowercased()
        if let url = Bundle.main.url(forResource: fileNameLower, withExtension: nil) {
            print("Found file with lowercase: \(url.path)")
            return url
        }

        // Try with lowercase and extensions
        for ext in ["wav", "mp3", "m4a", "aac"] {
            if let url = Bundle.main.url(forResource: fileNameLower, withExtension: ext) {
                print("Found file with lowercase and .\(ext) extension: \(url.path)")
                return url
            }
        }

        print("Could not find audio file: \(fileName)")
        return nil
    }

    // Get a file URL from the documents directory
    func getFileURL(fileName: String) -> URL {
        return documentsDirectory.appendingPathComponent(fileName)
    }

    // List all audio files in the documents directory
    func listAudioFilesInDocuments() -> [String] {
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(at: documentsDirectory, includingPropertiesForKeys: nil)
            return fileURLs.filter { url in
                let ext = url.pathExtension.lowercased()
                return ["wav", "mp3", "m4a", "aac"].contains(ext)
            }.map { $0.lastPathComponent }
        } catch {
            print("Error listing files in documents: \(error)")
            return []
        }
    }

    // Extract audio data from an asset in the asset catalog
    func extractAudioFromAsset(named name: String) -> URL? {
        // We can't use NSDataAsset directly here, so we'll use a different approach
        print("Attempting to extract asset: \(name)")

        // Try to find the file in the bundle first
        if let bundleURL = findFileInBundle(fileName: name) {
            let fileURL = documentsDirectory.appendingPathComponent("\(name).\(bundleURL.pathExtension)")

            do {
                try FileManager.default.copyItem(at: bundleURL, to: fileURL)
                print("Successfully copied asset to: \(fileURL.path)")
                return fileURL
            } catch {
                print("Error copying asset: \(error)")
                return nil
            }
        }

        print("Could not find asset: \(name)")
        return nil
    }
}
