# Configuration Files

This directory contains configuration files for the Talk Maxer application.

## Files

- `GoogleService-Info.plist` - Firebase configuration file for iOS

## Important Notes

- The `GoogleService-Info.plist` file contains sensitive information and should not be shared publicly.
- This file is required for Firebase integration to work properly.
- If you're setting up a new development environment, you'll need to download this file from the Firebase console.

## How to Get a New Configuration File

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Go to Project Settings (gear icon)
4. Under "Your apps", select the iOS app
5. Click "Download GoogleService-Info.plist"
6. Place the downloaded file in this directory
