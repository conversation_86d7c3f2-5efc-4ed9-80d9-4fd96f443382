# Project Rules

## Code Style
- Use SwiftUI for all new UI components
- Follow MVVM architecture pattern
- Keep ViewModels in the ViewModels directory
- Keep Views in the Views directory
- Keep Models in Models.swift or create dedicated files for complex models
- Use @Published for observable properties
- Use async/await for asynchronous operations when possible
- Don't write unnecessary code. Only write what user prompts you to write.

## Performance
- Avoid expensive UI operations in the render path
- Minimize the use of .blur() and material effects
- Limit animations to essential UI elements
- Use LazyVStack and <PERSON><PERSON><PERSON><PERSON><PERSON> for long lists
- Cache expensive computations
- Use background threads for heavy processing

## Audio Processing
- Use SpeechManager for all speech recognition and synthesis
- Configure audio sessions appropriately before use
- Release audio resources when not in use
- Handle permissions gracefully

## API Usage
- Store API keys securely, preferably in Firebase Functions
- Use Firebase Functions as a proxy for external API calls
- Implement proper error handling for all API calls
- Cache API responses when appropriate

## User Experience
- Provide clear feedback during loading states
- Handle errors gracefully with user-friendly messages
- Support both light and dark mode
- Ensure accessibility compliance
- Maintain responsive UI during network operations

## Testing
- Write unit tests for business logic
- Test on multiple device sizes
- Test both light and dark mode
- Test with slow network conditions

## Documentation
- Document complex functions and classes
- Add comments for non-obvious code
- Keep README up to date with setup instructions
- Document API endpoints and expected responses
