import Foundation
import SwiftUI

struct ChatMessage: Identifiable, Equatable, Codable {
    let id: UUID
    let content: String
    let isUser: Bool
    let timestamp: Date

    static func == (lhs: ChatMessage, rhs: ChatMessage) -> Bool {
        lhs.id == rhs.id
    }
}


struct LessonPlan: Identifiable {
    let id: UUID
    let title: String
    let description: String
    let prompt: String
    let rules: String

    init(id: UUID = UUID(), title: String, description: String, prompt: String, rules: String = "") {
        self.id = id
        self.title = title
        self.description = description
        self.prompt = prompt
        self.rules = rules
    }
}

// New models for the communication map
struct CommunicationStage: Identifiable {
    let id: UUID
    let number: Int
    let title: String
    let description: String
    let goals: String
    let activities: [StageActivity]

    init(id: UUID = UUID(), number: Int, title: String, description: String, goals: String, activities: [StageActivity] = []) {
        self.id = id
        self.number = number
        self.title = title
        self.description = description
        self.goals = goals
        self.activities = activities
    }
}

struct StageActivity: Identifiable {
    let id: UUID
    let title: String
    let description: String
    let type: ActivityType
    let content: String

    init(id: UUID = UUID(), title: String, description: String, type: ActivityType, content: String = "") {
        self.id = id
        self.title = title
        self.description = description
        self.type = type
        self.content = content
    }
}

enum ActivityType: String, Codable {
    case audio
    case vocabulary
    case chat
    case fillerDetector
    case video
    case lesson
    case recording
    case article
    case custom
}

struct VocabularyCard: Identifiable, Codable {
    let id: UUID
    let word: String
    let meaning: String
    let exampleSentence: String
    var isCompleted: Bool

    init(id: UUID = UUID(), word: String, meaning: String, exampleSentence: String, isCompleted: Bool = false) {
        self.id = id
        self.word = word
        self.meaning = meaning
        self.exampleSentence = exampleSentence
        self.isCompleted = isCompleted
    }
}
