//
//  Talk_MaxerApp.swift
//  Talk_Maxer
//
//  Created by <PERSON><PERSON><PERSON> on 2/16/25.
//

import SwiftUI
import RevenueCat
import AppTrackingTransparency
import Firebase

@main
struct Talk_MaxerApp: App {
    @Environment(\.scenePhase) private var scenePhase

    // Create a shared instance of CommunicationViewModel
    @StateObject private var communicationViewModel = CommunicationViewModel()

    // Make sure SubscriptionService is initialized before app launches
    @StateObject private var subscriptionService = SubscriptionService.shared

    // Add the terms consent manager
    @StateObject private var termsManager = TermsConsentManager.shared

    // Authentication service
    @StateObject private var authService = AuthService.shared

    // Track whether we've requested tracking permission
    @State private var hasRequestedTracking = false

    // Special state for handling auth view from notifications
    @State private var shouldShowAuthView = false

    init() {
        // Initialize Firebase
        FirebaseApp.configure()

        // Set up RevenueCat debug logs for development
        #if DEBUG
        Purchases.logLevel = .debug
        #endif
    }

    var body: some Scene {
        WindowGroup {
            ZStack {
                // First, check if terms have been accepted
                if !termsManager.hasAcceptedTerms {
                    // Show Terms of Use overlay before anything else
                    TermsConsentView()
                        .transition(.opacity)
                        .zIndex(100) // Ensure it's on top
                } else {
                    // Only show authentication or main content after terms are accepted
                    if !authService.isAuthenticated || shouldShowAuthView {
                        AuthenticationView()
                            .environmentObject(authService)
                            .onAppear {
                                // Reset the flag when auth view appears
                                shouldShowAuthView = false
                            }
                    } else {
                        // Main app content when authenticated
                        MainTabView()
                            .environmentObject(communicationViewModel)
                            .environmentObject(authService)
                            .onAppear {
                                // Force check subscription status when app appears
                                subscriptionService.forceCheckSubscriptionStatus()

                                // Request tracking authorization after a delay
                                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                                    requestTrackingAuthorization()
                                }

                                // Setup notification observer for showing auth view
                                NotificationCenter.default.addObserver(
                                    forName: NSNotification.Name("ShowAuthView"),
                                    object: nil,
                                    queue: .main
                                ) { _ in
                                    shouldShowAuthView = true
                                }
                            }
                            .onDisappear {
                                // Remove notification observer
                                NotificationCenter.default.removeObserver(
                                    self,
                                    name: NSNotification.Name("ShowAuthView"),
                                    object: nil
                                )
                            }
                    }
                }
            }
        }
        .onChange(of: scenePhase) { _, newPhase in
            // Ensure UserDefaults is synchronized when app moves to background
            if newPhase == .background {
                UserDefaults.standard.synchronize()
            }

            // Force check subscription status when app becomes active
            if newPhase == .active {
                subscriptionService.forceCheckSubscriptionStatus()

                // Request tracking if we haven't already
                if !hasRequestedTracking {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        requestTrackingAuthorization()
                    }
                }
            }
        }
    }

    private func requestTrackingAuthorization() {
        if #available(iOS 14, *) {
            // Check current status first
            if ATTrackingManager.trackingAuthorizationStatus == .notDetermined {
                ATTrackingManager.requestTrackingAuthorization { status in
                    DispatchQueue.main.async {
                        self.hasRequestedTracking = true
                        print("Tracking authorization status: \(status.rawValue)")
                    }
                }
            } else {
                hasRequestedTracking = true
            }
        }
    }
}
