# Talk Maxer Project Structure

This document outlines the organization of the Talk Maxer project.

## Directory Structure

- **Assets.xcassets/** - Contains all app assets including images and audio files
- **Config/** - Contains configuration files like GoogleService-Info.plist
- **Firebase/** - Contains all Firebase-related files
  - **Functions/** - Firebase Cloud Functions code
  - **.firebaserc** - Firebase project configuration
  - **firebase.json** - Firebase deployment configuration
- **Models/** - Data models used throughout the app
- **Services/** - Service classes for API calls, authentication, etc.
- **Utilities/** - Utility classes and helper functions
- **ViewModels/** - View models following MVVM architecture
- **Views/** - SwiftUI views

## Key Files

- **Talk_MaxerApp.swift** - Main app entry point
- **ContentView.swift** - Main content view
- **Package.swift** - Swift Package Manager configuration
- **.augment/rules.txt** - Project rules and guidelines

## Firebase Integration

All Firebase-related files are now organized in the `Firebase/` directory. The configuration file (`GoogleService-Info.plist`) is stored in the `Config/` directory.

For more information about Firebase integration, see the README.md file in the Firebase directory.

## Models

Data models are stored in the `Models/` directory. These include:

- **LessonCategory.swift** - Model for lesson categories
- **Post.swift** - Model for user posts

## Services

Service classes are stored in the `Services/` directory. These include:

- **AuthService.swift** - Authentication service
- **FirebaseService.swift** - Firebase API service
- **SubscriptionService.swift** - In-app purchase service
- **TermsConsentManager.swift** - Terms of service consent manager
- **VocabularyService.swift** - Vocabulary service

## ViewModels

View models are stored in the `ViewModels/` directory. These include:

- **CommunicationViewModel.swift** - Main view model for communication features
- **GeminiViewModel.swift** - View model for Gemini AI integration
- **PostViewModel.swift** - View model for posts
- **SpeechManager.swift** - Speech recognition and synthesis
- **VocabularyViewModel.swift** - View model for vocabulary features

## Views

UI views are stored in the `Views/` directory. These include various screens and components used throughout the app.
