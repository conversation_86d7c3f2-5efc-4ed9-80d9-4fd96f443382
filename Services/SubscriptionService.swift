import Foundation
import RevenueCat
import SwiftUI
import UIKit

class SubscriptionService: NSObject, ObservableObject {
    static let shared = SubscriptionService()

    @Published var isSubscribed = false
    @Published var packages: [Package] = []
    @Published var isLoadingProducts = false
    @Published var willAutoRenew = true
    @Published var expirationDate: Date?

    private let apiKey = "appl_URnFlEvDYChyQBwssCLEDkBKTfi"
    private let entitlementID = "pro" // This matches your entitlement ID in RevenueCat
    private let autoRenewKey = "willAutoRenew" // UserDefaults key for auto-renewal status

    // Add a timer for periodic checks
    private var backgroundCheckTimer: Timer?

    private override init() {
        super.init()
        // Load auto-renewal preference from UserDefaults
        willAutoRenew = UserDefaults.standard.bool(forKey: autoRenewKey)

        configureRevenueCat()
        setupObservers()

        // Force immediate check on init
        forceCheckSubscriptionStatus()

        // Setup background check for expired subscriptions
        setupBackgroundCheck()
    }

    deinit {
        backgroundCheckTimer?.invalidate()
    }

    private func configureRevenueCat() {
        Purchases.logLevel = .debug
        Purchases.configure(withAPIKey: apiKey)
    }

    private func setupObservers() {
        // Listen for purchases or changes in subscription status
        Purchases.shared.delegate = self

        // Listen for app foreground notifications
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleAppForeground),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
    }

    @objc private func handleAppForeground() {
        // Check subscription status whenever app comes to foreground
        print("App became active - checking subscription status")
        forceCheckSubscriptionStatus()
    }

    // Setup a timer to periodically check for expired subscriptions
    private func setupBackgroundCheck() {
        // Check every hour for expired subscriptions
        backgroundCheckTimer = Timer.scheduledTimer(
            timeInterval: 3600, // 1 hour in seconds
            target: self,
            selector: #selector(backgroundSubscriptionCheck),
            userInfo: nil,
            repeats: true
        )
    }

    @objc private func backgroundSubscriptionCheck() {
        print("Running background subscription check")
        forceCheckSubscriptionStatus()
    }

    // Regular update that won't force a network call if recent
    func updateSubscriptionStatus() {
        Purchases.shared.getCustomerInfo { [weak self] customerInfo, error in
            guard let self = self else { return }

            if let error = error {
                print("Error fetching customer info: \(error.localizedDescription)")
                return
            }

            self.processCustomerInfo(customerInfo)
        }
    }

    // Force check regardless of when we last checked
    func forceCheckSubscriptionStatus(completion: ((Bool) -> Void)? = nil) {
        print("Forcing subscription status check...")

        Purchases.shared.getCustomerInfo(fetchPolicy: .fetchCurrent) { [weak self] customerInfo, error in
            guard let self = self else {
                completion?(false)
                return
            }

            if let error = error {
                print("Error fetching customer info: \(error.localizedDescription)")
                completion?(false)
                return
            }

            // Specifically check for expired subscriptions
            if let entitlement = customerInfo?.entitlements[self.entitlementID],
               let expirationDate = entitlement.expirationDate,
               expirationDate < Date() {
                print("⚠️ Subscription expired on \(expirationDate)")
            }

            self.processCustomerInfo(customerInfo)
            completion?(self.isSubscribed)
        }
    }

    private func processCustomerInfo(_ customerInfo: CustomerInfo?) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            let wasSubscribed = self.isSubscribed
            let newSubscriptionStatus = customerInfo?.entitlements[self.entitlementID]?.isActive == true

            // Check for expired subscription
            if wasSubscribed && !newSubscriptionStatus {
                print("⚠️ Subscription has ended - locking premium features")
            }

            self.isSubscribed = newSubscriptionStatus

            // Get actual auto-renewal status and expiration date from RevenueCat
            if let entitlement = customerInfo?.entitlements[self.entitlementID] {
                // Only update if we have a real subscription
                if entitlement.isActive {
                    self.willAutoRenew = entitlement.willRenew
                    self.expirationDate = entitlement.expirationDate
                    // Save to UserDefaults
                    UserDefaults.standard.set(self.willAutoRenew, forKey: self.autoRenewKey)
                }
            } else {
                // Clear expiration date if no subscription
                self.expirationDate = nil
            }

            // Log subscription status with more details for debugging
            print("RevenueCat subscription status updated: \(self.isSubscribed ? "SUBSCRIBED" : "NOT SUBSCRIBED")")
            if let entitlement = customerInfo?.entitlements[self.entitlementID] {
                print("Entitlement details: isActive=\(entitlement.isActive), willRenew=\(entitlement.willRenew), expirationDate=\(String(describing: entitlement.expirationDate))")

                // Log if the subscription is about to expire
                if let expirationDate = entitlement.expirationDate {
                    let timeToExpiration = expirationDate.timeIntervalSince(Date())
                    if timeToExpiration > 0 && timeToExpiration < 60*60*24*3 { // Less than 3 days
                        print("⚠️ Subscription about to expire in \(Int(timeToExpiration/3600)) hours")
                    }
                }
            } else {
                print("No entitlement found with ID: \(self.entitlementID)")
            }

            if let allEntitlements = customerInfo?.entitlements, allEntitlements.all.count > 0 {
                print("All available entitlements: \(allEntitlements.all.keys.joined(separator: ", "))")
            }

            // If subscription status changed, post a notification
            if wasSubscribed != self.isSubscribed {
                print("Subscription status changed from \(wasSubscribed) to \(self.isSubscribed)")
                NotificationCenter.default.post(
                    name: NSNotification.Name("SubscriptionStatusChanged"),
                    object: nil,
                    userInfo: ["isSubscribed": self.isSubscribed]
                )
            }
        }
    }

    // Helper method to check if a specific lesson is available based on subscription status
    func isLessonAvailable(title: String) -> Bool {
        // "At a Grocery Store" is always available
        if title == "At a Grocery Store" {
            return true
        }

        // All other lessons require subscription
        return isSubscribed
    }

    // Helper method to check if a specific stage is available based on subscription status
    func isStageAvailable(stageNumber: Int) -> Bool {
        // Stage 1 is always available as a free sample
        if stageNumber == 1 {
            return true
        }

        // All other stages require subscription
        return isSubscribed
    }

    // Helper method to check if the post feature is available
    func isPostFeatureAvailable() -> Bool {
        // Post feature requires subscription
        return isSubscribed
    }

    func fetchOfferings() {
        isLoadingProducts = true
        packages = []

        Purchases.shared.getOfferings { [weak self] offerings, error in
            guard let self = self else { return }

            DispatchQueue.main.async {
                self.isLoadingProducts = false

                if let error = error {
                    print("Error fetching offerings: \(error.localizedDescription)")
                    return
                }

                if let packages = offerings?.current?.availablePackages {
                    self.packages = packages
                }
            }
        }
    }

    func purchase(package: Package, completion: @escaping (Bool, Error?) -> Void) {
        Purchases.shared.purchase(package: package) { [weak self] transaction, customerInfo, error, userCancelled in
            guard let self = self else { return }

            if userCancelled {
                completion(false, nil)
                return
            }

            if let error = error {
                completion(false, error)
                return
            }

            DispatchQueue.main.async {
                self.isSubscribed = customerInfo?.entitlements[self.entitlementID]?.isActive == true
                completion(self.isSubscribed, nil)
            }
        }
    }

    func restorePurchases(completion: @escaping (Bool) -> Void) {
        Purchases.shared.restorePurchases { [weak self] customerInfo, error in
            guard let self = self else {
                completion(false)
                return
            }

            if let error = error {
                print("Error restoring purchases: \(error.localizedDescription)")
                completion(false)
                return
            }

            DispatchQueue.main.async {
                self.isSubscribed = customerInfo?.entitlements[self.entitlementID]?.isActive == true
                completion(self.isSubscribed)
            }
        }
    }

    // Toggle auto-renewal preference (for debugging)
    func toggleAutoRenewal() {
        willAutoRenew.toggle()
        UserDefaults.standard.set(willAutoRenew, forKey: autoRenewKey)

        // In a real implementation, this would call RevenueCat's API to change renewal status
        // For debug purposes, we're just storing the preference locally
        print("Debug: Auto-renewal preference set to \(willAutoRenew)")

        // In production, to actually change subscription status, direct user to Apple's subscription settings
        // This is why we have the "Manage Subscription" button
    }
}

// MARK: - Purchases Delegate
extension SubscriptionService: PurchasesDelegate {
    func purchases(_ purchases: Purchases, receivedUpdated customerInfo: CustomerInfo) {
        // Update subscription status when we receive updates from RevenueCat
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.isSubscribed = customerInfo.entitlements[self.entitlementID]?.isActive == true
        }
    }
}
