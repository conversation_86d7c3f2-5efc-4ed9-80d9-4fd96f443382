# RevenueCat Integration

This app uses RevenueCat for handling in-app purchases and subscriptions. To set up the project properly, you need to add the RevenueCat SDK:

## Adding RevenueCat to the Project

1. In Xcode, select your project in the Project Navigator
2. Select `File` > `Add Packages...`
3. In the search field, enter `https://github.com/RevenueCat/purchases-ios.git`
4. Click `Add Package`
5. Select the "RevenueCat" package and click `Add Package`

## Configuration Details

- API Key: `appl_URnFlEvDYChyQBwssCLEDkBKTfi`
- Entitlement ID: `premium_access`

## Testing

You can test the subscription flow using Apple's sandbox environment. When the purchase flow appears, use a sandbox tester account to complete the transaction.

For more information on RevenueCat implementation, visit the [RevenueCat documentation](https://docs.revenuecat.com/docs/getting-started). 