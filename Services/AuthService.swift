import Foundation
import Firebase
import FirebaseAuth
import FirebaseFirestore
import FirebaseFunctions
import Combine

enum AuthError: Error, Equatable {
    case signInError(String)
    case signUpError(String)
    case signOutError(String)
    case deleteAccountError(String)
    case reauthenticationError(String)
    case userNotFound
    case networkError
    case unknownError

    var description: String {
        switch self {
        case .signInError(let message):
            return "Sign in failed: \(message)"
        case .signUpError(let message):
            return "Sign up failed: \(message)"
        case .signOutError(let message):
            return "Sign out failed: \(message)"
        case .deleteAccountError(let message):
            return "Account deletion failed: \(message)"
        case .reauthenticationError(let message):
            return "Re-authentication failed: \(message)"
        case .userNotFound:
            return "User not found"
        case .networkError:
            return "Network error occurred"
        case .unknownError:
            return "An unknown error occurred"
        }
    }

    // Implementation of Equatable
    static func == (lhs: AuthError, rhs: AuthError) -> Bool {
        switch (lhs, rhs) {
        case (.signInError(let lhsMessage), .signInError(let rhsMessage)):
            return lhsMessage == rhsMessage
        case (.signUpError(let lhsMessage), .signUpError(let rhsMessage)):
            return lhsMessage == rhsMessage
        case (.signOutError(let lhsMessage), .signOutError(let rhsMessage)):
            return lhsMessage == rhsMessage
        case (.deleteAccountError(let lhsMessage), .deleteAccountError(let rhsMessage)):
            return lhsMessage == rhsMessage
        case (.reauthenticationError(let lhsMessage), .reauthenticationError(let rhsMessage)):
            return lhsMessage == rhsMessage
        case (.userNotFound, .userNotFound),
             (.networkError, .networkError),
             (.unknownError, .unknownError):
            return true
        default:
            return false
        }
    }
}

class AuthService: ObservableObject {
    static let shared = AuthService()

    @Published var user: User?
    @Published var userProfile: UserProfile?
    @Published var isAuthenticated = false
    @Published var authError: AuthError?
    @Published var isLoading = false

    private var cancellables = Set<AnyCancellable>()
    private let db = Firestore.firestore()

    // Array to store auth state listeners
    private var authStateListeners: [(User?) -> Void] = []

    private init() {
        // Set up authentication state listener
        Auth.auth().addStateDidChangeListener { [weak self] _, user in
            guard let self = self else { return }

            self.user = user
            self.isAuthenticated = user != nil

            // If user is authenticated, fetch their profile
            if let user = user {
                self.fetchUserProfile(userId: user.uid)
            } else {
                self.userProfile = nil
            }

            // Notify all registered listeners
            self.notifyAuthStateListeners()
        }
    }

    // Fetch user profile from Firestore
    private func fetchUserProfile(userId: String) {
        db.collection("users").document(userId).getDocument { [weak self] snapshot, error in
            guard let self = self, let snapshot = snapshot, error == nil else {
                print("Error fetching user profile: \(error?.localizedDescription ?? "Unknown error")")
                return
            }

            if let userProfile = UserProfile(document: snapshot) {
                DispatchQueue.main.async {
                    self.userProfile = userProfile
                }
            }
        }
    }

    // Method to add an auth state listener
    func addAuthStateListener(_ listener: @escaping (User?) -> Void) {
        // Add the listener to the array
        authStateListeners.append(listener)

        // Immediately call the listener with the current user
        listener(user)
    }

    // Method to notify all listeners of auth state changes
    private func notifyAuthStateListeners() {
        for listener in authStateListeners {
            listener(user)
        }
    }

    func signUp(firstName: String, lastName: String, email: String, password: String) async throws {
        // Validate first and last name
        guard !firstName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty,
              !lastName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            self.authError = .signUpError("First name and last name are required")
            throw AuthError.signUpError("First name and last name are required")
        }

        do {
            isLoading = true
            // Create the user in Firebase Auth
            let authResult = try await Auth.auth().createUser(withEmail: email, password: password)
            let user = authResult.user

            // Create user profile in Firestore
            let userProfile = UserProfile(
                id: user.uid,
                firstName: firstName.trimmingCharacters(in: .whitespacesAndNewlines),
                lastName: lastName.trimmingCharacters(in: .whitespacesAndNewlines),
                email: email
            )

            // Save to Firestore
            try await db.collection("users").document(user.uid).setData(userProfile.toDictionary())

            // Update local state
            self.user = user
            self.userProfile = userProfile
            self.isAuthenticated = true
            self.isLoading = false
        } catch let error as NSError {
            isLoading = false

            let errorMessage: String
            switch error.code {
            case AuthErrorCode.emailAlreadyInUse.rawValue:
                errorMessage = "Email already in use"
            case AuthErrorCode.invalidEmail.rawValue:
                errorMessage = "Invalid email format"
            case AuthErrorCode.weakPassword.rawValue:
                errorMessage = "Password is too weak"
            default:
                errorMessage = error.localizedDescription
            }

            self.authError = .signUpError(errorMessage)
            throw AuthError.signUpError(errorMessage)
        }
    }

    func signIn(email: String, password: String) async throws {
        do {
            isLoading = true
            let authResult = try await Auth.auth().signIn(withEmail: email, password: password)
            self.user = authResult.user
            self.isAuthenticated = true
            self.isLoading = false
        } catch let error as NSError {
            isLoading = false

            let errorMessage: String
            switch error.code {
            case AuthErrorCode.userNotFound.rawValue:
                errorMessage = "User not found"
            case AuthErrorCode.wrongPassword.rawValue:
                errorMessage = "Incorrect password"
            case AuthErrorCode.invalidEmail.rawValue:
                errorMessage = "Invalid email format"
            case AuthErrorCode.userDisabled.rawValue:
                errorMessage = "Account has been disabled"
            case AuthErrorCode.networkError.rawValue:
                errorMessage = "Network error"
            default:
                errorMessage = error.localizedDescription
            }

            self.authError = .signInError(errorMessage)
            throw AuthError.signInError(errorMessage)
        }
    }

    func signOut() throws {
        do {
            try Auth.auth().signOut()
            self.user = nil
            self.isAuthenticated = false
        } catch {
            self.authError = .signOutError(error.localizedDescription)
            throw AuthError.signOutError(error.localizedDescription)
        }
    }

    func resetPassword(for email: String) async throws {
        do {
            try await Auth.auth().sendPasswordReset(withEmail: email)
        } catch {
            throw AuthError.unknownError
        }
    }

    func getCurrentUser() -> User? {
        return Auth.auth().currentUser
    }

    // Re-authenticate user with their password (required for sensitive operations)
    func reauthenticate(with password: String) async throws {
        guard let user = Auth.auth().currentUser, let email = user.email else {
            throw AuthError.userNotFound
        }

        let credential = EmailAuthProvider.credential(withEmail: email, password: password)

        do {
            try await user.reauthenticate(with: credential)
        } catch let error as NSError {
            let errorMessage: String
            switch error.code {
            case AuthErrorCode.wrongPassword.rawValue:
                errorMessage = "Incorrect password"
            case AuthErrorCode.userNotFound.rawValue:
                errorMessage = "User not found"
            case AuthErrorCode.invalidCredential.rawValue:
                errorMessage = "Invalid credentials"
            case AuthErrorCode.userTokenExpired.rawValue:
                errorMessage = "Session expired, please sign in again"
            default:
                errorMessage = error.localizedDescription
            }

            throw AuthError.reauthenticationError(errorMessage)
        }
    }

    // Delete user account and all associated data
    func deleteAccount(password: String) async throws {
        guard let user = Auth.auth().currentUser else {
            throw AuthError.userNotFound
        }

        isLoading = true

        do {
            // Step 1: Re-authenticate user (required by Firebase for sensitive operations)
            try await reauthenticate(with: password)

            // Step 2: Call the Firebase Function to delete user data
            // This approach uses admin privileges on the server side to avoid permission issues
            let functions = Functions.functions(region: "us-central1")

            // Make sure we're using the latest ID token
            try await user.getIDToken(forcingRefresh: true)

            let data: [String: Any] = ["userId": user.uid]

            // Call the deleteUserData function with explicit error handling
            do {
                let result = try await functions.httpsCallable("deleteUserData").call(data)
                print("Called deleteUserData function, result: \(result.data)")

                // Check if we got a partial success response
                if let resultData = result.data as? [String: Any],
                   let success = resultData["success"] as? Bool,
                   let partialSuccess = resultData["partialSuccess"] as? Bool,
                   !success && partialSuccess {

                    // Log the partial success but continue with account deletion
                    let message = resultData["message"] as? String ?? "Some data may not have been fully deleted"
                    let errorMsg = resultData["error"] as? String ?? "Unknown error"
                    print("Partial success: \(message), Error: \(errorMsg)")

                    // We'll continue with account deletion despite the partial data deletion
                }
            } catch let functionError as NSError {
                print("Function error: \(functionError.localizedDescription)")
                if let errorData = functionError.userInfo["FIRFunctionsErrorDetailsKey"] as? [String: Any],
                   let errorMessage = errorData["message"] as? String {
                    print("Function error details: \(errorMessage)")
                    throw AuthError.deleteAccountError(errorMessage)
                } else {
                    throw AuthError.deleteAccountError(functionError.localizedDescription)
                }
            }

            // Step 3: Delete Firebase Auth account
            try await user.delete()

            // Step 4: Clear local state
            self.user = nil
            self.userProfile = nil
            self.isAuthenticated = false
            self.isLoading = false

        } catch let error as AuthError {
            isLoading = false
            self.authError = error
            throw error
        } catch let error as NSError {
            isLoading = false

            let errorMessage: String
            switch error.code {
            case AuthErrorCode.requiresRecentLogin.rawValue:
                errorMessage = "Please sign in again before deleting your account"
            case AuthErrorCode.networkError.rawValue:
                errorMessage = "Network error, please try again"
            default:
                errorMessage = error.localizedDescription
            }

            self.authError = .deleteAccountError(errorMessage)
            throw AuthError.deleteAccountError(errorMessage)
        }
    }
}
