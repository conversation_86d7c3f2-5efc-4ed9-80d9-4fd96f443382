import Foundation
import Firebase
import FirebaseFunctions
import FirebaseAuth

// Constants for Firebase Functions error handling
let FunctionsErrorDomain = "com.firebase.functions"

// Define Firebase Functions error codes
enum FunctionsErrorCode: Int {
    case OK = 0
    case cancelled = 1
    case unknown = 2
    case invalidArgument = 3
    case deadlineExceeded = 4
    case notFound = 5
    case alreadyExists = 6
    case permissionDenied = 7
    case resourceExhausted = 8
    case failedPrecondition = 9
    case aborted = 10
    case outOfRange = 11
    case unimplemented = 12
    case `internal` = 13
    case unavailable = 14
    case dataLoss = 15
    case unauthenticated = 16
}

class FirebaseService {
    static let shared = FirebaseService()
    
    private let maxRetries = 2
    // Flag to ensure Firebase is only initialized once
    private static var isFirebaseInitialized = false
    
    // Function URL - Firebase callable functions should be called using POST
    private let functionBaseURL = "https://us-central1-talk-maxer-v2.cloudfunctions.net/talk_maxer_v2"
    
    // Custom error types for rate limiting
    enum FirebaseServiceError: Error {
        case rateLimitExceeded(requestsMade: Int, limit: Int)
        case authenticationRequired
        case networkError(Error)
        case serverError(Error)
        case invalidResponse
        case unknown(Error)
        
        var localizedDescription: String {
            switch self {
            case .rateLimitExceeded(let requestsMade, let limit):
                return "You have reached the daily limit of \(limit) requests. Please sign in to continue using the service. (\(requestsMade)/\(limit))"
            case .authenticationRequired:
                return "Please sign in to continue using this feature."
            case .networkError(let error):
                return "Network error: \(error.localizedDescription). Please check your connection."
            case .serverError(let error):
                return "Server error: \(error.localizedDescription). Please try again later."
            case .invalidResponse:
                return "Invalid response from the server. Please try again."
            case .unknown(let error):
                return "An error occurred: \(error.localizedDescription)"
            }
        }
    }
    
    private init() {
        // Initialize Firebase exactly once
        FirebaseService.initializeFirebaseIfNeeded()
    }
    
    // Static method to initialize Firebase
    private static func initializeFirebaseIfNeeded() {
        if !isFirebaseInitialized {
            if FirebaseApp.app() == nil {
                FirebaseApp.configure()
                print("Firebase initialized")
            } else {
                print("Firebase already initialized")
            }
            isFirebaseInitialized = true
        }
    }
    
    // MARK: - API Call using Firebase SDK
    
    func callGeminiAPI(prompt: String) async throws -> String {
        print("Making API call to Gemini via Firebase with prompt length: \(prompt.count)")
        
        var lastError: Error? = nil
        
        // Try up to maxRetries + 1 times (initial attempt + retries)
        for attempt in 0...maxRetries {
            do {
                // Add delay before retries
                if attempt > 0 {
                    let delaySeconds = Double(attempt * 3) // 3, 6, 9 seconds
                    print("Attempt \(attempt+1)/\(maxRetries+1): Waiting \(delaySeconds) seconds before retry...")
                    try await Task.sleep(nanoseconds: UInt64(delaySeconds * 1_000_000_000))
                }
                
                print("Attempt \(attempt+1)/\(maxRetries+1): Sending request")
                
                // Create a fresh Functions instance each time to avoid session reuse issues
                let functions = Functions.functions(region: "us-central1")
                
                // Prepare data - just a simple dictionary with the prompt
                let data: [String: Any] = ["prompt": prompt]
                
                // Make the API call with the Firebase SDK
                let result = try await functions.httpsCallable("talk_maxer_v2").call(data)
                
                // Parse the response
                guard let response = result.data as? [String: Any],
                      let text = response["text"] as? String else {
                    throw FirebaseServiceError.invalidResponse
                }
                
                print("Gemini API call successful - received response of length: \(text.count)")
                return text
                
            } catch {
                lastError = error
                print("Attempt \(attempt+1)/\(maxRetries+1) failed: \(error.localizedDescription)")
                
                if let functionsError = error as? NSError, functionsError.domain == FunctionsErrorDomain {
                    // Handle Firebase Functions specific errors
                    let errorCode = FunctionsErrorCode(rawValue: functionsError.code) ?? .internal
                    
                    switch errorCode {
                    case .resourceExhausted:
                        // This is a rate limit error
                        if let details = functionsError.userInfo["details"] as? [String: Any],
                           let count = details["count"] as? Int,
                           let limit = details["limit"] as? Int,
                           let requiresAuth = details["requiresAuth"] as? Bool, requiresAuth {
                            // Don't retry rate limit errors
                            throw FirebaseServiceError.rateLimitExceeded(requestsMade: count, limit: limit)
                        } else {
                            // Other resource exhausted errors
                            throw FirebaseServiceError.serverError(error)
                        }
                    case .unauthenticated, .permissionDenied:
                        // Authentication errors
                        throw FirebaseServiceError.authenticationRequired
                    case .unavailable, .deadlineExceeded:
                        // Network/timeout errors - can be retried
                        if attempt >= maxRetries {
                            throw FirebaseServiceError.networkError(error)
                        }
                        continue
                    default:
                        // For other errors, only retry if we haven't exceeded max attempts
                        if attempt >= maxRetries {
                            throw FirebaseServiceError.serverError(error)
                        }
                    }
                } else if let nsError = error as? NSError {
                    print("Error details - Domain: \(nsError.domain), Code: \(nsError.code)")
                    
                    // Convert network errors 
                    if nsError.domain == NSURLErrorDomain {
                        if attempt >= maxRetries {
                            throw FirebaseServiceError.networkError(error)
                        }
                        continue
                    }
                    
                    // If we've reached max retries, throw the appropriate error
                    if attempt >= maxRetries {
                        throw FirebaseServiceError.unknown(error)
                    }
                }
                
                // Continue to next retry if not explicitly handled above and we haven't reached max retries
                if attempt < maxRetries {
                    continue
                }
            }
        }
        
        // If we get here, all attempts failed
        print("All \(maxRetries+1) attempts failed")
        
        if let error = lastError {
            throw FirebaseServiceError.unknown(error)
        } else {
            throw FirebaseServiceError.unknown(NSError(
                domain: "FirebaseService", 
                code: 1000, 
                userInfo: [NSLocalizedDescriptionKey: "Failed to call Gemini API after multiple attempts"]
            ))
        }
    }
} 
