import Foundation

class VocabularyService {
    static let shared = VocabularyService()

    private let vocabularyKey = "dailyVocabularyWords"
    private let lastFetchDateKey = "lastVocabularyFetchDate"

    private init() {}

    // Check if we need to fetch new words
    func shouldFetchNewWords(forceRefresh: Bool = false) -> Bool {
        // If force refresh is requested, always return true
        if forceRefresh {
            return true
        }

        if let lastFetchDate = UserDefaults.standard.object(forKey: lastFetchDateKey) as? Date {
            // Check if the last fetch was on a different day
            return !Calendar.current.isDate(lastFetchDate, inSameDayAs: Date())
        }
        return true // No previous fetch date, so fetch new words
    }

    // Save vocabulary words to UserDefaults
    func saveVocabularyWords(_ words: [VocabularyCard]) {
        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(words)
            UserDefaults.standard.set(data, forKey: vocabularyKey)
            UserDefaults.standard.set(Date(), forKey: lastFetchDateKey)
            UserDefaults.standard.synchronize()
        } catch {
            print("Error saving vocabulary words: \(error.localizedDescription)")
        }
    }

    // Retrieve vocabulary words from UserDefaults
    func getVocabularyWords() -> [VocabularyCard]? {
        guard let data = UserDefaults.standard.data(forKey: vocabularyKey) else {
            return nil
        }

        do {
            let decoder = JSONDecoder()
            let words = try decoder.decode([VocabularyCard].self, from: data)
            return words
        } catch {
            print("Error retrieving vocabulary words: \(error.localizedDescription)")
            return nil
        }
    }

    // Clear stored vocabulary words (for testing)
    func clearVocabularyWords() {
        print("Clearing stored vocabulary words from UserDefaults")
        UserDefaults.standard.removeObject(forKey: vocabularyKey)
        UserDefaults.standard.removeObject(forKey: lastFetchDateKey)
        UserDefaults.standard.synchronize()

        // Verify the data was cleared
        if UserDefaults.standard.object(forKey: vocabularyKey) == nil &&
           UserDefaults.standard.object(forKey: lastFetchDateKey) == nil {
            print("Successfully cleared vocabulary data")
        } else {
            print("Warning: Failed to clear vocabulary data from UserDefaults")
        }
    }
}
