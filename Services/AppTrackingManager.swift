import Foundation
import AppTrackingTransparency
import UIKit

class AppTrackingManager {
    static let shared = AppTrackingManager()
    
    private init() {}
    
    func requestTrackingAuthorization(completion: @escaping (ATTrackingManager.AuthorizationStatus) -> Void) {
        // On iOS 14 and above, we need to request permission
        if #available(iOS 14, *) {
            ATTrackingManager.requestTrackingAuthorization { status in
                DispatchQueue.main.async {
                    completion(status)
                }
            }
        } else {
            // For iOS versions below 14, tracking is enabled by default
            completion(.authorized)
        }
    }
    
    func getTrackingAuthorizationStatus() -> ATTrackingManager.AuthorizationStatus {
        if #available(iOS 14, *) {
            return ATTrackingManager.trackingAuthorizationStatus
        } else {
            // For iOS versions below 14, tracking is enabled by default
            return .authorized
        }
    }
} 