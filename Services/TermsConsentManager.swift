import Foundation
import SwiftUI

class TermsConsentManager: ObservableObject {
    static let shared = TermsConsentManager()
    
    private let termsAcceptedKey = "hasAcceptedTermsOfUse"
    
    @Published var hasAcceptedTerms: Bool
    
    private init() {
        // Check if user has accepted terms
        self.hasAcceptedTerms = UserDefaults.standard.bool(forKey: termsAcceptedKey)
    }
    
    func acceptTerms() {
        hasAcceptedTerms = true
        UserDefaults.standard.set(true, forKey: termsAcceptedKey)
        UserDefaults.standard.synchronize()
    }
    
    func resetTermsConsent() {
        // For testing purposes only
        hasAcceptedTerms = false
        UserDefaults.standard.set(false, forKey: termsAcceptedKey)
        UserDefaults.standard.synchronize()
    }
} 