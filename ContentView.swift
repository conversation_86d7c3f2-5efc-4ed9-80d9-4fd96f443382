//
//  ContentView.swift
//  Talk_Maxer
//
//  Created by <PERSON><PERSON><PERSON> on 2/22/25.
//

import SwiftUI
import Charts
import AVFoundation
import RevenueCat

struct ContentView: View {
    @EnvironmentObject private var viewModel: CommunicationViewModel
    @State private var selectedTab = 0
    @State private var showingPaywall = false
    @ObservedObject private var subscriptionService = SubscriptionService.shared
    // Removed isAnimating state to reduce unnecessary animations
    @Environment(\.colorScheme) private var colorScheme

    // Dynamic colors based on color scheme
    private var textColor: Color {
        colorScheme == .dark ? .white : .black
    }

    private var subtleTextColor: Color {
        colorScheme == .dark ? .white.opacity(0.7) : .black.opacity(0.7)
    }

    private var gradientColors: [Color] {
        colorScheme == .dark ?
            [Color.blue.opacity(0.8), Color.purple.opacity(0.6), Color.indigo.opacity(0.4)] :
            [Color.blue.opacity(0.3), Color.purple.opacity(0.2), Color.indigo.opacity(0.1)]
    }

    private var glassStrokeGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: colorScheme == .dark ?
                [Color.white.opacity(0.5), Color.white.opacity(0.1)] :
                [Color.white.opacity(0.8), Color.black.opacity(0.1)]
            ),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    var body: some View {
        ZStack {
            // Simplified static background
            Color(colorScheme == .dark ? .black : .white)
                .overlay(
                    LinearGradient(
                        gradient: Gradient(colors: gradientColors),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                    .blur(radius: 30)
                    .opacity(0.5)
                )
                .ignoresSafeArea()

            // Removed floating elements to improve performance

            // Main content
            NavigationStack {
                ZStack {
                    // Main content view
                    ScrollView {
                        VStack(spacing: 24) {
                            // Header section with glass effect
                            welcomeHeaderView
                                .padding(.horizontal)
                                .padding(.top, 16)

                            // Communication Map section starts here

                            // Communication Stages
                            communicationStagesView

                            // Add padding to ensure content is not covered by tab bar
                            Spacer()
                                .frame(height: 90)
                        }
                    }
                    .scrollContentBackground(.hidden)

                    // No need for a tab bar here anymore as we're using TabView
                }
                .navigationTitle("Talk Maxer")
                .navigationBarTitleDisplayMode(.large)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        profileButton
                    }
                }
                .sheet(isPresented: $showingPaywall) {
                    PaywallView()
                        .onDisappear {
                            // Refresh subscription status when paywall is dismissed
                            subscriptionService.forceCheckSubscriptionStatus()
                        }
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("SubscriptionStatusChanged"))) { _ in
            // Refresh the view when subscription status changes
            // This will update the locked/unlocked state of stages
        }
    }

    // Welcome header with personalized message
    private var welcomeHeaderView: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Hello!")
                .font(.title3)
                .foregroundColor(subtleTextColor)

            Text("Ready to improve your communication?")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(textColor)
                .lineLimit(2)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(.ultraThinMaterial)
                .opacity(0.9)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 24)
                .stroke(glassStrokeGradient, lineWidth: 1)
        )
        .shadow(color: Color.black.opacity(0.07), radius: 6, x: 0, y: 3)
    }

    // Communication Stages View
    private var communicationStagesView: some View {
        VStack(alignment: .leading, spacing: 16) {
            ForEach(viewModel.communicationStages) { stage in
                communicationStageCard(stage: stage)
            }
        }
        .padding(.horizontal, 16)
    }

    // Individual Stage Card
    private func communicationStageCard(stage: CommunicationStage) -> some View {
        let isStageAvailable = subscriptionService.isStageAvailable(stageNumber: stage.number)

        return VStack(alignment: .leading, spacing: 12) {
            // Stage header
            HStack {
                Text("Stage \(stage.number):")
                    .font(.headline)
                    .foregroundColor(.blue)

                Text(stage.title)
                    .font(.headline)
                    .foregroundColor(textColor)

                Spacer()

                // Lock icon for premium stages
                if !isStageAvailable {
                    Image(systemName: "lock.fill")
                        .foregroundColor(.orange)
                        .font(.system(size: 16))
                }
            }

            // Goals
            Text("Goals:")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(textColor)

            Text(stage.goals)
                .font(.subheadline)
                .foregroundColor(subtleTextColor)
                .lineLimit(3)

            // Activities or Upgrade button
            if isStageAvailable {
                if !stage.activities.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        ForEach(stage.activities) { activity in
                            activityRow(activity: activity)
                        }
                    }
                    .padding(.top, 4)
                }
            } else {
                // Upgrade button for premium stages
                Button(action: {
                    showingPaywall = true
                }) {
                    HStack {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                        Text("Unlock Premium")
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.system(size: 14))
                            .foregroundColor(.white.opacity(0.8))
                    }
                    .padding(12)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [Color.blue, Color.purple]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(10)
                }
                .padding(.top, 8)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .opacity(0.9)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.gray.opacity(0.2), lineWidth: 0.5)
        )
    }

    // Activity Row
    private func activityRow(activity: StageActivity) -> some View {
        NavigationLink(destination: destinationView(for: activity)) {
            HStack {
                // Icon based on activity type
                Image(systemName: activityIcon(for: activity.type))
                    .font(.system(size: 18))
                    .foregroundColor(.blue)
                    .frame(width: 24, height: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(activity.title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(textColor)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    Text(activity.description)
                        .multilineTextAlignment(.leading)
                        .font(.caption)
                        .foregroundColor(subtleTextColor)
                        .lineLimit(2)
                        .frame(alignment: .leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }

                Spacer()

                // Navigation arrow
                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(.gray)
            }
            .padding(8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray6).opacity(0.5))
            )
        }
    }

    // Helper function to get icon for activity type
    private func activityIcon(for type: ActivityType) -> String {
        switch type {
        case .audio:
            return "mic.fill"
        case .vocabulary:
            return "textformat.abc"
        case .chat:
            return "bubble.left.and.bubble.right.fill"
        case .fillerDetector:
            return "xmark.circle" // Changed icon for removed feature
        case .video:
            return "video.fill"
        case .lesson:
            return "book.fill"
        case .recording:
            return "waveform.and.mic"
        case .article:
            return "doc.text.fill"
        case .custom:
            return "map.fill" // Icon for custom interactive content
        @unknown default:
            return "questionmark.circle"
        }
    }

    // Helper function to determine destination view based on activity type
    @ViewBuilder
    private func destinationView(for activity: StageActivity) -> some View {
            switch activity.type {
            case .audio:
                // Audio player for audio content
                if !activity.content.isEmpty {
                    VStack {
                        ReusableAudioPlayerView(
                            audioFileName: activity.content,
                            title: activity.title,
                            subtitle: activity.description,
                            accentColor: .blue
                        )

                        // Debug buttons - only visible in debug builds
                        #if DEBUG
                        HStack {
                            NavigationLink(destination: AudioDebugView()) {
                                Text("Audio Debug")
                                    .font(.caption)
                                    .padding(8)
                                    .background(Color.gray.opacity(0.2))
                                    .cornerRadius(8)
                            }

                            NavigationLink(destination: ReusableAudioPlayerView(
                                audioFileName: activity.content,
                                title: "\(activity.title) (Compact)",
                                accentColor: .purple,
                                compactMode: true
                            )) {
                                Text("Compact Player")
                                    .font(.caption)
                                    .padding(8)
                                    .background(Color.purple.opacity(0.2))
                                    .cornerRadius(8)
                            }

                            NavigationLink(destination: AudioDebugInfoView()) {
                                Text("Debug Info")
                                    .font(.caption)
                                    .padding(8)
                                    .background(Color.red.opacity(0.2))
                                    .cornerRadius(8)
                            }
                        }
                        .padding(.bottom)
                        #endif
                    }
                } else {
                    Text("No audio content available")
                        .foregroundColor(.gray)
                        .padding()
                }

            case .recording:
                // Clarity exercise with recording functionality
                ClarityExerciseView(
                    exerciseTitle: activity.title,
                    instructions: activity.description
                )
            case .vocabulary:
                // Use the VocabularyCardListView without wrapping it in another NavigationView
                VocabularyCardListView()
                    .navigationBarTitleDisplayMode(.inline)
            case .fillerDetector:
                // Redirect to video rephrasing as an alternative to filler uuword detection
                VideoRephraseView()
                    .navigationBarTitle("Video Rephrasing", displayMode: .inline)
            case .lesson:
                // Find matching lesson from existing lessons
                let matchingLesson = findMatchingLesson(title: activity.title)
                if let lesson = matchingLesson {
                    TextChatView(lesson: lesson, viewModel: viewModel)
                } else {
                    Text("Lesson not found")
                }
            case .chat:
                // Check if this is the Sentence Rewriting activity from Stage 4
                if activity.title == "Sentence Rewriting" {
                    SentenceRewriteView()
                } else {
                    ChatRewriteView()
                }
            case .article:
                // Handle article-related activities
                if activity.title == "Article Rephrasing" {
                    ArticleRephraseWithRecordingView()
                } else if activity.title == "Reading Article" {
                    ArticleRephraseViewWithDifficulty()
                } else {
                    ArticleRephraseView()
                }
            case .video:
                // Handle video-related activities
                VideoRephraseView()
            case .custom:
                // Handle custom interactive content
                customContentView(for: activity.content)
            @unknown default:
                // Handle any future cases that might be added to the enum
                Text("This activity type is not supported yet")
                    .foregroundColor(.gray)
                    .padding()
            }
    }

    // Helper function to get the view for custom content
    @ViewBuilder
    private func customContentView(for content: String) -> some View {
        switch content {
        case "EffectiveCommunicationMap":
            EffectiveCommunicationMapView()
        case "StoryElements":
            StoryElementsLauncher()
        default:
            Text("Custom content not available")
                .foregroundColor(.gray)
                .padding()
        }
    }

    // Helper function to find matching lesson from existing lessons
    private func findMatchingLesson(title: String) -> LessonPlan? {
        for category in viewModel.lessonCategories {
            if let lesson = category.lessons.first(where: { $0.title == title }) {
                return lesson
            }
        }
        return nil
    }

    // Profile button with glass effect
    private var profileButton: some View {
        NavigationLink {
            SettingsView(viewModel: viewModel)
        } label: {
            ZStack {
                Circle()
                    .fill(.ultraThinMaterial)
                    .frame(width: 40, height: 40)

                Image(systemName: "gearshape")
                    .foregroundColor(textColor)
                    .font(.system(size: 16, weight: .medium))
            }
            .overlay(
                Circle()
                    .stroke(glassStrokeGradient, lineWidth: 1)
            )
        }
    }
}

// Removed unused namespace wrapper

// Helper to create colors from hex
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// Removed unused namespace wrapper

#Preview {
    ContentView()
        .environmentObject(CommunicationViewModel())
}
