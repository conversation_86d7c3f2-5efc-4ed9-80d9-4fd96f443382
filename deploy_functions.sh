#!/bin/bash

# <PERSON>ript to deploy Firebase Functions for Talk Maxer

echo "Deploying Firebase Functions..."

# Change to the Firebase Functions directory
cd Firebase/Functions

# Deploy only the functions
firebase deploy --only functions

echo "Deployment complete!"
echo "The account deletion functionality should now work correctly."
echo "Note: You may need to wait a few minutes for the deployment to fully propagate."
