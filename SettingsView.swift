//
//  SettingsView.swift
//  Talk_Maxer
//
//  Created by <PERSON><PERSON><PERSON> on 2/22/25.
//

import SwiftUI
import WebKit
import UIKit
import FirebaseAuth

// Constants for Firebase Auth error handling
let AuthErrorDomain = "FIRAuthErrorDomain"

struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var viewModel: CommunicationViewModel
    @ObservedObject private var subscriptionService = SubscriptionService.shared
    @StateObject private var authService = AuthService.shared
    @State private var showingResetConfirmation = false
    @State private var showingClearAllChatsConfirmation = false
    @State private var showingLessonSelection = false
    @State private var showingTermsOfUse = false
    @State private var showingPrivacyPolicy = false
    @State private var showingContactForm = false
    @State private var showingPaywall = false
    @State private var isRestoring = false
    @State private var showRestoreSuccess = false
    @State private var showRestoreError = false
    @State private var restoreErrorMessage = ""
    @State private var showSubscriptionManagementError = false
    @State private var refreshTrigger = UUID()

    // Account deletion states
    @State private var showingDeleteAccountConfirmation = false
    @State private var showingDeleteAccountPasswordPrompt = false
    @State private var deleteAccountPassword = ""
    @State private var isDeleting = false
    @State private var showDeleteAccountError = false
    @State private var deleteAccountErrorMessage = ""

    var body: some View {
        List {
            // New Profile section
            Section {
                NavigationLink {
                    ProfileView()
                } label: {
                    HStack {
                        Image(systemName: "person.circle")
                            .foregroundColor(.blue)
                        Text("Account Profile")
                    }
                }
            } header: {
                Text("Profile")
            }

            Section {
                HStack {
                    Image(systemName: "star.fill")
                        .foregroundColor(.yellow)
                    Text("Premium Access")

                    Spacer()

                    if subscriptionService.isSubscribed {
                        Text("Active")
                            .foregroundColor(.green)
                            .font(.subheadline)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(Color.green.opacity(0.2))
                            )
                    } else {
                        Button("Subscribe") {
                            showingPaywall = true
                        }
                        .foregroundColor(.blue)
                        .font(.subheadline)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 4)
                        .background(
                            Capsule()
                                .fill(Color.blue.opacity(0.1))
                        )
                    }
                }

                if subscriptionService.isSubscribed {
                    Button("Manage Subscription") {
                        openSubscriptionManagement()
                    }
                    .foregroundColor(.red)
                }

                Button("Restore Purchases") {
                    restorePurchases()
                }
                .foregroundColor(.blue)
            } header: {
                Text("Subscription")
            } footer: {
                if subscriptionService.isSubscribed {
                    Text("You can cancel your subscription through your Apple ID settings. Your premium access will remain active until the end of your current billing period.")
                } else {
                    Text("With premium access, unlock all lessons. Only \"At a Grocery Store\" lesson is available without subscription.")
                }
            }

            // For development and testing only
            #if DEBUG
            Section(header: Text("Development Options")) {
                Toggle(isOn: $subscriptionService.isSubscribed) {
                    Text("Debug Toggle Subscription")
                }

                Toggle(isOn: $subscriptionService.willAutoRenew) {
                    Text("Auto-Renewal")
                }
                .onChange(of: subscriptionService.willAutoRenew) { _, _ in
                    subscriptionService.toggleAutoRenewal()
                }

                if subscriptionService.isSubscribed {
                    HStack {
                        Text("Expiration Date:")
                        Spacer()
                        Text(getExpirationDateText())
                            .foregroundStyle(.secondary)
                    }
                    .font(.footnote)
                }

                Button(role: .destructive) {
                    TermsConsentManager.shared.resetTermsConsent()
                } label: {
                    Text("Reset Terms Consent")
                        .foregroundColor(.red)
                }
            }
            #endif

            Section {
                Button {
                    showingTermsOfUse = true
                } label: {
                    HStack {
                        Image(systemName: "doc.text")
                            .foregroundColor(.blue)
                        Text("Terms of Use")
                    }
                }

                Button {
                    showingPrivacyPolicy = true
                } label: {
                    HStack {
                        Image(systemName: "lock.shield")
                            .foregroundColor(.blue)
                        Text("Privacy Policy")
                    }
                }

                Button {
                    showingContactForm = true
                } label: {
                    HStack {
                        Image(systemName: "envelope")
                            .foregroundColor(.blue)
                        Text("Contact Us")
                    }
                }
            } header: {
                Text("Legal")
            }
            Section {
                Button(role: .destructive) {
                    showingResetConfirmation = true
                } label: {
                    HStack {
                        Image(systemName: "arrow.counterclockwise")
                            .foregroundColor(.red)
                        Text("Reset Data")
                            .foregroundColor(.red)
                    }
                }
            } header: {
                Text("Data Management")
            } footer: {
                Text("Resetting will clear all your scores and communication data.")
            }

            Section {
                Button {
                    showingClearAllChatsConfirmation = true
                } label: {
                    HStack {
                        Image(systemName: "ellipsis.message")
                            .foregroundColor(.red)
                        Text("Clear All Chat History")
                            .foregroundColor(.red)
                    }
                }

                Button {
                    showingLessonSelection = true
                } label: {
                    HStack {
                        Image(systemName: "square.and.pencil")
                            .foregroundColor(.blue)
                        Text("Clear Chat for Specific Lesson")
                            .foregroundColor(.primary)
                    }
                }
            } header: {
                Text("Chat History")
            } footer: {
                Text("You can clear all chat history or select a specific lesson.")
            }

            // Danger Zone section with account deletion
            Section {
                Button(role: .destructive) {
                    showingDeleteAccountConfirmation = true
                } label: {
                    HStack {
                        Image(systemName: "person.crop.circle.badge.xmark")
                            .foregroundColor(.red)
                        Text("Delete Account")
                            .foregroundColor(.red)
                    }
                }
            } header: {
                Text("Danger Zone")
            } footer: {
                Text("Deleting your account will permanently remove all your data, including posts, comments, and profile information. This action cannot be undone.")
            }
        }
        .id(refreshTrigger)
        .navigationTitle("Settings")
        .navigationBarTitleDisplayMode(.large)
        .alert("Reset Data?", isPresented: $showingResetConfirmation) {
            Button("Cancel", role: .cancel) {}
            Button("Reset", role: .destructive) {
                viewModel.clearAllData()
            }
        } message: {
            Text("This will clear all your scores and communication data.")
        }
        .alert("Clear All Chat History?", isPresented: $showingClearAllChatsConfirmation) {
            Button("Cancel", role: .cancel) {}
            Button("Clear", role: .destructive) {
                viewModel.clearAllChatHistory()
            }
        } message: {
            Text("This will delete all saved chat messages for all lessons. This action cannot be undone.")
        }
        .sheet(isPresented: $showingLessonSelection) {
            LessonSelectionView(viewModel: viewModel)
                .presentationDetents([.medium, .large])
        }
        .sheet(isPresented: $showingTermsOfUse) {
            TermsOfUseView(urlString: "https://www.rashedslab.com/terms-of-use")
        }
        .sheet(isPresented: $showingPrivacyPolicy) {
            WebViewSheet(title: "Privacy Policy", urlString: "https://www.rashedslab.com/privacy-policy")
        }
        .sheet(isPresented: $showingContactForm) {
            WebViewSheet(title: "Contact Us", urlString: "https://forms.gle/cKoZ8qMHrWwz6uGk8")
        }
        .sheet(isPresented: $showingPaywall) {
            PaywallView()
                .onDisappear {
                    subscriptionService.forceCheckSubscriptionStatus()
                    refreshTrigger = UUID()
                }
        }
        .onAppear {
            subscriptionService.forceCheckSubscriptionStatus()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("SubscriptionStatusChanged"))) { _ in
            print("SettingsView received subscription status change notification")
            refreshTrigger = UUID()
        }
        .alert("Restore Successful", isPresented: $showRestoreSuccess) {
            Button("OK") { }
        } message: {
            Text("Your purchases have been restored successfully.")
        }
        .alert("Restore Failed", isPresented: $showRestoreError) {
            Button("OK") { }
        } message: {
            Text(restoreErrorMessage)
        }
        .alert("Cannot Open Settings", isPresented: $showSubscriptionManagementError) {
            Button("OK", role: .cancel) { }
        } message: {
            Text("To manage your subscription, go to Settings > Apple ID > Subscriptions on your device.")
        }
        // Delete account confirmation
        .alert("Delete Account?", isPresented: $showingDeleteAccountConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                showingDeleteAccountPasswordPrompt = true
            }
        } message: {
            Text("This will permanently delete your account and all associated data. This action cannot be undone.")
        }
        // Password confirmation for account deletion
        .alert("Confirm Password", isPresented: $showingDeleteAccountPasswordPrompt) {
            SecureField("Password", text: $deleteAccountPassword)

            Button("Cancel", role: .cancel) {
                deleteAccountPassword = ""
            }

            Button("Delete Account", role: .destructive) {
                deleteAccount()
            }
        } message: {
            Text("Please enter your password to confirm account deletion.")
        }
        // Error alert for account deletion
        .alert("Account Deletion Failed", isPresented: $showDeleteAccountError) {
            Button("OK") { }
        } message: {
            Text(deleteAccountErrorMessage)
        }
        .overlay {
            if isRestoring || isDeleting {
                Color.black.opacity(0.2)
                    .ignoresSafeArea()
                    .overlay {
                        VStack {
                            ProgressView()
                                .padding()
                            Text(isDeleting ? "Deleting account..." : "Restoring purchases...")
                                .foregroundStyle(.secondary)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color(.systemBackground))
                                .shadow(radius: 5)
                        )
                    }
            }
        }
    }

    private func restorePurchases() {
        isRestoring = true

        subscriptionService.restorePurchases { success in
            isRestoring = false

            if success {
                subscriptionService.forceCheckSubscriptionStatus()
                showRestoreSuccess = true
            } else {
                restoreErrorMessage = "No purchases found to restore. Make sure you're using the same Apple ID that was used for the original purchase."
                showRestoreError = true
            }
        }
    }

    private func openSubscriptionManagement() {
        // Open Apple's subscription management page
        guard let url = URL(string: "itms-apps://apps.apple.com/account/subscriptions") else {
            showSubscriptionManagementError = true
            return
        }

        if UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url)
        } else {
            // Fallback for simulator or if the URL can't be opened
            print("Cannot open subscription management URL")
            showSubscriptionManagementError = true
        }
    }

    private func getExpirationDateText() -> String {
        guard let expirationDate = subscriptionService.expirationDate else {
            return "Not available"
        }

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short

        // Calculate days remaining
        let days = Calendar.current.dateComponents([.day], from: Date(), to: expirationDate).day ?? 0

        if days < 0 {
            return "Expired"
        } else if days == 0 {
            return "Today at \(formatter.string(from: expirationDate))"
        } else {
            return "\(formatter.string(from: expirationDate)) (\(days) days)"
        }
    }

    // Handle account deletion
    private func deleteAccount() {
        guard !deleteAccountPassword.isEmpty else {
            deleteAccountErrorMessage = "Password cannot be empty"
            showDeleteAccountError = true
            return
        }

        isDeleting = true

        Task {
            do {
                print("Starting account deletion process...")

                // Get the current user
                guard let user = Auth.auth().currentUser, let email = user.email else {
                    throw AuthError.userNotFound
                }

                // Create credential for re-authentication
                let credential = EmailAuthProvider.credential(withEmail: email, password: deleteAccountPassword)

                // Re-authenticate before deleting
                print("Re-authenticating user...")
                try await user.reauthenticate(with: credential)
                print("Re-authentication successful")

                // Now delete the user directly
                print("Deleting user account...")
                try await user.delete()

                print("Account deletion successful")
                // Account deletion successful - user will be automatically signed out
                // and redirected to the authentication screen by the auth state listener

                // Clear local state
                DispatchQueue.main.async {
                    self.isDeleting = false
                    self.deleteAccountPassword = ""
                }

            } catch let error as NSError {
                DispatchQueue.main.async {
                    self.isDeleting = false

                    // Handle specific Firebase Auth errors
                    if error.domain == AuthErrorDomain {
                        switch error.code {
                        case AuthErrorCode.wrongPassword.rawValue:
                            self.deleteAccountErrorMessage = "Incorrect password. Please try again."
                        case AuthErrorCode.userNotFound.rawValue:
                            self.deleteAccountErrorMessage = "User account not found."
                        case AuthErrorCode.invalidCredential.rawValue:
                            self.deleteAccountErrorMessage = "Invalid credentials. Please try again."
                        case AuthErrorCode.requiresRecentLogin.rawValue:
                            self.deleteAccountErrorMessage = "This operation requires recent authentication. Please try again."
                        default:
                            self.deleteAccountErrorMessage = "Authentication error: \(error.localizedDescription)"
                        }
                    } else {
                        self.deleteAccountErrorMessage = "An error occurred: \(error.localizedDescription)"
                    }

                    print("Error during account deletion: \(error.localizedDescription)")
                    self.showDeleteAccountError = true
                    self.deleteAccountPassword = ""
                }
            }
        }
    }
}

struct LessonSelectionView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var viewModel: CommunicationViewModel
    @State private var selectedLesson: LessonPlan?
    @State private var showingClearConfirmation = false

    var body: some View {
        NavigationStack {
            List {
                ForEach(viewModel.lessonCategories, id: \.title) { category in
                    Section(header: Text(category.title)) {
                        ForEach(category.lessons) { lesson in
                            Button {
                                selectedLesson = lesson
                                showingClearConfirmation = true
                            } label: {
                                Text(lesson.title)
                                    .foregroundColor(.primary)
                            }
                        }
                    }
                }
            }
            .navigationTitle("Select Lesson")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .alert("Clear Chat History", isPresented: $showingClearConfirmation, presenting: selectedLesson) { lesson in
                Button("Cancel", role: .cancel) { }
                Button("Clear", role: .destructive) {
                    viewModel.clearChatHistory(for: lesson.id.uuidString)
                    dismiss()
                }
            } message: { lesson in
                Text("This will delete all saved messages for \(lesson.title). This action cannot be undone.")
            }
        }
    }
}

struct WebView: UIViewRepresentable {
    let url: URL

    func makeUIView(context: Context) -> WKWebView {
        let webView = WKWebView()
        webView.load(URLRequest(url: url))
        return webView
    }

    func updateUIView(_ uiView: WKWebView, context: Context) {}
}

struct WebViewSheet: View {
    @Environment(\.dismiss) private var dismiss
    let title: String
    let urlString: String

    var body: some View {
        NavigationStack {
            WebView(url: URL(string: urlString)!)
                .navigationTitle(title)
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Done") {
                            dismiss()
                        }
                    }
                }
        }
    }
}

struct TermsOfUseView: View {
    let urlString: String

    var body: some View {
        WebViewSheet(title: "Terms of Use", urlString: urlString)
    }
}

#Preview {
    NavigationStack {
        SettingsView(viewModel: CommunicationViewModel())
    }
}
