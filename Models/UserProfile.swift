import Foundation
import FirebaseFirestore

struct UserProfile: Identifiable, Codable {
    var id: String // User UID from Firebase Auth
    var firstName: String
    var lastName: String
    var email: String
    var createdAt: Date
    
    // Computed property for full name
    var fullName: String {
        return "\(firstName) \(lastName)"
    }
    
    // Default initializer
    init(id: String, firstName: String, lastName: String, email: String, createdAt: Date = Date()) {
        self.id = id
        self.firstName = firstName
        self.lastName = lastName
        self.email = email
        self.createdAt = createdAt
    }
    
    // Initialize from Firestore document
    init?(document: DocumentSnapshot) {
        guard let data = document.data(),
              let firstName = data["firstName"] as? String,
              let lastName = data["lastName"] as? String,
              let email = data["email"] as? String else {
            return nil
        }
        
        let createdAt = (data["createdAt"] as? Timestamp)?.dateValue() ?? Date()
        
        self.id = document.documentID
        self.firstName = firstName
        self.lastName = lastName
        self.email = email
        self.createdAt = createdAt
    }
    
    // Convert to dictionary for Firestore
    func toDictionary() -> [String: Any] {
        return [
            "firstName": firstName,
            "lastName": lastName,
            "email": email,
            "createdAt": FieldValue.serverTimestamp()
        ]
    }
}
