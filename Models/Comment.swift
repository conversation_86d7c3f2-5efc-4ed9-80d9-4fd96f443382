import Foundation
import FirebaseFirestore

struct Comment: Identifiable, Codable, Equatable {
    static func == (lhs: Comment, rhs: Comment) -> Bool {
        // If both have IDs, compare them
        if let lhsId = lhs.id, let rhsId = rhs.id {
            return lhsId == rhsId
        }

        // Otherwise compare all relevant fields
        return lhs.postId == rhs.postId &&
               lhs.authorId == rhs.authorId &&
               lhs.content == rhs.content &&
               lhs.timestamp == rhs.timestamp
    }
    var id: String?
    let postId: String
    let authorId: String
    let authorEmail: String
    let authorName: String // Full name of the author
    let content: String
    let timestamp: Date

    // Computed property for displaying time since comment
    var timeAgo: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .full
        return formatter.localizedString(for: timestamp, relativeTo: Date())
    }

    // Default initializer
    init(id: String? = nil,
         postId: String,
         authorId: String,
         authorEmail: String,
         authorName: String,
         content: String,
         timestamp: Date = Date()) {
        self.id = id
        self.postId = postId
        self.authorId = authorId
        self.authorEmail = authorEmail
        self.authorName = authorName
        self.content = content
        self.timestamp = timestamp
    }

    // Initialize from Firestore QueryDocumentSnapshot
    init?(document: QueryDocumentSnapshot) {
        let data = document.data()

        guard let postId = data["postId"] as? String,
              let authorId = data["authorId"] as? String,
              let authorEmail = data["authorEmail"] as? String,
              let content = data["content"] as? String else {
            return nil
        }

        // Get author name or use email as fallback
        let authorName = (data["authorName"] as? String) ?? "Unknown User"

        let timestamp = (data["timestamp"] as? Timestamp)?.dateValue() ?? Date()

        self.id = document.documentID
        self.postId = postId
        self.authorId = authorId
        self.authorEmail = authorEmail
        self.authorName = authorName
        self.content = content
        self.timestamp = timestamp
    }

    // Initialize from Firestore DocumentSnapshot
    init?(document: DocumentSnapshot) {
        guard let data = document.data() else { return nil }

        guard let postId = data["postId"] as? String,
              let authorId = data["authorId"] as? String,
              let authorEmail = data["authorEmail"] as? String,
              let content = data["content"] as? String else {
            return nil
        }

        // Get author name or use email as fallback
        let authorName = (data["authorName"] as? String) ?? "Unknown User"

        let timestamp = (data["timestamp"] as? Timestamp)?.dateValue() ?? Date()

        self.id = document.documentID
        self.postId = postId
        self.authorId = authorId
        self.authorEmail = authorEmail
        self.authorName = authorName
        self.content = content
        self.timestamp = timestamp
    }
}
