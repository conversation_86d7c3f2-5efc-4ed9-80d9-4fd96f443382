import Foundation
import FirebaseFirestore

struct Post: Identifiable, Codable, Equatable {
    static func == (lhs: Post, rhs: Post) -> Bool {
        // If both have IDs, compare them
        if let lhsId = lhs.id, let rhsId = rhs.id {
            return lhsId == rhsId
        }

        // Otherwise compare all relevant fields
        return lhs.authorId == rhs.authorId &&
               lhs.content == rhs.content &&
               lhs.timestamp == rhs.timestamp
    }
    var id: String?
    let authorId: String
    let authorEmail: String
    let authorName: String // Full name of the author
    let content: String
    let timestamp: Date
    var likeCount: Int
    var dislikeCount: Int
    var commentCount: Int
    var likedBy: [String]
    var dislikedBy: [String]

    // Computed property for displaying time since post
    var timeAgo: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .full
        return formatter.localizedString(for: timestamp, relativeTo: Date())
    }

    // Default initializer
    init(id: String? = nil,
         authorId: String,
         authorEmail: String,
         authorName: String,
         content: String,
         timestamp: Date = Date(),
         likeCount: Int = 0,
         dislikeCount: Int = 0,
         commentCount: Int = 0,
         likedBy: [String] = [],
         dislikedBy: [String] = []) {
        self.id = id
        self.authorId = authorId
        self.authorEmail = authorEmail
        self.authorName = authorName
        self.content = content
        self.timestamp = timestamp
        self.likeCount = likeCount
        self.dislikeCount = dislikeCount
        self.commentCount = commentCount
        self.likedBy = likedBy
        self.dislikedBy = dislikedBy
    }

    // Initialize from Firestore QueryDocumentSnapshot
    init?(document: QueryDocumentSnapshot) {
        let data = document.data()

        guard let authorId = data["authorId"] as? String,
              let authorEmail = data["authorEmail"] as? String,
              let content = data["content"] as? String else {
            return nil
        }

        // Get author name or use email as fallback
        let authorName = (data["authorName"] as? String) ?? "Unknown User"

        let timestamp = (data["timestamp"] as? Timestamp)?.dateValue() ?? Date()
        let likeCount = (data["likeCount"] as? Int) ?? 0
        let dislikeCount = (data["dislikeCount"] as? Int) ?? 0
        let commentCount = (data["commentCount"] as? Int) ?? 0
        let likedBy = (data["likedBy"] as? [String]) ?? []
        let dislikedBy = (data["dislikedBy"] as? [String]) ?? []

        self.id = document.documentID
        self.authorId = authorId
        self.authorEmail = authorEmail
        self.authorName = authorName
        self.content = content
        self.timestamp = timestamp
        self.likeCount = likeCount
        self.dislikeCount = dislikeCount
        self.commentCount = commentCount
        self.likedBy = likedBy
        self.dislikedBy = dislikedBy
    }

    // Initialize from Firestore DocumentSnapshot
    init?(document: DocumentSnapshot) {
        guard let data = document.data() else { return nil }

        guard let authorId = data["authorId"] as? String,
              let authorEmail = data["authorEmail"] as? String,
              let content = data["content"] as? String else {
            return nil
        }

        // Get author name or use email as fallback
        let authorName = (data["authorName"] as? String) ?? "Unknown User"

        let timestamp = (data["timestamp"] as? Timestamp)?.dateValue() ?? Date()
        let likeCount = (data["likeCount"] as? Int) ?? 0
        let dislikeCount = (data["dislikeCount"] as? Int) ?? 0
        let commentCount = (data["commentCount"] as? Int) ?? 0
        let likedBy = (data["likedBy"] as? [String]) ?? []
        let dislikedBy = (data["dislikedBy"] as? [String]) ?? []

        self.id = document.documentID
        self.authorId = authorId
        self.authorEmail = authorEmail
        self.authorName = authorName
        self.content = content
        self.timestamp = timestamp
        self.likeCount = likeCount
        self.dislikeCount = dislikeCount
        self.commentCount = commentCount
        self.likedBy = likedBy
        self.dislikedBy = dislikedBy
    }
}
