/**
 * Cloud Functions for Firebase
 */

const functions = require("firebase-functions");
const admin = require("firebase-admin");
const {GoogleGenerativeAI} = require("@google/generative-ai");

admin.initializeApp();

// Your Gemini API key should be stored as a Firebase environment variable
// Use the following command to set it:
// firebase functions:config:set gemini.key="YOUR_API_KEY"
let geminiApiKey;
try {
  geminiApiKey = process.env.GEMINI_API_KEY ||
      (functions.config() && functions.config().gemini ?
        functions.config().gemini.key :
        "DEMO_KEY");
} catch (e) {
  // For local development without config
  console.log("Warning: Using demo key for Gemini API - API calls will fail");
  geminiApiKey = "DEMO_KEY";
}

console.log("Initializing Gemini API with key starting with: " +
    (geminiApiKey ? geminiApiKey.substring(0, 4) + "..." : "null"));
const genAI = new GoogleGenerativeAI(geminiApiKey);

// Constants for rate limiting
const RATE_LIMIT_COLLECTION = "rateLimits";
const DAILY_LIMIT_UNAUTHENTICATED = 5; // Limit for unauthenticated users

// Helper function to check and update rate limits
async function checkRateLimit(context) {
  // If authenticated, no rate limit
  if (context.auth) {
    return { limited: false };
  }

  const ip = (context.rawRequest && context.rawRequest.ip) ? context.rawRequest.ip : "unknown";
  const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
  const docId = `${ip}_${today}`;
  const rateLimitRef = admin.firestore().collection(RATE_LIMIT_COLLECTION).doc(docId);

  try {
    // Atomic transaction to check and update count
    const result = await admin.firestore().runTransaction(async (transaction) => {
      const doc = await transaction.get(rateLimitRef);

      // Current count or 0 if document doesn't exist
      const count = doc.exists ? doc.data().count : 0;

      // Check if limit exceeded
      if (count >= DAILY_LIMIT_UNAUTHENTICATED) {
        return {
          limited: true,
          count: count,
          limit: DAILY_LIMIT_UNAUTHENTICATED
        };
      }

      // Update or create the document with incremented count
      transaction.set(rateLimitRef, {
        ip: ip,
        date: today,
        count: count + 1,
        lastUpdated: admin.firestore.FieldValue.serverTimestamp()
      }, { merge: true });

      return {
        limited: false,
        count: count + 1,
        limit: DAILY_LIMIT_UNAUTHENTICATED
      };
    });

    return result;
  } catch (error) {
    console.error("Error checking rate limit:", error);
    // Default to not limiting on errors - better user experience than false positives
    return { limited: false };
  }
}

// Cloud Function to call Gemini API with the specified name talk_maxer_v2
exports.talk_maxer_v2 = functions.https.onCall(async (data, context) => {
  console.log("========== FUNCTION CALLED - FIXED VERSION 2.0 ==========");

  // Safely log request information
  try {
    console.log("Data received:", data ? "yes" : "no");
    console.log("Data type:", typeof data);

    if (data) {
      // Don't stringify the whole object - just log key info
      const dataKeys = Object.keys(data);
      console.log(`Data has ${dataKeys.length} keys:`,
          dataKeys.join(", "));
    }

    // Extract prompt from the correct location
    let prompt = null;

    if (data && data.prompt) {
      prompt = data.prompt;
      console.log("Found prompt directly in data.prompt");
    } else if (data && data.data && data.data.prompt) {
      prompt = data.data.prompt;
      console.log("Found prompt in data.data.prompt");
      // Move it to where the function expects it
      data.prompt = prompt;
    }

    console.log("Prompt found:", prompt ? "yes" : "no");
    if (prompt) {
      console.log("Prompt length:", prompt.length);
    }

    console.log("Auth context:", context.auth ?
        `User ID: ${context.auth.uid}` : "No auth");
  } catch (loggingError) {
    // Don't let logging errors crash the function
    console.error("Error in logging:", loggingError);
  }

  // Check rate limits for unauthenticated users
  if (!context.auth) {
    console.log("Unauthenticated request - checking rate limits");
    const rateLimitResult = await checkRateLimit(context);

    if (rateLimitResult.limited) {
      console.log(`Rate limit exceeded: ${rateLimitResult.count}/${rateLimitResult.limit} requests`);
      throw new functions.https.HttpsError(
        "resource-exhausted",
        `You have reached the daily limit of ${DAILY_LIMIT_UNAUTHENTICATED} requests for unauthenticated users. Please sign in to continue using the service.`,
        {
          count: rateLimitResult.count,
          limit: DAILY_LIMIT_UNAUTHENTICATED,
          requiresAuth: true
        }
      );
    }

    console.log(`Rate limit status: ${rateLimitResult.count}/${rateLimitResult.limit} requests`);
  }

  // Extract prompt from data or data.data
  let prompt = null;
  if (data && data.prompt) {
    prompt = data.prompt;
  } else if (data && data.data && data.data.prompt) {
    prompt = data.data.prompt;
  }

  // Validate that we have a prompt
  if (!prompt) {
    console.error("Missing prompt in request data!");
    throw new functions.https.HttpsError(
        "invalid-argument",
        "The function must be called with a 'prompt' argument.",
    );
  }

  try {
    console.log("Prompt received, length:", prompt.length);

    // Log user ID and timestamp for audit purposes
    const logData = {
      userId: context.auth ? context.auth.uid : "anonymous",
      timestamp: new Date().toISOString(),
    };
    functions.logger.info(
        `User ${logData.userId} made a Gemini API request`,
        logData,
    );

    // Check if using demo key
    if (geminiApiKey === "DEMO_KEY") {
      return {
        text: "This is a demo response. Please set the Gemini API key " +
              "in Firebase.",
      };
    }

    console.log("Initializing Gemini model...");
    // Initialize the model
    const model = genAI.getGenerativeModel({model: "gemini-2.0-flash"});

    console.log("Generating content with prompt length:", prompt.length);
    // Generate content
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    console.log("Generated response with length:", text.length);

    return {text};
  } catch (error) {
    console.error("Error calling Gemini API:", error);
    functions.logger.error("Error calling Gemini API:", error);

    throw new functions.https.HttpsError(
        "internal",
        "An error occurred while calling the Gemini API: " + error.message,
        {originalError: error.message},
    );
  }
});

// Cloud Function to create a new post
exports.createPost = functions.https.onCall(async (data, context) => {
  // Check if user is authenticated
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "You must be logged in to create a post."
    );
  }

  try {
    // Validate post data
    if (!data.content || typeof data.content !== "string" || data.content.trim() === "") {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Post content cannot be empty."
      );
    }

    // Try to get the user's profile to get their name
    let authorName = "Unknown User";
    try {
      const userProfileDoc = await admin.firestore().collection("users").doc(context.auth.uid).get();
      if (userProfileDoc.exists) {
        const userProfile = userProfileDoc.data();
        if (userProfile.firstName && userProfile.lastName) {
          authorName = `${userProfile.firstName} ${userProfile.lastName}`;
        }
      } else {
        // Fallback to email if profile not found
        authorName = context.auth.token.email.split('@')[0] || "Unknown User";
      }
    } catch (error) {
      console.error("Error fetching user profile:", error);
      // Continue with default name if there's an error
    }

    // Create post object
    const post = {
      authorId: context.auth.uid,
      authorEmail: context.auth.token.email || "Unknown",
      authorName: authorName,
      content: data.content.trim(),
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      likeCount: 0,
      dislikeCount: 0,
      commentCount: 0,
      likedBy: [],
      dislikedBy: []
    };

    // Save post to Firestore
    const postRef = await admin.firestore().collection("posts").add(post);

    console.log(`User ${context.auth.uid} created post with ID: ${postRef.id}`);

    return {
      success: true,
      postId: postRef.id
    };
  } catch (error) {
    console.error("Error creating post:", error);
    throw new functions.https.HttpsError(
      "internal",
      "An error occurred while creating the post: " + error.message
    );
  }
});

// Cloud Function to delete a post
exports.deletePost = functions.https.onCall(async (data, context) => {
  // Check if user is authenticated
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "You must be logged in to delete a post."
    );
  }

  try {
    // Validate post ID
    if (!data.postId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Post ID is required."
      );
    }

    // Get the post
    const postRef = admin.firestore().collection("posts").doc(data.postId);
    const postDoc = await postRef.get();

    // Check if post exists
    if (!postDoc.exists) {
      throw new functions.https.HttpsError(
        "not-found",
        "Post not found."
      );
    }

    // Check if user is the author of the post
    const postData = postDoc.data();
    if (postData.authorId !== context.auth.uid) {
      throw new functions.https.HttpsError(
        "permission-denied",
        "You can only delete your own posts."
      );
    }

    // Delete the post
    await postRef.delete();

    console.log(`User ${context.auth.uid} deleted post with ID: ${data.postId}`);

    return { success: true };
  } catch (error) {
    console.error("Error deleting post:", error);
    throw new functions.https.HttpsError(
      "internal",
      "An error occurred while deleting the post: " + error.message
    );
  }
});
