# Talk Maxer

A communication skills practice app that uses Google's Gemini AI for interactive conversations.

## Project Structure

The project has been organized into a clear directory structure:

- **Assets.xcassets/** - App assets including images and audio files
- **Config/** - Configuration files like GoogleService-Info.plist
- **Firebase/** - All Firebase-related files
- **Models/** - Data models used throughout the app
- **Services/** - Service classes for API calls, authentication, etc.
- **Utilities/** - Utility classes and helper functions
- **ViewModels/** - View models following MVVM architecture
- **Views/** - SwiftUI views

For more details, see the [PROJECT_STRUCTURE.md](PROJECT_STRUCTURE.md) file.

## Firebase Integration

This project uses Firebase for authentication and secure API calls to Gemini AI.

### Setup Instructions

For detailed Firebase setup instructions, see the [Firebase/README.md](Firebase/README.md) file.

### Prerequisites
- Node.js installed
- Firebase CLI installed (`npm install -g firebase-tools`)
- Xcode for iOS development

### Quick Start

1. **Get Configuration File**:
   - Download `GoogleService-Info.plist` from Firebase console
   - Add it to the `Config/` directory

2. **Deploy Functions**:
   - Use the deployment script:
   ```bash
   ./Firebase/deploy.sh
   ```

3. **Or Run Locally with Emulators**:
   ```bash
   cd Firebase
   firebase emulators:start
   ```

### Important Files

- `Firebase/Functions/index.js` - The Cloud Functions code
- `Services/FirebaseService.swift` - Swift service to interact with Firebase Functions
- `Services/AuthService.swift` - Swift service for authentication

### Security Benefits

- Gemini API key is stored securely in Firebase, not in the client app
- All API calls require authentication
- User data is stored securely in Firestore
- Rate limiting can be implemented on the server-side

## Troubleshooting

- If you encounter linting errors during deployment, run:
  ```bash
  cd Firebase/Functions && npm run lint -- --fix
  ```

- For emulator issues, check the Firebase logs and make sure you're using the right ports

## Authentication

The app now requires users to sign up or log in with email and password. This authentication:
- Secures access to the app
- Protects your Gemini API key from being exposed
- Enables user-specific data storage

## Security Features

- **API Key Protection**: The Gemini API key is now stored securely in Firebase and not in the client app
- **Authentication**: All API requests require authentication
- **Cloud Functions**: API requests go through Firebase Cloud Functions, not directly from the app
- **Rate Limiting**: Can be implemented on Firebase Functions if needed
- **Audit Logging**: User API requests are logged for monitoring

## Running the App

1. Open the project in Xcode
2. Make sure you've placed the `GoogleService-Info.plist` file in the `Config/` directory
3. Build and run the app
4. Create an account or sign in to start using the app