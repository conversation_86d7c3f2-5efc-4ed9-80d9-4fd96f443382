import SwiftUI
import Combine
import GoogleGenerativeAI

struct ChatHistory: Codable {
    let role: String // "user" or "model"
    let content: String
    let lessonId: String // To associate history with specific lessons
    let timestamp: Date? // When the message was created
}

class GeminiViewModel: ObservableObject {
    @Published var isLoading: Bool = false
    @Published var chatHistory: [ChatHistory] = []

    // Dependency on AuthService to ensure user is authenticated
    private let authService = AuthService.shared
    // Firebase service for making secure API calls
    private let firebaseService = FirebaseService.shared

    private let userDefaults = UserDefaults.standard
    private let historyKey = "chatHistory"

    init() {
        loadChatHistory()
    }

    // Optimized chat history loading with better error handling
    private func loadChatHistory() {
        guard let data = userDefaults.data(forKey: historyKey) else {
            // No data exists, start with empty array
            chatHistory = []
            return
        }

        do {
            let decoder = JSONDecoder()

            // Try to decode the current format with timestamps
            if let history = try? decoder.decode([ChatHistory].self, from: data) {
                // Filter out any corrupted entries
                self.chatHistory = history.filter { !$0.content.isEmpty && !$0.lessonId.isEmpty }
                return
            }

            // Legacy format handling
            struct LegacyChatHistory: Codable {
                let role: String
                let content: String
                let lessonId: String
            }

            let legacyHistory = try decoder.decode([LegacyChatHistory].self, from: data)
            self.chatHistory = legacyHistory.compactMap { legacy in
                // Skip any corrupted entries
                guard !legacy.content.isEmpty, !legacy.lessonId.isEmpty else { return nil }

                return ChatHistory(
                    role: legacy.role,
                    content: legacy.content,
                    lessonId: legacy.lessonId,
                    timestamp: Date() // Use current date as fallback
                )
            }

            // Save in the new format if we converted from legacy
            if !self.chatHistory.isEmpty {
                saveChatHistory()
            }
        } catch {
            print("Failed to load chat history: \(error)")
            // If decoding fails, start with empty array
            chatHistory = []
        }
    }

    // Save chat history to UserDefaults
    private func saveChatHistory() {
        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(chatHistory)
            userDefaults.set(data, forKey: historyKey)
        } catch {
            print("Failed to save chat history: \(error)")
        }
    }

    // Filter chat history by lesson ID
    func historyForLesson(_ lessonId: String) -> [ChatHistory] {
        return chatHistory.filter { $0.lessonId == lessonId }
    }

    // Add a single history entry and save it
    func addToHistory(_ entry: ChatHistory) {
        chatHistory.append(entry)
        saveChatHistory()
    }

    func generateResponse(for message: String, lesson: LessonPlan) async throws -> String {
        // Check if user is authenticated
        guard authService.isAuthenticated else {
            print("API Error: User not authenticated")
            throw NSError(domain: "GeminiViewModel", code: 401, userInfo: [NSLocalizedDescriptionKey: "Please sign in to continue. Authentication is required to use the AI features."])
        }

        // Update isLoading state on main thread
        await MainActor.run {
            isLoading = true
        }

        // Ensure we update isLoading when function exits
        defer {
            Task { @MainActor in
                isLoading = false
            }
        }

        // Add user message to history with current timestamp
        let lessonId = lesson.id.uuidString
        let userHistory = ChatHistory(role: "user", content: message, lessonId: lessonId, timestamp: Date())

        // Update chat history on main thread
        await MainActor.run {
            chatHistory.append(userHistory)
            saveChatHistory()
        }

        // Create a system prompt with lesson context
        let systemPrompt = """
        You are a communication skills tutor. The current lesson is: "\(lesson.title)"
        Lesson prompt: "\(lesson.prompt)".
        \(lesson.rules.isEmpty ? "" : "\nLesson specific rules:\n\(lesson.rules)")

        - Ignore punctuation error.
        - Fix user's mistake, by giving them feedback and instructions with example sentence on better way to say. Wait for user to fix their mistake before continuing. If user makes same mistake twice give him feedback and continue the conversation.
        - Be direct and keep short sentences.
        - Don't mention that you are an AI.
        - Act like a real human and act like a real teacher.
        - When conversation ends, start giving new scenario to the user again.
        - Do not give same scenario twice.
        - Do not mention or talk about Alcohol, Tobacco, or Drug Use or References.
        - Do not mention or talk about Violence, Nudity, Gambling.
        - Do not give medical prescriptions, medical advice.

        """

        // Build conversation history for context more efficiently
        // Get history for this specific lesson (perform this read on the main thread)
        let lessonHistory = await MainActor.run {
            return historyForLesson(lessonId)
        }

        // Add previous conversation history (limited to last 15 exchanges to reduce token usage)
        let recentHistory = lessonHistory.count > 15 ? Array(lessonHistory.suffix(15)) : lessonHistory

        // Build prompt more efficiently with string interpolation
        let historyText = recentHistory.map { entry in
            let roleLabel = entry.role == "user" ? "Student" : "Tutor"
            return "\(roleLabel): \(entry.content)"
        }.joined(separator: "\n")

        let fullPrompt = """
        \(systemPrompt)

        Conversation history:
        \(historyText)

        Based on this conversation history, provide your next response. Remember to be concise and not repeat information:
        """

        do {
            // Log API call in production
            print("Making API call to Gemini via Firebase for lesson: \(lesson.title)")

            // Use Firebase backend to call Gemini API securely
            let responseText = try await firebaseService.callGeminiAPI(prompt: fullPrompt)

            // Add AI response to history on main thread with current timestamp
            await MainActor.run {
                chatHistory.append(ChatHistory(role: "model", content: responseText, lessonId: lessonId, timestamp: Date()))
                saveChatHistory()
            }

            return responseText
        } catch {
            // Log errors in production
            print("Gemini API Error: \(error.localizedDescription)")

            // Handle our custom FirebaseService errors
            if let firebaseError = error as? FirebaseService.FirebaseServiceError {
                switch firebaseError {
                case .rateLimitExceeded(let requestsMade, let limit):
                    throw NSError(
                        domain: "GeminiViewModel",
                        code: 429,
                        userInfo: [
                            NSLocalizedDescriptionKey: "You've reached the daily limit of \(limit) requests. Sign in to continue using the AI features.",
                            "requestsMade": requestsMade,
                            "limit": limit
                        ]
                    )
                case .authenticationRequired:
                    throw NSError(
                        domain: "GeminiViewModel",
                        code: 401,
                        userInfo: [NSLocalizedDescriptionKey: "Authentication required. Please sign in to use this feature."]
                    )
                case .networkError(_):
                    throw NSError(
                        domain: "GeminiViewModel",
                        code: 503,
                        userInfo: [NSLocalizedDescriptionKey: "Cannot connect to the AI service. Please check your internet connection and try again."]
                    )
                case .serverError(_):
                    throw NSError(
                        domain: "GeminiViewModel",
                        code: 500,
                        userInfo: [NSLocalizedDescriptionKey: "The AI service is currently unavailable. Please try again in a few moments."]
                    )
                case .invalidResponse:
                    throw NSError(
                        domain: "GeminiViewModel",
                        code: 500,
                        userInfo: [NSLocalizedDescriptionKey: "Invalid response from the AI service. Please try again."]
                    )
                case .unknown(_):
                    // Use the default error handling below
                    break
                }
            }

            // Return a more user-friendly error message for other error types
            let errorDomain = (error as NSError).domain
            let errorCode = (error as NSError).code

            // Handle specific error cases
            if errorDomain.contains("Firebase") {
                if errorCode == NSURLErrorTimedOut || errorCode == NSURLErrorNotConnectedToInternet {
                    throw NSError(domain: "GeminiViewModel", code: 503,
                                  userInfo: [NSLocalizedDescriptionKey: "Cannot connect to the AI service. Please check your internet connection and try again."])
                } else {
                    throw NSError(domain: "GeminiViewModel", code: 500,
                                  userInfo: [NSLocalizedDescriptionKey: "The AI service is currently unavailable. Please try again in a few moments."])
                }
            } else if errorDomain.contains("GeminiViewModel") && errorCode == 401 {
                throw NSError(domain: "GeminiViewModel", code: 401,
                              userInfo: [NSLocalizedDescriptionKey: "Authentication required. Please sign in to use this feature."])
            }

            // Default error message
            throw NSError(domain: "GeminiViewModel", code: 500,
                          userInfo: [NSLocalizedDescriptionKey: "Something went wrong. Please try again later."])
        }
    }

    // Score calculation removed
}
