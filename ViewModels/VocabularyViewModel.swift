import Foundation
import SwiftUI
import Combine

// Data Transfer Object for parsing JSON from Gemini
struct VocabularyCardDTO: Codable {
    let word: String
    let meaning: String
    let exampleSentence: String
}

// Custom coding key for flexible JSON decoding
struct AnyCodingKey: CodingKey {
    var stringValue: String
    var intValue: Int?

    init(stringValue: String) {
        self.stringValue = stringValue
        self.intValue = nil
    }

    init(intValue: Int) {
        self.stringValue = String(intValue)
        self.intValue = intValue
    }

    init(stringValue: String, intValue: Int?) {
        self.stringValue = stringValue
        self.intValue = intValue
    }
}

class VocabularyViewModel: ObservableObject {
    @Published var vocabularyCards: [VocabularyCard] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String = ""
    @Published var sentenceFeedback: [String: SentenceFeedback] = [:]

    private let vocabularyService = VocabularyService.shared
    private let firebaseService = FirebaseService.shared
    private let authService = AuthService.shared

    // Keep track of previously seen words to avoid repetition
    private var previouslySeenWords: Set<String> = []
    private let previousWordsKey = "previouslySeenVocabularyWords"

    // Initialize with stored words or fetch new ones
    init() {
        // Load previously seen words
        loadPreviouslySeenWords()

        // Load vocabulary words
        loadVocabularyWords()
    }

    // Load previously seen words from UserDefaults
    private func loadPreviouslySeenWords() {
        if let data = UserDefaults.standard.data(forKey: previousWordsKey),
           let words = try? JSONDecoder().decode([String].self, from: data) {
            previouslySeenWords = Set(words)
            print("Loaded \(previouslySeenWords.count) previously seen words")
        }
    }

    // Save previously seen words to UserDefaults
    private func savePreviouslySeenWords() {
        do {
            let data = try JSONEncoder().encode(Array(previouslySeenWords))
            UserDefaults.standard.set(data, forKey: previousWordsKey)
            UserDefaults.standard.synchronize()
        } catch {
            print("Error saving previously seen words: \(error)")
        }
    }

    // Load vocabulary words from storage or fetch new ones if needed
    func loadVocabularyWords(forceRefresh: Bool = false) {
        if !forceRefresh, let storedWords = vocabularyService.getVocabularyWords(), !vocabularyService.shouldFetchNewWords() {
            self.vocabularyCards = storedWords
        } else {
            fetchNewVocabularyWords(forceRefresh: forceRefresh)
        }
    }

    // Fetch new vocabulary words from Gemini
    func fetchNewVocabularyWords(forceRefresh: Bool = false) {
        isLoading = true

        Task {
            do {
                // Generate a random seed to ensure we get different words each time
                let randomSeed = Int.random(in: 1...1000)
                let timestamp = Date().timeIntervalSince1970

                // Create a list of previously seen words to avoid repetition
                let previousWordsString = previouslySeenWords.isEmpty ?
                    "No previously seen words yet." :
                    "Previously seen words (DO NOT use these): " + previouslySeenWords.joined(separator: ", ")

                print("Previously seen words count: \(previouslySeenWords.count)")

                // Create a prompt for Gemini to generate vocabulary words
                let prompt = """
                Generate 5 NEW and DIFFERENT vocabulary words that would be useful for someone learning English and improving their communication skills. For each word, provide:
                1. The word itself
                2. A clear, concise definition
                3. An example sentence showing how to use the word in context

                IMPORTANT: Format your response ONLY as a JSON array with the following structure, with no additional text before or after:
                [
                  {
                    "word": "word1",
                    "meaning": "definition1",
                    "exampleSentence": "example sentence1"
                  },
                  ...and so on for all 5 words
                ]

                Choose words that are useful in professional and everyday conversations, at an intermediate to advanced level.

                DO NOT include any markdown formatting, explanations, or additional text. ONLY return the JSON array.

                IMPORTANT: This is request #\(randomSeed) at timestamp \(timestamp). Please provide COMPLETELY DIFFERENT words than you've provided before.

                \(previousWordsString)
                """

                // Call Gemini API through Firebase
                let response = try await firebaseService.callGeminiAPI(prompt: prompt)

                // First, clean up the response to handle code blocks
                var cleanedResponse = response

                print("Original response from Gemini: \(response)")

                // Remove markdown code block markers if present
                if cleanedResponse.contains("```json") {
                    let components = cleanedResponse.components(separatedBy: "```json")
                    if components.count > 1 {
                        let jsonPart = components[1].components(separatedBy: "```").first ?? components[1]
                        cleanedResponse = jsonPart
                    } else {
                        cleanedResponse = cleanedResponse.replacingOccurrences(of: "```json", with: "")
                        cleanedResponse = cleanedResponse.replacingOccurrences(of: "```", with: "")
                    }
                } else if cleanedResponse.contains("```") {
                    let components = cleanedResponse.components(separatedBy: "```")
                    if components.count > 1 {
                        cleanedResponse = components[1]
                    } else {
                        cleanedResponse = cleanedResponse.replacingOccurrences(of: "```", with: "")
                    }
                } else if cleanedResponse.hasPrefix("`") && cleanedResponse.hasSuffix("`") {
                    cleanedResponse = String(cleanedResponse.dropFirst().dropLast())
                }

                // Trim whitespace
                cleanedResponse = cleanedResponse.trimmingCharacters(in: .whitespacesAndNewlines)

                print("Cleaned response: \(cleanedResponse)")

                do {
                    // Try to find JSON array in the response
                    if let jsonStartIndex = cleanedResponse.firstIndex(of: "["),
                       let jsonEndIndex = cleanedResponse.lastIndex(of: "]") {
                        let jsonSubstring = cleanedResponse[jsonStartIndex...jsonEndIndex]
                        let jsonString = String(jsonSubstring)

                        if let jsonData = jsonString.data(using: .utf8) {
                            let decoder = JSONDecoder()

                            // Create a custom decoder to handle missing ID field
                            decoder.keyDecodingStrategy = .custom { codingKeys in
                                let key = codingKeys.last!.stringValue
                                if key == "id" {
                                    return AnyCodingKey(stringValue: "id", intValue: nil)
                                }
                                return codingKeys.last!
                            }

                            // Try to decode the array of vocabulary cards
                            var newWords = try decoder.decode([VocabularyCardDTO].self, from: jsonData)

                            // Convert DTOs to VocabularyCard objects
                            let vocabularyCards = newWords.map { dto in
                                VocabularyCard(
                                    word: dto.word,
                                    meaning: dto.meaning,
                                    exampleSentence: dto.exampleSentence
                                )
                            }

                            await MainActor.run {
                                self.vocabularyCards = vocabularyCards
                                self.isLoading = false

                                // Add words to previously seen list
                                for card in vocabularyCards {
                                    self.previouslySeenWords.insert(card.word.lowercased())
                                }

                                // Save previously seen words
                                self.savePreviouslySeenWords()

                                // Save the new words
                                self.vocabularyService.saveVocabularyWords(vocabularyCards)

                                print("Added \(vocabularyCards.count) new words to previously seen list. Total: \(self.previouslySeenWords.count)")
                            }
                            return
                        }
                    }

                    // If we still can't parse, create words manually from the response
                    print("Attempting to manually parse response")
                    let manualWords = self.manuallyParseVocabularyResponse(cleanedResponse)

                    if !manualWords.isEmpty {
                        await MainActor.run {
                            self.vocabularyCards = manualWords
                            self.isLoading = false

                            // Add words to previously seen list
                            for card in manualWords {
                                self.previouslySeenWords.insert(card.word.lowercased())
                            }

                            // Save previously seen words
                            self.savePreviouslySeenWords()

                            // Save the new words
                            self.vocabularyService.saveVocabularyWords(manualWords)

                            print("Added \(manualWords.count) manually parsed words to previously seen list. Total: \(self.previouslySeenWords.count)")
                        }
                    } else {
                        throw NSError(domain: "VocabularyViewModel", code: 1001, userInfo: [NSLocalizedDescriptionKey: "Could not parse vocabulary words from response"])
                    }
                } catch {
                    print("Error fetching vocabulary: \(error)")

                    // Last resort: create default vocabulary cards
                    let defaultCards = [
                        VocabularyCard(
                            word: "Articulate",
                            meaning: "Able to express thoughts and feelings clearly and effectively",
                            exampleSentence: "She is known for being an articulate speaker who can explain complex topics simply."
                        ),
                        VocabularyCard(
                            word: "Concise",
                            meaning: "Giving a lot of information clearly and in a few words",
                            exampleSentence: "His concise explanation helped everyone understand the concept quickly."
                        ),
                        VocabularyCard(
                            word: "Eloquent",
                            meaning: "Fluent or persuasive in speaking or writing",
                            exampleSentence: "The eloquent speech moved many people to tears."
                        ),
                        VocabularyCard(
                            word: "Coherent",
                            meaning: "Logical and consistent; easy to understand",
                            exampleSentence: "The professor gave a coherent lecture that helped students grasp the complex theory."
                        ),
                        VocabularyCard(
                            word: "Persuasive",
                            meaning: "Able to convince someone to do or believe something",
                            exampleSentence: "Her persuasive argument convinced the committee to approve the project."
                        )
                    ]

                    await MainActor.run {
                        self.vocabularyCards = defaultCards
                        self.isLoading = false
                        self.errorMessage = "Using default vocabulary words. Will try again tomorrow."

                        // Save the default words
                        self.vocabularyService.saveVocabularyWords(defaultCards)
                    }
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false

                    // Provide more specific error messages based on error type
                    if let firebaseError = error as? FirebaseService.FirebaseServiceError {
                        switch firebaseError {
                        case .networkError:
                            self.errorMessage = "Network error. Please check your internet connection and try again."
                        case .serverError:
                            self.errorMessage = "Server error. The AI service is currently unavailable."
                        case .rateLimitExceeded:
                            self.errorMessage = "You've reached the daily limit for AI requests."
                        case .authenticationRequired:
                            self.errorMessage = "Authentication required to use this feature."
                        default:
                            self.errorMessage = "Failed to fetch vocabulary words: \(firebaseError.localizedDescription)"
                        }
                    } else {
                        self.errorMessage = "Failed to fetch vocabulary words: \(error.localizedDescription)"
                    }

                    print("Error fetching vocabulary: \(error)")

                    // If we failed to fetch new words but have old ones, use those
                    if let storedWords = vocabularyService.getVocabularyWords(), self.vocabularyCards.isEmpty {
                        print("Using previously stored vocabulary words")
                        self.vocabularyCards = storedWords
                        self.errorMessage += " Using previously stored words."
                    } else if self.vocabularyCards.isEmpty {
                        // If we have no words at all, provide some default ones
                        self.vocabularyCards = [
                            VocabularyCard(
                                word: "Articulate",
                                meaning: "Able to express thoughts and feelings clearly and effectively",
                                exampleSentence: "She is known for being an articulate speaker who can explain complex topics simply."
                            ),
                            VocabularyCard(
                                word: "Concise",
                                meaning: "Giving a lot of information clearly and in a few words",
                                exampleSentence: "His concise explanation helped everyone understand the concept quickly."
                            ),
                            VocabularyCard(
                                word: "Eloquent",
                                meaning: "Fluent or persuasive in speaking or writing",
                                exampleSentence: "The eloquent speech moved many people to tears."
                            ),
                            VocabularyCard(
                                word: "Coherent",
                                meaning: "Logical and consistent; easy to understand",
                                exampleSentence: "The professor gave a coherent lecture that helped students grasp the complex theory."
                            ),
                            VocabularyCard(
                                word: "Persuasive",
                                meaning: "Able to convince someone to do or believe something",
                                exampleSentence: "Her persuasive argument convinced the committee to approve the project."
                            )
                        ]
                    }
                }
            }
        }
    }

    // Check a user's sentence with Gemini
    func checkSentence(word: String, sentence: String) {
        guard !sentence.isEmpty else { return }

        // Set loading state for this specific word
        DispatchQueue.main.async {
            var feedback = self.sentenceFeedback[word] ?? SentenceFeedback(isChecking: false, feedback: "", isCorrect: false)
            feedback.isChecking = true
            self.sentenceFeedback[word] = feedback
        }

        Task {
            do {
                // Create a prompt for Gemini to check the sentence
                let prompt = """
                Evaluate the following sentence that uses the word "\(word)":

                Sentence: "\(sentence)"

                Please check if:
                1. The word is used correctly according to its meaning
                2. The sentence is grammatically correct
                3. The sentence makes logical sense

                Format your response as a JSON object with the following structure:
                {
                  "isCorrect": true/false,
                  "feedback": "Your specific feedback about the sentence, including suggestions for improvement if needed"
                }

                Be encouraging but honest in your feedback. If there are minor issues but the word is used correctly, still mark it as correct but provide suggestions.
                """

                // Call Gemini API through Firebase
                let response = try await firebaseService.callGeminiAPI(prompt: prompt)

                // Clean up the response to handle code blocks
                var cleanedResponse = response

                // Remove markdown code block markers if present
                if cleanedResponse.contains("```json") {
                    cleanedResponse = cleanedResponse.replacingOccurrences(of: "```json", with: "")
                    cleanedResponse = cleanedResponse.replacingOccurrences(of: "```", with: "")
                } else if cleanedResponse.hasPrefix("`") && cleanedResponse.hasSuffix("`") {
                    cleanedResponse = String(cleanedResponse.dropFirst().dropLast())
                }

                // Trim whitespace
                cleanedResponse = cleanedResponse.trimmingCharacters(in: .whitespacesAndNewlines)

                // Try to parse the JSON response
                do {
                    // If the response contains text before or after the JSON, extract just the JSON part
                    if let jsonStartIndex = cleanedResponse.firstIndex(of: "{"),
                       let jsonEndIndex = cleanedResponse.lastIndex(of: "}") {
                        let jsonSubstring = cleanedResponse[jsonStartIndex...jsonEndIndex]
                        let jsonString = String(jsonSubstring)

                        if let data = jsonString.data(using: .utf8),
                           let jsonObject = try JSONSerialization.jsonObject(with: data) as? [String: Any] {

                            // Extract isCorrect and feedback
                            if let isCorrect = jsonObject["isCorrect"] as? Bool,
                               let feedbackText = jsonObject["feedback"] as? String {

                                // Update the UI on the main thread
                                await MainActor.run {
                                    self.sentenceFeedback[word] = SentenceFeedback(
                                        isChecking: false,
                                        feedback: feedbackText,
                                        isCorrect: isCorrect
                                    )
                                }
                                return
                            }
                        }
                    }

                    // If JSON parsing fails, try to extract feedback manually
                    let isCorrect = cleanedResponse.lowercased().contains("correct") && !cleanedResponse.lowercased().contains("incorrect")
                    let feedbackText = cleanedResponse

                    await MainActor.run {
                        self.sentenceFeedback[word] = SentenceFeedback(
                            isChecking: false,
                            feedback: feedbackText,
                            isCorrect: isCorrect
                        )
                    }
                } catch {
                    // If all parsing fails, just use the raw response
                    print("Error parsing feedback: \(error)")

                    let isCorrect = response.lowercased().contains("correct") && !response.lowercased().contains("incorrect")

                    await MainActor.run {
                        self.sentenceFeedback[word] = SentenceFeedback(
                            isChecking: false,
                            feedback: response,
                            isCorrect: isCorrect
                        )
                    }
                }
            } catch {
                await MainActor.run {
                    self.sentenceFeedback[word] = SentenceFeedback(
                        isChecking: false,
                        feedback: "Error checking sentence: \(error.localizedDescription)",
                        isCorrect: false
                    )
                }
            }
        }
    }

    // Clear feedback for a specific word and mark it as completed if correct
    func clearFeedback(for word: String) {
        // Check if the feedback was positive before removing it
        let wasCorrect = sentenceFeedback[word]?.isCorrect ?? false

        // Remove the feedback
        sentenceFeedback.removeValue(forKey: word)

        // If the feedback was positive, mark the word as completed
        if wasCorrect {
            markWordAsCompleted(word)
        }
    }

    // Mark a word as completed
    private func markWordAsCompleted(_ word: String) {
        // Find the index of the word in the vocabulary cards
        if let index = vocabularyCards.firstIndex(where: { $0.word == word }) {
            // Create a new card with isCompleted set to true
            let updatedCard = VocabularyCard(
                id: vocabularyCards[index].id,
                word: vocabularyCards[index].word,
                meaning: vocabularyCards[index].meaning,
                exampleSentence: vocabularyCards[index].exampleSentence,
                isCompleted: true
            )

            // Update the card in the array
            vocabularyCards[index] = updatedCard

            // Save the updated vocabulary cards
            vocabularyService.saveVocabularyWords(vocabularyCards)
        }
    }

    // Force refresh to get new words (for testing or user-initiated refresh)
    func forceRefresh() {
        print("Force refresh called - clearing stored words")
        vocabularyService.clearVocabularyWords()

        // Reset error message
        self.errorMessage = ""

        // Check if we need to reset the previously seen words list
        // If it gets too large, we'll start over to avoid running out of words
        if previouslySeenWords.count > 200 {
            print("Previously seen words list is getting large (\(previouslySeenWords.count)). Resetting.")
            previouslySeenWords.removeAll()
            UserDefaults.standard.removeObject(forKey: previousWordsKey)
            UserDefaults.standard.synchronize()
        }

        // Fetch new words with force refresh flag
        print("Initiating fetch of new vocabulary words with force refresh")
        fetchNewVocabularyWords(forceRefresh: true)
    }

    // Manually parse vocabulary response when JSON parsing fails
    private func manuallyParseVocabularyResponse(_ response: String) -> [VocabularyCard] {
        var words: [VocabularyCard] = []

        // Look for patterns like "word: "Word"", "meaning: "Definition"", etc.
        let lines = response.components(separatedBy: .newlines)

        var currentWord = ""
        var currentMeaning = ""
        var currentExample = ""

        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)

            // Check for word
            if trimmedLine.contains("word") || trimmedLine.contains("Word") {
                // If we already have a complete word, add it to our list
                if !currentWord.isEmpty && !currentMeaning.isEmpty && !currentExample.isEmpty {
                    words.append(VocabularyCard(word: currentWord, meaning: currentMeaning, exampleSentence: currentExample))
                    currentWord = ""
                    currentMeaning = ""
                    currentExample = ""
                }

                // Extract the word
                if let wordValue = extractValue(from: trimmedLine) {
                    currentWord = wordValue
                }
            }
            // Check for meaning/definition
            else if trimmedLine.contains("meaning") || trimmedLine.contains("definition") || trimmedLine.contains("Meaning") || trimmedLine.contains("Definition") {
                if let meaningValue = extractValue(from: trimmedLine) {
                    currentMeaning = meaningValue
                }
            }
            // Check for example
            else if trimmedLine.contains("example") || trimmedLine.contains("Example") {
                if let exampleValue = extractValue(from: trimmedLine) {
                    currentExample = exampleValue
                }
            }
        }

        // Add the last word if complete
        if !currentWord.isEmpty && !currentMeaning.isEmpty && !currentExample.isEmpty {
            words.append(VocabularyCard(word: currentWord, meaning: currentMeaning, exampleSentence: currentExample))
        }

        return words
    }

    // Helper method to extract values from text lines
    private func extractValue(from line: String) -> String? {
        // Try to find text between quotes
        if let startQuoteIndex = line.firstIndex(of: "\""),
           let endQuoteIndex = line.lastIndex(of: "\""),
           startQuoteIndex != endQuoteIndex {
            let startIndex = line.index(after: startQuoteIndex)
            return String(line[startIndex..<endQuoteIndex])
        }

        // Try to find text after a colon
        if let colonIndex = line.firstIndex(of: ":") {
            let startIndex = line.index(after: colonIndex)
            let valueText = line[startIndex...].trimmingCharacters(in: .whitespacesAndNewlines)

            // Remove quotes if present
            if valueText.hasPrefix("\"") && valueText.hasSuffix("\"") {
                let startIndex = valueText.index(after: valueText.startIndex)
                let endIndex = valueText.index(before: valueText.endIndex)
                return String(valueText[startIndex..<endIndex])
            }

            return valueText
        }

        return nil
    }
}

// Model to store sentence feedback
struct SentenceFeedback {
    var isChecking: Bool
    var feedback: String
    var isCorrect: Bool
}
