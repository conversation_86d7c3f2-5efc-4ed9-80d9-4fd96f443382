import Speech
import AVFoundation

class SpeechManager: NSObject, ObservableObject, AVSpeechSynthesizerDelegate, SFSpeechRecognizerDelegate {
    private let speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "en-US"))
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private let audioEngine = AVAudioEngine()

    let synthesizer = AVSpeechSynthesizer()

    // Common filler words to detect
    private let fillerWords = ["um", "uh", "er", "ah", "like", "you know", "hmm", "uhm"]

    @Published var isRecording = false
    @Published var transcribedText = ""
    @Published var isSpeaking = false
    @Published var rawTranscription = true // Toggle between raw and processed transcription

    // Audio session configuration cache
    private var isAudioSessionConfigured = false

    override init() {
        super.init()
        requestPermissions()
        synthesizer.delegate = self

        // Initial audio session configuration
        configureAudioSession(for: .playAndRecord)
    }

    // Centralized audio session configuration to avoid redundant calls
    private func configureAudioSession(for category: AVAudioSession.Category, mode: AVAudioSession.Mode = .default) {
        // Skip if already configured with the same category
        let audioSession = AVAudioSession.sharedInstance()

        do {
            // Only reconfigure if needed
            if audioSession.category != category || !isAudioSessionConfigured {
                if category == .playAndRecord {
                    try audioSession.setCategory(category, mode: mode, options: [.defaultToSpeaker, .allowBluetooth])
                } else {
                    try audioSession.setCategory(category, mode: mode)
                }
                try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
                isAudioSessionConfigured = true
            }
        } catch {
            print("Failed to configure audio session: \(error)")
        }
    }

    private func requestPermissions() {
        SFSpeechRecognizer.requestAuthorization { status in
            DispatchQueue.main.async {
                switch status {
                case .authorized:
                    print("Speech recognition authorized")
                case .denied:
                    print("Speech recognition authorization denied")
                case .restricted:
                    print("Speech recognition restricted")
                case .notDetermined:
                    print("Speech recognition not determined")
                @unknown default:
                    print("Unknown authorization status")
                }
            }
        }

        // Using the new API introduced in iOS 17
        if #available(iOS 17.0, *) {
            AVAudioApplication.requestRecordPermission(completionHandler: { granted in
                DispatchQueue.main.async {
                    if granted {
                        print("Microphone access granted")
                    } else {
                        print("Microphone access denied")
                    }
                }
            })
        } else {
            // Fallback for older iOS versions
            AVAudioSession.sharedInstance().requestRecordPermission { granted in
                DispatchQueue.main.async {
                    if granted {
                        print("Microphone access granted")
                    } else {
                        print("Microphone access denied")
                    }
                }
            }
        }
    }

    // Optimized process transcription to preserve or remove filler words
    private func processTranscription(_ transcription: SFTranscription) -> String {
        if rawTranscription {
            // For raw transcription, just return the formatted string
            return transcription.formattedString
        } else {
            // For processed transcription, remove filler words
            var processedText = transcription.formattedString

            // Create regex patterns once for better performance
            let fillerWordPatterns = fillerWords.compactMap { word -> NSRegularExpression? in
                try? NSRegularExpression(pattern: "\\b\(word)\\b", options: [.caseInsensitive])
            }

            // Apply all regex patterns in one pass
            for regex in fillerWordPatterns {
                processedText = regex.stringByReplacingMatches(
                    in: processedText,
                    options: [],
                    range: NSRange(location: 0, length: processedText.utf16.count),
                    withTemplate: ""
                )
            }

            // More efficient double space cleanup
            if processedText.contains("  ") {
                processedText = processedText.replacingOccurrences(of: "  +", with: " ", options: .regularExpression)
            }

            return processedText.trimmingCharacters(in: .whitespacesAndNewlines)
        }
    }

    func startRecording() throws {
        // Cancel any ongoing tasks
        recognitionTask?.cancel()
        recognitionTask = nil

        // Configure audio session for recording using centralized method
        configureAudioSession(for: .playAndRecord, mode: .measurement)

        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()

        let inputNode = audioEngine.inputNode
        guard let recognitionRequest = recognitionRequest else { return }

        // Configure recognition request to better capture natural speech
        recognitionRequest.shouldReportPartialResults = true

        // Use dictation hint to better capture natural speech with filler words
        recognitionRequest.taskHint = .dictation

        // Create a recognition task
        recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            var isFinal = false

            if let result = result, let self = self {
                // Process the transcription based on user preference
                let processedText = self.processTranscription(result.bestTranscription)

                // Update UI on main thread
                DispatchQueue.main.async {
                    self.transcribedText = processedText
                }

                isFinal = result.isFinal

                // Debug output to see what's being captured
                if self.rawTranscription {
                    print("Raw transcription: \(result.bestTranscription.formattedString)")

                    // Check for filler words in segments
                    for segment in result.bestTranscription.segments {
                        for fillerWord in self.fillerWords {
                            if segment.substring.lowercased() == fillerWord {
                                print("Filler word detected: \(segment.substring)")
                            }
                        }
                    }
                }
            }

            if error != nil || isFinal {
                self?.audioEngine.stop()
                inputNode.removeTap(onBus: 0)
                self?.recognitionRequest = nil
                self?.recognitionTask = nil

                // Update UI on main thread
                DispatchQueue.main.async {
                    self?.isRecording = false
                }
            }
        }

        // Get the native format of the input node
        let recordingFormat = inputNode.inputFormat(forBus: 0)

        // Remove any existing tap first
        inputNode.removeTap(onBus: 0)

        // Install tap with the native format
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }

        audioEngine.prepare()
        try audioEngine.start()

        isRecording = true
    }

    // Toggle between raw and processed transcription
    func toggleRawTranscription() {
        rawTranscription.toggle()
    }

    func stopRecording() {
        audioEngine.stop()
        recognitionRequest?.endAudio()

        // Update UI on main thread
        DispatchQueue.main.async {
            self.isRecording = false
        }
    }

    // Cached voice for better performance
    private static var cachedVoice: AVSpeechSynthesisVoice? = nil

    func speak(_ text: String) {
        // Update UI immediately to provide instant feedback
        DispatchQueue.main.async {
            self.isSpeaking = true
        }

        // Stop any ongoing speech
        if synthesizer.isSpeaking {
            synthesizer.stopSpeaking(at: .immediate)
        }

        // Configure audio session for playback only if needed
        if AVAudioSession.sharedInstance().category != .playback {
            configureAudioSession(for: .playback)
        }

        // Create and configure the utterance - cache common settings
        let utterance = AVSpeechUtterance(string: text)

        // Use cached voice if available
        if SpeechManager.cachedVoice == nil {
            SpeechManager.cachedVoice = AVSpeechSynthesisVoice(language: "en-US")
        }
        utterance.voice = SpeechManager.cachedVoice

        utterance.rate = 0.5
        utterance.pitchMultiplier = 1.0
        utterance.volume = 1.0

        // Speak directly without additional thread for short texts
        synthesizer.speak(utterance)
    }

    func stopSpeaking() {
        if synthesizer.isSpeaking {
            synthesizer.stopSpeaking(at: .immediate)

            // Update UI on main thread
            DispatchQueue.main.async {
                self.isSpeaking = false
            }
        }
    }

    // Prepare the speech synthesizer for immediate use
    func prepareForSpeech() {
        // Configure audio session for playback in advance
        // Only do this once per instance to avoid unnecessary audio session reconfiguration
        if !isAudioSessionConfigured {
            configureAudioSession(for: .playback)
        }
    }

    // AVSpeechSynthesizerDelegate methods
    func speechSynthesizer(_ synthesizer: AVSpeechSynthesizer, didFinish utterance: AVSpeechUtterance) {
        // Update UI on main thread
        DispatchQueue.main.async {
            self.isSpeaking = false
        }

        // Reset audio session to playAndRecord after speaking using centralized method
        configureAudioSession(for: .playAndRecord)
    }
}
