import Foundation
import Firebase
import FirebaseFirestore
// Remove FirebaseFirestoreSwift import until package is added
import Combine
import SwiftUI

class PostViewModel: ObservableObject {
    @Published var posts: [Post] = []
    @Published var userPosts: [Post] = []
    @Published var comments: [Comment] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var selectedPostId: String? = nil

    private let db = Firestore.firestore()
    private let authService = AuthService.shared
    private var commentsListener: ListenerRegistration?
    private var postsListener: ListenerRegistration?
    private var userPostsListener: ListenerRegistration?

    // Collection references
    private var postsRef: CollectionReference {
        return db.collection("posts")
    }

    private var commentsRef: CollectionReference {
        return db.collection("comments")
    }

    // Initialize the view model
    init() {
        // Fetch posts once instead of setting up real-time listener
        fetchAllPosts()

        // If user is logged in, set up listener for their posts only
        if authService.user != nil {
            setupUserPostsListener()
        }

        // Listen for auth state changes
        authService.addAuthStateListener { [weak self] user in
            if let user = user {
                print("👤 User authenticated: \(user.uid)")
                self?.setupUserPostsListener()
            } else {
                print("👤 User signed out")
                self?.userPosts = []
                // Remove user posts listener when signed out
                self?.userPostsListener?.remove()
                self?.userPostsListener = nil
            }
        }

        // Schedule periodic sync of likes from separate collection to post documents
        scheduleLikeSync()
    }

    // Clean up resources when the view model is no longer needed
    deinit {
        commentsListener?.remove()
        postsListener?.remove()
        userPostsListener?.remove()
    }

    // Schedule periodic sync of likes from separate collection to post documents
    private func scheduleLikeSync() {
        // Sync immediately on startup
        syncLikesToPosts()

        // Then schedule periodic sync every 5 minutes
        Timer.scheduledTimer(withTimeInterval: 300, repeats: true) { [weak self] _ in
            self?.syncLikesToPosts()
        }
    }

    // Sync likes from separate collection to post documents
    private func syncLikesToPosts() {
        print("🔄 Starting sync of likes from separate collection to post documents")

        // Get all likes from the separate collection
        db.collection("postLikes")
            .whereField("liked", isEqualTo: true)
            .getDocuments { [weak self] snapshot, error in
                guard let self = self else { return }

                if let error = error {
                    print("❌ Error fetching likes: \(error.localizedDescription)")
                    return
                }

                guard let documents = snapshot?.documents, !documents.isEmpty else {
                    print("ℹ️ No likes found in separate collection")
                    return
                }

                print("📊 Found \(documents.count) likes in separate collection")

                // Group likes by post ID
                var likesByPost: [String: [String]] = [:]

                for document in documents {
                    guard let postId = document.data()["postId"] as? String,
                          let userId = document.data()["userId"] as? String else {
                        continue
                    }

                    if likesByPost[postId] == nil {
                        likesByPost[postId] = []
                    }

                    likesByPost[postId]?.append(userId)
                }

                // Update each post with its likes
                for (postId, userIds) in likesByPost {
                    print("🔄 Syncing \(userIds.count) likes for post \(postId)")

                    // Get the current post data
                    self.postsRef.document(postId).getDocument { snapshot, error in
                        if let error = error {
                            print("❌ Error fetching post \(postId): \(error.localizedDescription)")
                            return
                        }

                        guard let snapshot = snapshot, snapshot.exists else {
                            print("❌ Post \(postId) not found")
                            return
                        }

                        // Get the current likedBy array
                        let currentLikedBy = snapshot.data()?["likedBy"] as? [String] ?? []

                        // Find user IDs that are in the separate collection but not in the post document
                        let newLikes = userIds.filter { !currentLikedBy.contains($0) }

                        if newLikes.isEmpty {
                            print("ℹ️ No new likes to sync for post \(postId)")
                            return
                        }

                        print("🔄 Adding \(newLikes.count) new likes to post \(postId)")

                        // Update the post document
                        let updateData: [String: Any] = [
                            "likeCount": FieldValue.increment(Int64(newLikes.count)),
                            "likedBy": FieldValue.arrayUnion(newLikes)
                        ]

                        self.postsRef.document(postId).updateData(updateData) { error in
                            if let error = error {
                                print("❌ Error updating post \(postId): \(error.localizedDescription)")
                            } else {
                                print("✅ Successfully synced likes for post \(postId)")
                            }
                        }
                    }
                }
            }
    }

    // Set up real-time listener for all posts
    func setupPostsListener() {
        print("🔍 Setting up real-time listener for all posts")

        // Remove any existing listener
        if postsListener != nil {
            print("🔍 Removing existing posts listener")
            postsListener?.remove()
        }

        isLoading = true

        // Set up a real-time listener for posts
        postsListener = postsRef.order(by: "timestamp", descending: true)
            .addSnapshotListener { [weak self] snapshot, error in
                guard let self = self else {
                    print("❌ Self reference lost in posts listener")
                    return
                }

                print("🔍 Posts listener fired")

                if let error = error {
                    print("❌ Error in posts listener: \(error.localizedDescription)")
                    self.isLoading = false
                    self.errorMessage = "Error fetching posts: \(error.localizedDescription)"
                    return
                }

                guard let snapshot = snapshot else {
                    print("❌ Snapshot is nil in posts listener")
                    self.isLoading = false
                    return
                }

                print("🔍 Received snapshot with \(snapshot.documents.count) posts")
                print("🔍 Document changes: \(snapshot.documentChanges.count)")

                // Get posts from documents
                let fetchedPosts = snapshot.documents.compactMap { document in
                    Post(document: document)
                }

                // Now fetch likes from the separate collection
                self.fetchLikesForPosts(fetchedPosts) { updatedPosts in
                    DispatchQueue.main.async {
                        self.isLoading = false
                        self.posts = updatedPosts
                        print("✅ Updated posts array with \(updatedPosts.count) posts")
                    }
                }
            }
    }

    // Fetch all posts (one-time query, for manual refresh)
    func fetchAllPosts() {
        print("🔍 Manual refresh of all posts requested")

        // One-time query to fetch posts without setting up a real-time listener
        isLoading = true

        postsRef.order(by: "timestamp", descending: true)
            .getDocuments { [weak self] snapshot, error in
                guard let self = self else { return }

                if let error = error {
                    self.isLoading = false
                    self.errorMessage = "Error fetching posts: \(error.localizedDescription)"
                    print("❌ Error fetching posts: \(error.localizedDescription)")
                    return
                }

                guard let documents = snapshot?.documents else {
                    self.isLoading = false
                    self.errorMessage = "No documents found"
                    print("❌ No documents found")
                    return
                }

                print("✅ Successfully fetched \(documents.count) posts")

                // Get posts from documents
                let fetchedPosts = documents.compactMap { document in
                    Post(document: document)
                }

                // Now fetch likes from the separate collection
                self.fetchLikesForPosts(fetchedPosts) { updatedPosts in
                    DispatchQueue.main.async {
                        self.isLoading = false
                        self.posts = updatedPosts
                        print("✅ Updated posts array with \(updatedPosts.count) posts after manual refresh")
                    }
                }
            }
    }

    // Fetch likes from the separate collection and update post data
    private func fetchLikesForPosts(_ posts: [Post], completion: @escaping ([Post]) -> Void) {
        // If there are no posts, return immediately
        if posts.isEmpty {
            completion(posts)
            return
        }

        // Get the current user ID
        let currentUserId = authService.user?.uid

        // Create a dispatch group to wait for all like queries to complete
        let group = DispatchGroup()

        // Create a mutable copy of the posts
        var updatedPosts = posts

        // For each post, check if there are any likes in the separate collection
        for (index, post) in posts.enumerated() {
            guard let postId = post.id else { continue }

            // Enter the dispatch group
            group.enter()

            // Query the likes collection for this post
            db.collection("postLikes")
                .whereField("postId", isEqualTo: postId)
                .whereField("liked", isEqualTo: true)
                .getDocuments { snapshot, error in
                    defer {
                        // Leave the dispatch group when done
                        group.leave()
                    }

                    if let error = error {
                        print("⚠️ Error fetching likes for post \(postId): \(error.localizedDescription)")
                        return
                    }

                    guard let documents = snapshot?.documents, !documents.isEmpty else {
                        // No likes found in the separate collection
                        return
                    }

                    print("📊 Found \(documents.count) likes in separate collection for post \(postId)")

                    // Extract user IDs from the documents
                    let userIds = documents.compactMap { doc -> String? in
                        return doc.data()["userId"] as? String
                    }

                    // Update the post with the likes from the separate collection
                    var updatedPost = post

                    // Add any user IDs that aren't already in the likedBy array
                    for userId in userIds {
                        if !updatedPost.likedBy.contains(userId) {
                            updatedPost.likedBy.append(userId)
                            updatedPost.likeCount += 1
                        }
                    }

                    // Update the post in our array
                    updatedPosts[index] = updatedPost

                    // If the current user is one of the likers, print a debug message
                    if let currentUserId = currentUserId, userIds.contains(currentUserId) {
                        print("🔍 Current user has liked post \(postId) according to separate collection")
                    }
                }
        }

        // When all queries are complete, return the updated posts
        group.notify(queue: .main) {
            completion(updatedPosts)
        }
    }

    // Fetch a single post by ID
    func fetchPostById(postId: String, completion: @escaping (Post?) -> Void) {
        postsRef.document(postId).getDocument { snapshot, error in
            if let error = error {
                print("Error fetching post: \(error.localizedDescription)")
                completion(nil)
                return
            }

            guard let snapshot = snapshot, snapshot.exists else {
                print("Post not found")
                completion(nil)
                return
            }

            if let post = Post(document: snapshot) {
                completion(post)
            } else {
                completion(nil)
            }
        }
    }



    // Set up real-time listener for user posts
    func setupUserPostsListener() {
        guard let currentUser = authService.user else {
            print("❌ Cannot set up user posts listener: User not logged in")
            errorMessage = "User not logged in"
            return
        }

        print("🔍 Setting up real-time listener for user posts")

        // Remove any existing listener
        if userPostsListener != nil {
            print("🔍 Removing existing user posts listener")
            userPostsListener?.remove()
        }

        isLoading = true

        // Set up a real-time listener for user posts
        userPostsListener = postsRef.whereField("authorId", isEqualTo: currentUser.uid)
            .order(by: "timestamp", descending: true)
            .addSnapshotListener { [weak self] snapshot, error in
                guard let self = self else {
                    print("❌ Self reference lost in user posts listener")
                    return
                }

                print("🔍 User posts listener fired")

                if let error = error {
                    print("❌ Error in user posts listener: \(error.localizedDescription)")
                    self.isLoading = false
                    self.errorMessage = "Error fetching user posts: \(error.localizedDescription)"
                    return
                }

                guard let snapshot = snapshot else {
                    print("❌ Snapshot is nil in user posts listener")
                    self.isLoading = false
                    return
                }

                print("🔍 Received snapshot with \(snapshot.documents.count) user posts")
                print("🔍 Document changes: \(snapshot.documentChanges.count)")

                // Get posts from documents
                let fetchedPosts = snapshot.documents.compactMap { document in
                    Post(document: document)
                }

                // Now fetch likes from the separate collection
                self.fetchLikesForPosts(fetchedPosts) { updatedPosts in
                    DispatchQueue.main.async {
                        self.isLoading = false
                        self.userPosts = updatedPosts
                        print("✅ Updated user posts array with \(updatedPosts.count) posts")
                    }
                }
            }
    }

    // Fetch posts for current user (one-time query, for manual refresh)
    func fetchUserPosts() {
        guard let currentUser = authService.user else {
            errorMessage = "User not logged in"
            return
        }

        print("🔍 Manual refresh of user posts requested")

        // Even if we have a real-time listener, we'll still do a manual refresh
        // This ensures the refresh button works as expected
        isLoading = true

        postsRef.whereField("authorId", isEqualTo: currentUser.uid)
            .order(by: "timestamp", descending: true)
            .getDocuments { [weak self] snapshot, error in
                guard let self = self else { return }

                if let error = error {
                    self.isLoading = false
                    self.errorMessage = "Error fetching user posts: \(error.localizedDescription)"
                    print("❌ Error fetching user posts: \(error.localizedDescription)")
                    return
                }

                guard let documents = snapshot?.documents else {
                    self.isLoading = false
                    self.errorMessage = "No documents found"
                    print("❌ No user posts found")
                    return
                }

                print("✅ Successfully fetched \(documents.count) user posts")

                // Get posts from documents
                let fetchedPosts = documents.compactMap { document in
                    Post(document: document)
                }

                // Now fetch likes from the separate collection
                self.fetchLikesForPosts(fetchedPosts) { updatedPosts in
                    DispatchQueue.main.async {
                        self.isLoading = false
                        self.userPosts = updatedPosts
                        print("✅ Updated user posts array with \(updatedPosts.count) posts after manual refresh")
                    }
                }
            }
    }

    // Create a new post using Firestore directly and return the created post
    func createPost(content: String, completion: @escaping (Bool, String?, Post?) -> Void) {
        guard let currentUser = authService.user else {
            completion(false, "User not logged in", nil)
            return
        }

        print("🔍 Creating new post")
        isLoading = true

        // Get current timestamp for immediate display
        let clientTimestamp = Date()

        // Get user's full name from profile if available
        let authorName: String
        if let userProfile = authService.userProfile {
            authorName = userProfile.fullName
        } else {
            // Fallback to email if profile not available
            authorName = currentUser.email?.components(separatedBy: "@").first ?? "Unknown User"
        }

        // Create post data
        let postData: [String: Any] = [
            "authorId": currentUser.uid,
            "authorEmail": currentUser.email ?? "Unknown",
            "authorName": authorName,
            "content": content.trimmingCharacters(in: .whitespacesAndNewlines),
            "timestamp": FieldValue.serverTimestamp(),
            "likeCount": 0,
            "dislikeCount": 0,
            "commentCount": 0,
            "likedBy": [],
            "dislikedBy": []
        ]

        // Add document to Firestore
        let docRef = postsRef.addDocument(data: postData)

        // Create a local post object for immediate display
        let newPost = Post(
            id: docRef.documentID,
            authorId: currentUser.uid,
            authorEmail: currentUser.email ?? "Unknown",
            authorName: authorName,
            content: content.trimmingCharacters(in: .whitespacesAndNewlines),
            timestamp: clientTimestamp,
            likeCount: 0,
            dislikeCount: 0,
            commentCount: 0,
            likedBy: [],
            dislikedBy: []
        )

        print("🔍 Created local post object with ID: \(docRef.documentID)")

        // Don't add to the posts array here - we'll do that in the view
        // This prevents automatic updates for other users
        DispatchQueue.main.async {
            self.isLoading = false

            // Always add to the userPosts array for the current user's profile view
            // This ensures posts always appear in the user's profile
            self.userPosts.insert(newPost, at: 0)
            print("✅ Added new post to userPosts array")

            // Also fetch user posts to ensure we have the latest data
            self.fetchUserPosts()

            // Return the new post to the caller for immediate feedback
            // The view will handle adding it to the visible posts array
            completion(true, nil, newPost)
        }
    }

    // Delete a post using Firestore directly
    func deletePost(postId: String, completion: @escaping (Bool, String?) -> Void) {
        guard let currentUser = authService.user else {
            completion(false, "User not logged in")
            return
        }

        isLoading = true

        // First check if the post belongs to the current user
        postsRef.document(postId).getDocument { [weak self] snapshot, error in
            guard let self = self else { return }

            if let error = error {
                self.isLoading = false
                completion(false, "Error fetching post: \(error.localizedDescription)")
                return
            }

            guard let snapshot = snapshot, snapshot.exists else {
                self.isLoading = false
                completion(false, "Post not found")
                return
            }

            let data = snapshot.data() ?? [:]
            guard let authorId = data["authorId"] as? String else {
                self.isLoading = false
                completion(false, "Invalid post data")
                return
            }

            // Verify the post belongs to the current user
            guard authorId == currentUser.uid else {
                self.isLoading = false
                completion(false, "You can only delete your own posts")
                return
            }

            // First, delete all comments associated with this post
            print("🔍 Deleting all comments for post ID: \(postId)")

            // Query all comments for this post
            self.commentsRef.whereField("postId", isEqualTo: postId).getDocuments { [weak self] (snapshot, error) in
                guard let self = self else { return }

                if let error = error {
                    self.isLoading = false
                    print("❌ Error fetching comments for post: \(error.localizedDescription)")
                    completion(false, "Error fetching comments: \(error.localizedDescription)")
                    return
                }

                guard let documents = snapshot?.documents else {
                    // No comments found, proceed with deleting the post
                    print("ℹ️ No comments found for post ID: \(postId)")
                    self.deletePostAfterCommentsRemoval(postId: postId, completion: completion)
                    return
                }

                print("🔍 Found \(documents.count) comments to delete for post ID: \(postId)")

                // If there are no comments, proceed with deleting the post
                if documents.isEmpty {
                    self.deletePostAfterCommentsRemoval(postId: postId, completion: completion)
                    return
                }

                // If there are comments, delete them in a batch
                let batch = self.db.batch()

                documents.forEach { document in
                    batch.deleteDocument(document.reference)
                }

                // Commit the batch delete
                batch.commit { error in
                    if let error = error {
                        self.isLoading = false
                        print("❌ Error deleting comments: \(error.localizedDescription)")
                        completion(false, "Error deleting comments: \(error.localizedDescription)")
                        return
                    }

                    print("✅ Successfully deleted \(documents.count) comments for post ID: \(postId)")

                    // Now delete the post
                    self.deletePostAfterCommentsRemoval(postId: postId, completion: completion)
                }
            }
        }
    }

    // Helper method to delete the post after comments have been removed
    private func deletePostAfterCommentsRemoval(postId: String, completion: @escaping (Bool, String?) -> Void) {
        // Delete the post
        self.postsRef.document(postId).delete { [weak self] error in
            guard let self = self else { return }

            self.isLoading = false

            if let error = error {
                print("❌ Error deleting post: \(error.localizedDescription)")
                completion(false, "Error deleting post: \(error.localizedDescription)")
                return
            }

            print("✅ Post successfully deleted with ID: \(postId)")

            // Update local arrays to remove the deleted post and its comments
            DispatchQueue.main.async {
                // Remove from posts array
                self.posts.removeAll { $0.id == postId }

                // Remove from userPosts array
                self.userPosts.removeAll { $0.id == postId }

                // Remove all comments associated with this post from local comments array
                let commentsCountBefore = self.comments.count
                self.comments.removeAll { $0.postId == postId }
                let commentsRemoved = commentsCountBefore - self.comments.count

                print("✅ Removed deleted post from local arrays")
                print("✅ Removed \(commentsRemoved) comments associated with the deleted post")

                // If the deleted post was the selected post, clear the selection
                if self.selectedPostId == postId {
                    self.selectedPostId = nil
                }
            }

            completion(true, nil)
        }
    }

    // MARK: - Like/Dislike Methods

    // Toggle like on a post (with refresh)
    func toggleLike(postId: String, completion: @escaping (Bool, String?) -> Void) {
        guard let currentUser = authService.user else {
            completion(false, "User not logged in")
            return
        }

        let userId = currentUser.uid

        // Get the post document
        postsRef.document(postId).getDocument { [weak self] snapshot, error in
            guard let self = self else { return }

            if let error = error {
                completion(false, "Error fetching post: \(error.localizedDescription)")
                return
            }

            guard let snapshot = snapshot, snapshot.exists else {
                completion(false, "Post not found")
                return
            }

            guard let post = Post(document: snapshot) else {
                completion(false, "Invalid post data")
                return
            }

            // Check if user already liked the post
            let alreadyLiked = post.likedBy.contains(userId)

            // Update the post data
            var updateData: [String: Any] = [:]

            if alreadyLiked {
                // Remove like
                updateData["likeCount"] = FieldValue.increment(Int64(-1))
                updateData["likedBy"] = FieldValue.arrayRemove([userId])
            } else {
                // Add like
                updateData["likeCount"] = FieldValue.increment(Int64(1))
                updateData["likedBy"] = FieldValue.arrayUnion([userId])

                // If user previously disliked, remove dislike
                if post.dislikedBy.contains(userId) {
                    updateData["dislikeCount"] = FieldValue.increment(Int64(-1))
                    updateData["dislikedBy"] = FieldValue.arrayRemove([userId])
                }
            }

            // Update the post in Firestore
            self.postsRef.document(postId).updateData(updateData) { error in
                if let error = error {
                    completion(false, "Error updating post: \(error.localizedDescription)")
                    return
                }

                // Refresh posts
                self.fetchAllPosts()
                self.fetchUserPosts()
                completion(true, nil)
            }
        }
    }

    // Check if the current user has permission to update a post
    private func checkPermission(forPost postId: String, completion: @escaping (Bool, String?) -> Void) {
        // First, get the post to check if it exists
        postsRef.document(postId).getDocument { [weak self] snapshot, error in
            guard let self = self else {
                completion(false, "Internal error")
                return
            }

            if let error = error {
                print("❌ Error fetching post: \(error.localizedDescription)")
                completion(false, "Error fetching post: \(error.localizedDescription)")
                return
            }

            guard let snapshot = snapshot, snapshot.exists else {
                print("❌ Post not found with ID: \(postId)")
                completion(false, "Post not found")
                return
            }

            // Try a simple update to see if we have permission
            // Use a special field name that's unlikely to conflict with real data
            let testUpdate: [String: Any] = ["_permissionTest": FieldValue.serverTimestamp()]

            self.postsRef.document(postId).updateData(testUpdate) { error in
                if let error = error {
                    if let nsError = error as NSError?,
                       nsError.domain == "FIRFirestoreErrorDomain",
                       nsError.code == 7 { // Permission denied
                        print("❌ PERMISSION TEST: User does not have permission to update post \(postId)")

                        // Try to determine why permission was denied
                        if let currentUser = self.authService.user {
                            print("🔍 Current user ID: \(currentUser.uid)")
                            print("🔍 Current user email: \(currentUser.email ?? "unknown")")

                            // Get post data to check author
                            if let data = snapshot.data(),
                               let authorId = data["authorId"] as? String {
                                print("🔍 Post author ID: \(authorId)")

                                if authorId == currentUser.uid {
                                    print("⚠️ User is the author but still doesn't have permission")
                                    completion(false, "Permission denied: You should have permission as the author but don't")
                                } else {
                                    print("⚠️ User is not the author - this might be expected")
                                    completion(false, "Permission denied: Only the post author can update this post")
                                }
                            } else {
                                completion(false, "Permission denied: You don't have permission to update this post")
                            }
                        } else {
                            completion(false, "Permission denied: You must be logged in to update posts")
                        }
                    } else {
                        // Some other error
                        print("❌ Error checking permissions: \(error.localizedDescription)")
                        completion(false, "Error checking permissions: \(error.localizedDescription)")
                    }
                } else {
                    // Success - we have permission
                    // Now remove the test field
                    self.postsRef.document(postId).updateData(["_permissionTest": FieldValue.delete()]) { _ in
                        // Don't care about the result of this cleanup
                        print("✅ Permission check passed - user has permission to update post \(postId)")
                        completion(true, nil)
                    }
                }
            }
        }
    }

    // Create a special like field update function that doesn't require full document update permission
    func createLikeFieldUpdate(postId: String, userId: String, isLiked: Bool, completion: @escaping (Bool, String?) -> Void) {
        print("🔄 Creating special like field update for post \(postId) by user \(userId), isLiked: \(isLiked)")

        // First, find the post in our local array to update it
        var postToUpdate: Post?
        var postIndex: Int = -1

        // Check in the main posts array
        if let index = posts.firstIndex(where: { $0.id == postId }) {
            postToUpdate = posts[index]
            postIndex = index
        }

        // Create a special document just for this like operation
        let likeDocRef = db.collection("postLikes").document("\(postId)_\(userId)")

        if isLiked {
            // For liking: Set the like data
            likeDocRef.setData([
                "postId": postId,
                "userId": userId,
                "liked": true,
                "timestamp": FieldValue.serverTimestamp()
            ]) { [weak self] error in
                guard let self = self else { return }

                if let error = error {
                    print("❌ Error creating like document: \(error.localizedDescription)")
                    completion(false, "Error creating like: \(error.localizedDescription)")
                    return
                }

                print("✅ Successfully created like document")

                // We don't need to update the local array here
                // The UI will be updated by the view after the server update is complete

                // Now also update the actual post document in Firestore
                self.updatePostWithLike(postId: postId, userId: userId, isLiked: true) { success, message in
                    if !success {
                        print("⚠️ Warning: Could not update post document directly: \(message ?? "Unknown error")")
                        print("⚠️ But like was recorded in separate collection")
                    } else {
                        print("✅ Successfully updated post document directly")
                    }

                    // Still consider the operation successful even if the direct update failed
                    completion(true, nil)
                }
            }
        } else {
            // For unliking: Delete the like document
            likeDocRef.delete { [weak self] error in
                guard let self = self else { return }

                if let error = error {
                    print("❌ Error deleting like document: \(error.localizedDescription)")
                    completion(false, "Error removing like: \(error.localizedDescription)")
                    return
                }

                print("✅ Successfully deleted like document")

                // We don't need to update the local array here
                // The UI will be updated by the view after the server update is complete

                // Now also update the actual post document in Firestore
                self.updatePostWithLike(postId: postId, userId: userId, isLiked: false) { success, message in
                    if !success {
                        print("⚠️ Warning: Could not update post document directly: \(message ?? "Unknown error")")
                        print("⚠️ But unlike was recorded in separate collection")
                    } else {
                        print("✅ Successfully updated post document directly")
                    }

                    // Still consider the operation successful even if the direct update failed
                    completion(true, nil)
                }
            }
        }
    }

    // Helper method to update the post document with like information
    private func updatePostWithLike(postId: String, userId: String, isLiked: Bool, completion: @escaping (Bool, String?) -> Void) {
        // First get the current post data
        postsRef.document(postId).getDocument { [weak self] snapshot, error in
            guard let self = self else {
                completion(false, "Internal error: self reference lost")
                return
            }

            if let error = error {
                completion(false, "Error fetching post: \(error.localizedDescription)")
                return
            }

            guard let snapshot = snapshot, snapshot.exists else {
                completion(false, "Post not found")
                return
            }

            // Try to update the post document directly
            var updateData: [String: Any] = [:]

            if isLiked {
                // Add like
                updateData["likeCount"] = FieldValue.increment(Int64(1))
                updateData["likedBy"] = FieldValue.arrayUnion([userId])

                // If user previously disliked, remove dislike
                if let dislikedBy = snapshot.data()?["dislikedBy"] as? [String], dislikedBy.contains(userId) {
                    updateData["dislikeCount"] = FieldValue.increment(Int64(-1))
                    updateData["dislikedBy"] = FieldValue.arrayRemove([userId])
                }
            } else {
                // Remove like
                updateData["likeCount"] = FieldValue.increment(Int64(-1))
                updateData["likedBy"] = FieldValue.arrayRemove([userId])
            }

            // Update the post document
            self.postsRef.document(postId).updateData(updateData) { error in
                if let error = error {
                    if let nsError = error as NSError?,
                       nsError.domain == "FIRFirestoreErrorDomain",
                       nsError.code == 7 { // Permission denied
                        completion(false, "Permission denied: Only the post author can update this post directly")
                    } else {
                        completion(false, "Error updating post: \(error.localizedDescription)")
                    }
                    return
                }

                completion(true, nil)
            }
        }
    }

    // Toggle like on a post using a transaction (more reliable)
    func toggleLikeWithTransaction(postId: String, userId: String, isLiked: Bool, completion: @escaping (Bool, String?) -> Void) {
        print("🔄 toggleLikeWithTransaction called with postId: \(postId), userId: \(userId), isLiked: \(isLiked)")

        let postRef = postsRef.document(postId)

        // Use a transaction for atomic updates
        db.runTransaction({ (transaction, errorPointer) -> Any? in
            do {
                // Get the current post data
                let postDocument = try transaction.getDocument(postRef)

                guard let post = Post(document: postDocument) else {
                    throw NSError(domain: "AppErrorDomain", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid post data"])
                }

                print("📊 Transaction - Current post data: likeCount: \(post.likeCount), likedBy: \(post.likedBy)")

                var newLikeCount = post.likeCount
                var newLikedBy = post.likedBy
                var newDislikeCount = post.dislikeCount
                var newDislikedBy = post.dislikedBy

                if isLiked {
                    // User wants to like the post
                    if !newLikedBy.contains(userId) {
                        // Only increment if not already liked
                        newLikeCount += 1
                        newLikedBy.append(userId)
                        print("➕ Transaction - Adding like for user: \(userId)")
                    }

                    // Remove dislike if present
                    if newDislikedBy.contains(userId) {
                        newDislikeCount = max(0, newDislikeCount - 1)
                        newDislikedBy.removeAll { $0 == userId }
                        print("🔄 Transaction - Removing previous dislike for user: \(userId)")
                    }
                } else {
                    // User wants to unlike the post
                    if newLikedBy.contains(userId) {
                        // Only decrement if already liked
                        newLikeCount = max(0, newLikeCount - 1)
                        newLikedBy.removeAll { $0 == userId }
                        print("➖ Transaction - Removing like for user: \(userId)")
                    }
                }

                // Update the document
                transaction.updateData([
                    "likeCount": newLikeCount,
                    "likedBy": newLikedBy,
                    "dislikeCount": newDislikeCount,
                    "dislikedBy": newDislikedBy
                ], forDocument: postRef)

                // Return the updated counts for UI update
                return [
                    "likeCount": newLikeCount,
                    "likedBy": newLikedBy
                ]
            } catch let error as NSError {
                errorPointer?.pointee = error
                return nil
            }
        }) { (result, error) in
            if let error = error {
                print("❌ Transaction failed: \(error.localizedDescription)")
                if let nsError = error as NSError? {
                    print("❌ Error domain: \(nsError.domain), code: \(nsError.code)")
                    print("❌ Error user info: \(nsError.userInfo)")

                    // Check for permission errors
                    if nsError.domain == "FIRFirestoreErrorDomain" && nsError.code == 7 {
                        completion(false, "Permission denied: You don't have permission to like this post")
                        return
                    }
                }
                completion(false, "Failed to update like: \(error.localizedDescription)")
                return
            }

            print("✅ Transaction successful")

            // Don't refresh posts - the UI has already been updated locally
            completion(true, nil)
        }
    }

    // Toggle like on a post without refreshing the entire list
    func toggleLikeWithoutRefresh(postId: String, userId: String, isLiked: Bool, completion: @escaping (Bool, String?) -> Void) {
        print("🔄 toggleLikeWithoutRefresh called with postId: \(postId), userId: \(userId), isLiked: \(isLiked)")

        // First check if the current user is the author of the post
        postsRef.document(postId).getDocument { [weak self] snapshot, error in
            guard let self = self else {
                completion(false, "Internal error")
                return
            }

            if let error = error {
                print("❌ Error fetching post: \(error.localizedDescription)")
                completion(false, "Error fetching post: \(error.localizedDescription)")
                return
            }

            guard let snapshot = snapshot, snapshot.exists else {
                print("❌ Post not found with ID: \(postId)")
                completion(false, "Post not found")
                return
            }

            guard let post = Post(document: snapshot) else {
                print("❌ Invalid post data for ID: \(postId)")
                completion(false, "Invalid post data")
                return
            }

            print("✅ Post found: \(post.id ?? "unknown")")
            print("📊 Current post data - likeCount: \(post.likeCount), likedBy: \(post.likedBy)")

            // Check if the current user is the author
            let isAuthor = post.authorId == self.authService.user?.uid
            print("🔍 Is current user the author? \(isAuthor)")

            if isAuthor {
                // If the user is the author, use the transaction method
                print("🔄 Using transaction method for author")
                self.toggleLikeWithTransaction(postId: postId, userId: userId, isLiked: isLiked, completion: completion)
            } else {
                // If the user is not the author, try the special like field update
                print("🔄 Using special like field update for non-author")
                self.createLikeFieldUpdate(postId: postId, userId: userId, isLiked: isLiked) { success, message in
                    if success {
                        // The createLikeFieldUpdate method already updates the post document
                        // No need to update it again here
                        print("✅ Like operation successful")
                        completion(true, nil)
                    } else {
                        // If the special update fails, pass the error to the caller
                        completion(false, message)
                    }
                }
            }
        }
    }

    // Toggle dislike on a post (with refresh)
    func toggleDislike(postId: String, completion: @escaping (Bool, String?) -> Void) {
        guard let currentUser = authService.user else {
            completion(false, "User not logged in")
            return
        }

        let userId = currentUser.uid

        // Get the post document
        postsRef.document(postId).getDocument { [weak self] snapshot, error in
            guard let self = self else { return }

            if let error = error {
                completion(false, "Error fetching post: \(error.localizedDescription)")
                return
            }

            guard let snapshot = snapshot, snapshot.exists else {
                completion(false, "Post not found")
                return
            }

            guard let post = Post(document: snapshot) else {
                completion(false, "Invalid post data")
                return
            }

            // Check if user already disliked the post
            let alreadyDisliked = post.dislikedBy.contains(userId)

            // Update the post data
            var updateData: [String: Any] = [:]

            if alreadyDisliked {
                // Remove dislike
                updateData["dislikeCount"] = FieldValue.increment(Int64(-1))
                updateData["dislikedBy"] = FieldValue.arrayRemove([userId])
            } else {
                // Add dislike
                updateData["dislikeCount"] = FieldValue.increment(Int64(1))
                updateData["dislikedBy"] = FieldValue.arrayUnion([userId])

                // If user previously liked, remove like
                if post.likedBy.contains(userId) {
                    updateData["likeCount"] = FieldValue.increment(Int64(-1))
                    updateData["likedBy"] = FieldValue.arrayRemove([userId])
                }
            }

            // Update the post in Firestore
            self.postsRef.document(postId).updateData(updateData) { error in
                if let error = error {
                    completion(false, "Error updating post: \(error.localizedDescription)")
                    return
                }

                // Refresh posts
                self.fetchAllPosts()
                self.fetchUserPosts()
                completion(true, nil)
            }
        }
    }

    // Toggle dislike on a post without refreshing the entire list
    func toggleDislikeWithoutRefresh(postId: String, userId: String, isDisliked: Bool, completion: @escaping (Bool, String?) -> Void) {
        // We don't need to check if the current user is authenticated
        // because we're using the passed userId parameter

        // Update data based on current state
        var updateData: [String: Any] = [:]

        if isDisliked {
            // Add dislike
            updateData["dislikeCount"] = FieldValue.increment(Int64(1))
            updateData["dislikedBy"] = FieldValue.arrayUnion([userId])

            // Check if we need to remove a like
            postsRef.document(postId).getDocument { [weak self] snapshot, error in
                guard let self = self else {
                    completion(false, "Internal error")
                    return
                }

                if let error = error {
                    completion(false, "Error fetching post: \(error.localizedDescription)")
                    return
                }

                guard let snapshot = snapshot, snapshot.exists,
                      let post = Post(document: snapshot) else {
                    completion(false, "Post not found")
                    return
                }

                // If user previously liked, remove like
                if post.likedBy.contains(userId) {
                    updateData["likeCount"] = FieldValue.increment(Int64(-1))
                    updateData["likedBy"] = FieldValue.arrayRemove([userId])
                }

                // Update the post in Firestore
                self.postsRef.document(postId).updateData(updateData) { error in
                    if let error = error {
                        completion(false, "Error updating post: \(error.localizedDescription)")
                        return
                    }

                    // Refresh posts in the background to keep data in sync
                    DispatchQueue.global(qos: .background).async {
                        self.fetchAllPosts()
                    }

                    completion(true, nil)
                }
            }
        } else {
            // Remove dislike
            updateData["dislikeCount"] = FieldValue.increment(Int64(-1))
            updateData["dislikedBy"] = FieldValue.arrayRemove([userId])

            // Update the post in Firestore
            postsRef.document(postId).updateData(updateData) { error in
                if let error = error {
                    completion(false, "Error updating post: \(error.localizedDescription)")
                    return
                }

                // Refresh posts in the background to keep data in sync
                DispatchQueue.global(qos: .background).async { [weak self] in
                    self?.fetchAllPosts()
                }

                completion(true, nil)
            }
        }
    }

    // MARK: - Comment Methods

    // Fetch comments for a post with real-time updates
    func fetchComments(for postId: String) {
        print("🔍 fetchComments called for post ID: \(postId)")

        // If we're already listening to this post's comments, don't set up a new listener
        if selectedPostId == postId && !comments.isEmpty {
            print("ℹ️ Already listening to comments for this post, skipping setup")
            return
        }

        isLoading = true
        selectedPostId = postId
        print("🔍 Setting selectedPostId to: \(postId)")

        // Remove any existing listener
        if commentsListener != nil {
            print("🔍 Removing existing comments listener")
            commentsListener?.remove()
        }

        // Clear previous comments
        print("🔍 Clearing previous comments array")
        comments = []

        // IMPORTANT: We're setting up a new listener for comments, but we need to make sure
        // this doesn't cause the comment section to close or the view to scroll back to the top

        // Set up a real-time listener for comments
        print("🔍 Setting up real-time listener for comments with postId: \(postId)")
        commentsListener = commentsRef.whereField("postId", isEqualTo: postId)
            .order(by: "timestamp", descending: false)
            .addSnapshotListener { [weak self] snapshot, error in
                guard let self = self else {
                    print("❌ Self reference lost in comments listener")
                    return
                }

                self.isLoading = false
                print("🔍 Comments listener fired for post ID: \(postId)")

                if let error = error {
                    print("❌ Error in comments listener: \(error.localizedDescription)")
                    if let nsError = error as NSError? {
                        print("❌ Error domain: \(nsError.domain), code: \(nsError.code)")
                    }
                    self.errorMessage = "Error fetching comments: \(error.localizedDescription)"
                    return
                }

                guard let snapshot = snapshot else {
                    print("❌ Snapshot is nil in comments listener")
                    self.comments = []
                    return
                }

                print("🔍 Received snapshot with \(snapshot.documents.count) comments")
                print("🔍 Document changes: \(snapshot.documentChanges.count)")

                // IMPORTANT: We need to update comments without triggering a full refresh of posts
                // This ensures the comment section stays open and the view doesn't jump

                // For a complete refresh, get all documents
                if self.comments.isEmpty {
                    print("🔍 Comments array is empty, doing complete refresh")
                    self.comments = snapshot.documents.compactMap { Comment(document: $0) }
                    self.comments.sort { $0.timestamp < $1.timestamp }
                    print("✅ Added \(self.comments.count) comments to array")

                    // Print comment details for debugging
                    for (index, comment) in self.comments.enumerated() {
                        print("📝 Comment \(index + 1): ID=\(comment.id ?? "nil"), Content=\(comment.content)")
                    }
                } else {
                    // Process document changes
                    print("🔍 Processing \(snapshot.documentChanges.count) document changes")
                    snapshot.documentChanges.forEach { change in
                        if change.type == .added {
                            print("🔍 Document ADDED: \(change.document.documentID)")
                            if let comment = Comment(document: change.document) {
                                // Remove existing comment with same ID if it exists
                                self.comments.removeAll { $0.id == comment.id }
                                // Add the new comment
                                DispatchQueue.main.async {
                                    self.comments.append(comment)
                                    // Sort comments by timestamp
                                    self.comments.sort { $0.timestamp < $1.timestamp }
                                    print("✅ Added new comment: \(comment.content)")
                                }
                            }
                        } else if change.type == .modified {
                            print("🔍 Document MODIFIED: \(change.document.documentID)")
                            if let comment = Comment(document: change.document) {
                                // Remove existing comment with same ID if it exists
                                self.comments.removeAll { $0.id == comment.id }
                                // Add the updated comment
                                DispatchQueue.main.async {
                                    self.comments.append(comment)
                                    // Sort comments by timestamp
                                    self.comments.sort { $0.timestamp < $1.timestamp }
                                    print("✅ Updated comment: \(comment.content)")
                                }
                            }
                        } else if change.type == .removed {
                            print("🔍 Document REMOVED: \(change.document.documentID)")
                            // Remove the comment
                            if let commentId = change.document.documentID as String? {
                                let countBefore = self.comments.count
                                DispatchQueue.main.async {
                                    self.comments.removeAll { $0.id == commentId }
                                }
                                let countAfter = self.comments.count
                                print("✅ Removed comment: \(countBefore - countAfter) comments removed")
                            }
                        }
                    }
                }

                print("🔍 Final comments count after listener update: \(self.comments.count)")

                // IMPORTANT: Do NOT call fetchAllPosts() here as it would reorder posts and cause UI jumps
                print("✅ IMPORTANT: Avoiding fetchAllPosts() to prevent reordering and UI jumps")
            }
    }

    // This deinit method is now handled at the top of the class

    // Add a comment to a post
    func addComment(to postId: String, content: String, completion: @escaping (Bool, String?) -> Void) {
        guard let currentUser = authService.user else {
            print("❌ addComment failed: User not logged in")
            completion(false, "User not logged in")
            return
        }

        print("🔍 Adding comment to post ID: \(postId)")
        print("🔍 Comment content: \"\(content)\"")
        print("🔍 User ID: \(currentUser.uid)")
        print("🔍 User email: \(currentUser.email ?? "Unknown")")

        isLoading = true

        // Get user's full name from profile if available
        let authorName: String
        if let userProfile = authService.userProfile {
            authorName = userProfile.fullName
        } else {
            // Fallback to email if profile not available
            authorName = currentUser.email?.components(separatedBy: "@").first ?? "Unknown User"
        }

        // Create comment data
        let commentData: [String: Any] = [
            "postId": postId,
            "authorId": currentUser.uid,
            "authorEmail": currentUser.email ?? "Unknown",
            "authorName": authorName,
            "content": content.trimmingCharacters(in: .whitespacesAndNewlines),
            "timestamp": FieldValue.serverTimestamp()
        ]

        print("🔍 Comment data prepared: \(commentData)")

        // Add comment to Firestore
        print("🔍 Adding comment document to Firestore 'comments' collection")
        let docRef = commentsRef.addDocument(data: commentData)
        print("🔍 Comment document reference created with ID: \(docRef.documentID)")

        // Create a server comment object with the real document ID
        let serverComment = Comment(
            id: docRef.documentID,
            postId: postId,
            authorId: currentUser.uid,
            authorEmail: currentUser.email ?? "Unknown",
            authorName: authorName,
            content: content.trimmingCharacters(in: .whitespacesAndNewlines),
            timestamp: Date()
        )

        // Update comment count on the post
        self.postsRef.document(postId).updateData([
            "commentCount": FieldValue.increment(Int64(1))
        ]) { [weak self] error in
            guard let self = self else { return }

            self.isLoading = false

            if let error = error {
                print("❌ Error updating post comment count: \(error.localizedDescription)")
                if let nsError = error as NSError? {
                    print("❌ Error domain: \(nsError.domain), code: \(nsError.code)")

                    // Check for permission errors
                    if nsError.domain == "FIRFirestoreErrorDomain" && nsError.code == 7 {
                        print("❌ PERMISSION DENIED: User does not have permission to update post comment count")
                        completion(false, "Permission denied: You don't have permission to update the post.")
                        return
                    }
                }
                completion(false, "Error updating post: \(error.localizedDescription)")
                return
            }

            print("✅ Post comment count successfully updated")

            // Update the post's comment count in the local posts array WITHOUT reordering posts
            DispatchQueue.main.async {
                // Update the post in the posts array
                if let index = self.posts.firstIndex(where: { $0.id == postId }) {
                    var updatedPost = self.posts[index]
                    updatedPost.commentCount += 1

                    // Important: Update the post in place without changing its position in the array
                    self.posts[index] = updatedPost
                    print("✅ Updated post in posts array with new comment count: \(updatedPost.commentCount)")
                }

                // Also update in userPosts if present
                if let index = self.userPosts.firstIndex(where: { $0.id == postId }) {
                    var updatedPost = self.userPosts[index]
                    updatedPost.commentCount += 1
                    self.userPosts[index] = updatedPost
                    print("✅ Updated post in userPosts array with new comment count: \(updatedPost.commentCount)")
                }

                print("✅ Updated local post arrays with new comment count")
                print("✅ IMPORTANT: Avoiding fetchAllPosts() to prevent reordering and UI jumps")

                // CRITICAL: Do NOT call fetchAllPosts() here as it would reorder posts and cause UI jumps

                // Notify the UI to keep the comment section open
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    // Delay the notification to ensure it's processed after any UI updates
                    NotificationCenter.default.post(
                        name: NSNotification.Name("KeepCommentSectionOpen"),
                        object: nil,
                        userInfo: ["postId": postId]
                    )
                }

                completion(true, nil)
            }
        }
    }

    // Delete a comment
    func deleteComment(commentId: String, postId: String, completion: @escaping (Bool, String?) -> Void) {
        guard let currentUser = authService.user else {
            print("❌ deleteComment failed: User not logged in")
            completion(false, "User not logged in")
            return
        }

        print("🔍 Attempting to delete comment ID: \(commentId) from post ID: \(postId)")
        print("🔍 Current user ID: \(currentUser.uid)")

        isLoading = true

        // First check if the comment belongs to the current user
        print("🔍 Checking if comment belongs to current user")
        commentsRef.document(commentId).getDocument { [weak self] snapshot, error in
            guard let self = self else {
                print("❌ Self reference lost in deleteComment completion")
                return
            }

            if let error = error {
                print("❌ Error fetching comment: \(error.localizedDescription)")
                if let nsError = error as NSError? {
                    print("❌ Error domain: \(nsError.domain), code: \(nsError.code)")
                }
                self.isLoading = false
                completion(false, "Error fetching comment: \(error.localizedDescription)")
                return
            }

            guard let snapshot = snapshot, snapshot.exists else {
                print("❌ Comment not found with ID: \(commentId)")
                self.isLoading = false
                completion(false, "Comment not found")
                return
            }

            let data = snapshot.data() ?? [:]
            guard let authorId = data["authorId"] as? String else {
                print("❌ Invalid comment data - missing authorId")
                self.isLoading = false
                completion(false, "Invalid comment data")
                return
            }

            print("🔍 Comment author ID: \(authorId)")
            print("🔍 Current user ID: \(currentUser.uid)")

            // Verify the comment belongs to the current user
            guard authorId == currentUser.uid else {
                print("❌ Permission denied - comment belongs to another user")
                self.isLoading = false
                completion(false, "You can only delete your own comments")
                return
            }

            print("✅ Comment ownership verified")

            // Delete the comment
            print("🔍 Deleting comment document from Firestore")
            self.commentsRef.document(commentId).delete { error in
                if let error = error {
                    print("❌ Error deleting comment: \(error.localizedDescription)")
                    if let nsError = error as NSError? {
                        print("❌ Error domain: \(nsError.domain), code: \(nsError.code)")
                    }
                    self.isLoading = false
                    completion(false, "Error deleting comment: \(error.localizedDescription)")
                    return
                }

                print("✅ Comment document successfully deleted")
                print("🔍 Now updating comment count on post document")

                // Update comment count on the post
                self.postsRef.document(postId).updateData([
                    "commentCount": FieldValue.increment(Int64(-1))
                ]) { error in
                    self.isLoading = false

                    if let error = error {
                        print("❌ Error updating post comment count: \(error.localizedDescription)")
                        if let nsError = error as NSError? {
                            print("❌ Error domain: \(nsError.domain), code: \(nsError.code)")
                        }
                        completion(false, "Error updating post: \(error.localizedDescription)")
                        return
                    }

                    print("✅ Post comment count successfully updated")

                    // Update the local UI immediately
                    DispatchQueue.main.async {
                        // Remove the comment from the comments array
                        self.comments.removeAll { $0.id == commentId }

                        // Update the post's comment count in the local posts array
                        if let index = self.posts.firstIndex(where: { $0.id == postId }) {
                            var updatedPost = self.posts[index]
                            updatedPost.commentCount = max(0, updatedPost.commentCount - 1)
                            self.posts[index] = updatedPost
                        }

                        // Also update in userPosts if present
                        if let index = self.userPosts.firstIndex(where: { $0.id == postId }) {
                            var updatedPost = self.userPosts[index]
                            updatedPost.commentCount = max(0, updatedPost.commentCount - 1)
                            self.userPosts[index] = updatedPost
                        }

                        print("✅ Updated local arrays after comment deletion")
                        completion(true, nil)
                    }
                }
            }
        }
    }
}
