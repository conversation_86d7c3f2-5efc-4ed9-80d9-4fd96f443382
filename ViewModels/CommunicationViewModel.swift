import Foundation
import Combine

class CommunicationViewModel: ObservableObject {
    // Removed dailyScores
    @Published var isSubscribed: Bool = false

    private var subscriptionService = SubscriptionService.shared
    private var cancellables = Set<AnyCancellable>()

    // UserDefaults key
    private let subscriptionKey = "isSubscribed"

    // New communication stages for the Effective Communication Map
    let communicationStages = [
        CommunicationStage(
            number: 1,
            title: "Learn the Fundamentals",
            description: "Understand what makes a communicator effective",
            goals: "Understand what makes a communicator effective: clarity, empathy, tone, and structure.",
            activities: [
                StageActivity(
                    title: "Getting Started Audio",
                    description: "Listen to the fundamentals of effective communication.",
                    type: .audio,
                    content: "Fundamentals of Effective Communication"
                ),
                StageActivity(
                    title: "Effective Communication",
                    description: "Explore an interactive map of effective communication concepts.",
                    type: .custom,
                    content: "EffectiveCommunicationMap"
                ),
                StageActivity(
                    title: "Record Yourself to Improve",
                    description: "Record yourself everyday explaining any topic and track yourself",
                    type: .recording,
                    content: ""
                )
            ]
        ),
        CommunicationStage(
            number: 2,
            title: "Build a Powerful Vocabulary",
            description: "Expand your vocabulary naturally and contextually",
            goals: "Make sure to expand your vocabulary naturally and contextually. When you get a new word, practice saying it out loud.",
            activities: [
                StageActivity(
                    title: "Vocabulary Cards",
                    description: "One side of the card is for the word itself and meaning, the other for example sentences and practice.",
                    type: .vocabulary,
                    content: "Practice using new words in different contexts to solidify your understanding."
                )
            ]
        ),
        CommunicationStage(
            number: 3,
            title: "Practice Daily Life Communication Phase 1",
            description: "Practice everyday conversations in common situations",
            goals: "Master communication in everyday situations like stores, restaurants, and pharmacies.",
            activities: [
                StageActivity(
                    title: "At a Grocery Store",
                    description: "Practice asking about prices, product availability, or assistance.",
                    type: .lesson,
                    content: "You're at a grocery store looking for specific items. Practice asking about prices, product availability, and for assistance."
                ),
                StageActivity(
                    title: "At a Pharmacy",
                    description: "Practice asking about medications and their side effects.",
                    type: .lesson,
                    content: "You're at a pharmacy picking up a prescription. Ask about your medication and possible side effects."
                ),
                StageActivity(
                    title: "At a Restaurant",
                    description: "Practice ordering food, requesting modifications, asking for the bill.",
                    type: .lesson,
                    content: "You are at a restaurant. Order your meal, request any modifications, and interact with the server."
                ),
                StageActivity(
                    title: "At a Retail Store",
                    description: "Practice asking about sizes, discounts, or return policies.",
                    type: .lesson,
                    content: "You're shopping at a retail store. Ask about sizes, current discounts, and the store's return policy."
                )
            ]
        ),
        CommunicationStage(
            number: 4,
            title: "Sentence Construction & Clarity",
            description: "Learn to write clearly and speak with precision",
            goals: "Practice sentence writing. Learn to write clearly and speak with precision.",
            activities: [
                StageActivity(
                    title: "Sentence Rewriting",
                    description: "Rewrite complex sentences in simpler language.",
                    type: .chat,
                    content: "Practice rewriting complex sentences in simpler, more direct language."
                ),
                StageActivity(
                    title: "Reading Article",
                    description: "Read articles and practice paraphrasing the content.",
                    type: .article,
                    content: "Read short articles and practice paraphrasing the content in your own words."
                )
            ]
        ),
        CommunicationStage(
            number: 5,
            title: "Practice Daily Life Communication Phase 2",
            description: "Continue practicing everyday conversations in different settings",
            goals: "Master communication in more complex daily situations like banking and traveling.",
            activities: [
                StageActivity(
                    title: "At a Bank",
                    description: "Practice inquiring about account details, transactions, or loans.",
                    type: .lesson,
                    content: "You're at a bank and need to inquire about your account details, recent transactions, or loan options."
                ),
                StageActivity(
                    title: "While Commuting",
                    description: "Practice asking for directions, talking to a taxi driver or ride share driver.",
                    type: .lesson,
                    content: "You're in a taxi or rideshare. Ask the driver for directions or engage in conversation during your commute."
                ),
                StageActivity(
                    title: "While Traveling",
                    description: "Practice communicating with airport staff, booking hotels, asking for recommendations.",
                    type: .lesson,
                    content: "You're traveling abroad. Practice communicating with airport staff, hotel receptionist, or local guide."
                ),
                StageActivity(
                    title: "At a Doctor's Office",
                    description: "Practice describing symptoms, understanding prescriptions.",
                    type: .lesson,
                    content: "You're visiting a doctor. Describe your symptoms and ask questions about your prescription."
                )
            ]
        ),
        CommunicationStage(
            number: 6,
            title: "Reduce Filler Words",
            description: "Speak with purpose, avoid 'um,' 'like,' 'you know'",
            goals: "Speak with purpose, avoid 'um,' 'like,' 'you know.'",
            activities: [
                StageActivity(
                    title: "Understanding Filler Words",
                    description: "Learn about filler words and how to reduce them.",
                    type: .audio,
                    content: "Understanding and Reducing Filler Words"
                ),
                StageActivity(
                    title: "Article Rephrasing",
                    description: "Read an article and rephrase it in your own words without filler words.",
                    type: .article,
                    content: "Read an article and practice rephrasing the content without using filler words."
                )
            ]
        ),
        CommunicationStage(
            number: 7,
            title: "Education Communication",
            description: "Improve your communication in educational settings",
            goals: "Learn to communicate effectively in classroom settings, during exams, and in libraries.",
            activities: [
                StageActivity(
                    title: "In a Classroom",
                    description: "Practice asking questions, participating in discussions.",
                    type: .lesson,
                    content: "You're in a classroom setting. Practice asking insightful questions and participating in discussions."
                ),
                StageActivity(
                    title: "During an Exam or Assessment",
                    description: "Practice clarifying doubts with the instructor.",
                    type: .lesson,
                    content: "You're taking an important exam and need clarification on a question without seeking the answer itself."
                ),
                StageActivity(
                    title: "At a Library",
                    description: "Practice asking for book recommendations or assistance.",
                    type: .lesson,
                    content: "You're at a library researching a topic. Practice asking for book recommendations and assistance."
                )
            ]
        ),
        CommunicationStage(
            number: 8,
            title: "Master Storytelling",
            description: "Make stories memorable and structured",
            goals: "Make stories memorable and structured. Learn to engage your audience through effective storytelling.",
            activities: [
                StageActivity(
                    title: "The Art of Storytelling",
                    description: "Listen to the fundamentals of effective storytelling.",
                    type: .audio,
                    content: "The Art and Practice of Storytelling"
                ),
                StageActivity(
                    title: "Key Story Elements and Structure",
                    description: "Learn about essential elements and structure for effective storytelling.",
                    type: .custom,
                    content: "StoryElements"
                ),
                StageActivity(
                    title: "Personal Story Practice",
                    description: "Record yourself telling a personal story with structure and impact. Focus on having a clear beginning, middle, and end to your story.",
                    type: .recording,
                    content: ""
                )
            ]
        ),
        CommunicationStage(
            number: 9,
            title: "Emergency Communication",
            description: "Learn to communicate effectively in urgent situations",
            goals: "Master clear, concise communication in emergency and high-stress situations.",
            activities: [
                StageActivity(
                    title: "Calling Emergency Services",
                    description: "Practice reporting an accident, seeking help.",
                    type: .lesson,
                    content: "You need to call emergency services. Practice reporting an accident and seeking help."
                ),
                StageActivity(
                    title: "Explaining a Situation to Authorities",
                    description: "Practice talking to police, security, or legal representatives.",
                    type: .lesson,
                    content: "You need to explain a situation to authorities. Practice providing clear information."
                ),
                StageActivity(
                    title: "At a Hospital",
                    description: "Practice asking for urgent medical assistance.",
                    type: .lesson,
                    content: "You're at a hospital emergency room. Practice explaining your medical emergency and asking for assistance."
                ),
                StageActivity(
                    title: "Serious or Difficult Conversations",
                    description: "Practice apologizing, confronting issues, breaking bad news, or having emotional talks.",
                    type: .lesson,
                    content: "You need to have a difficult conversation. Practice handling emotionally charged situations."
                )
            ]
        ),
        CommunicationStage(
            number: 10,
            title: "Social Communication",
            description: "Enhance your social interaction skills",
            goals: "Develop skills for effective communication in various social settings and gatherings.",
            activities: [
                StageActivity(
                    title: "Meeting New People",
                    description: "Practice introducing oneself, making small talk.",
                    type: .lesson,
                    content: "You're at an event where you don't know anyone. Practice introducing yourself and making small talk."
                ),
                StageActivity(
                    title: "At a Party or Social Gathering",
                    description: "Practice engaging in casual conversations.",
                    type: .lesson,
                    content: "You're at a house party with a mix of friends and strangers. Practice engaging in casual conversations."
                ),
                StageActivity(
                    title: "With Friends and Family",
                    description: "Practice expressing thoughts, resolving conflicts.",
                    type: .lesson,
                    content: "You're having a gathering with close friends or family. Practice expressing your thoughts clearly and resolving conflicts."
                ),
                StageActivity(
                    title: "At a Wedding or Family Event",
                    description: "Practice giving a speech, congratulating others.",
                    type: .lesson,
                    content: "You're attending a wedding or family celebration. Practice giving a congratulatory speech or interacting with relatives."
                )
            ]
        ),
        CommunicationStage(
            number: 11,
            title: "Charisma/Rizz/Romance",
            description: "Develop charm, romantic communication, and interpersonal skills",
            goals: "Master the art of charming communication, flirting, and building romantic connections.",
            activities: [
                StageActivity(
                    title: "First Meeting",
                    description: "Practice breaking the ice, keeping conversation engaging and flowing naturally.",
                    type: .lesson,
                    content: "You've just met someone new at a social event. Practice breaking the ice and keeping the conversation engaging."
                ),
                StageActivity(
                    title: "Honeymoon Phase",
                    description: "Practice establishing connection, complimenting genuinely, flirting respectfully.",
                    type: .lesson,
                    content: "You're in the early stages of dating someone. Practice establishing connection and giving genuine compliments."
                ),
                StageActivity(
                    title: "Romance 'Reality Check'",
                    description: "Practice resolving conflicts, planning future goals, providing emotional support.",
                    type: .lesson,
                    content: "You're in a long-term relationship. Practice resolving conflicts and providing emotional support."
                ),
                StageActivity(
                    title: "Being Charming in Front of People",
                    description: "Practice confidence, humor, storytelling, and engaging everyone in conversation.",
                    type: .lesson,
                    content: "You're at a dinner party with several people. Practice being charming and engaging everyone in conversation."
                )
            ]
        ),
        CommunicationStage(
            number: 12,
            title: "Professional Communication",
            description: "Enhance your workplace communication skills",
            goals: "Master effective communication in professional settings to advance your career.",
            activities: [
                StageActivity(
                    title: "At Work",
                    description: "Practice discussing tasks, attending meetings, asking for feedback.",
                    type: .lesson,
                    content: "You're in a workplace setting. Practice discussing projects and asking for feedback."
                ),
                StageActivity(
                    title: "During Interviews",
                    description: "Practice answering questions, negotiating salary, discussing roles.",
                    type: .lesson,
                    content: "You're interviewing for a job in your field. Practice answering questions and negotiating terms."
                ),
                StageActivity(
                    title: "On a Business Call",
                    description: "Practice scheduling meetings, handling customer inquiries, problem-solving.",
                    type: .lesson,
                    content: "You're on a business call. Practice scheduling meetings and addressing customer concerns."
                ),
                StageActivity(
                    title: "At Networking Events",
                    description: "Practice introducing oneself, discussing career goals.",
                    type: .lesson,
                    content: "You're at an industry networking event. Practice introducing yourself and discussing your career goals."
                ),
                StageActivity(
                    title: "During a Presentation",
                    description: "Practice explaining ideas, answering questions.",
                    type: .lesson,
                    content: "You're giving a presentation to a team or client. Practice explaining your ideas clearly and answering questions."
                ),
                StageActivity(
                    title: "While Negotiating",
                    description: "Practice settling deals, discussing terms with vendors or partners.",
                    type: .lesson,
                    content: "You're negotiating a business deal. Practice discussing terms and reaching an agreement."
                )
            ]
        )
    ]

    let lessonCategories = [
        LessonCategory(
            title: "Daily Life Communication",
            description: "Practice everyday conversations and interactions",
            lessons: [
                LessonPlan(
                    title: "At a Grocery Store",
                    description: "Asking for prices, product availability, or assistance.",
                    prompt: "You're at a grocery store looking for specific items. Practice asking the AI store employee about prices, product availability, and for assistance.",
                    rules: "- Teach vocabulary for food items and grocery shopping\n- Practice making polite requests for assistance\n- Guide on comparing prices and quality\n- Focus on clear articulation of needs"
                ),
                LessonPlan(
                    title: "At a Restaurant",
                    description: "Ordering food, requesting modifications, asking for the bill.",
                    prompt: "You are at a restaurant. Order your meal, request any modifications, and interact with the AI server.",
                    rules: "- Teach polite table manners\n- Emphasize proper ways to address restaurant staff\n- Guide on making special requests or modifications\n- Practice vocabulary for different cuisines and dishes"
                ),
                LessonPlan(
                    title: "At a Doctor's Office",
                    description: "Describing symptoms, understanding prescriptions.",
                    prompt: "You're visiting a doctor. Describe your symptoms to the AI doctor and ask questions about your prescription.",
                    rules: "- Teach medical vocabulary\n- Practice clearly describing symptoms\n- Focus on asking clarifying questions\n- Guide on expressing levels of discomfort appropriately"
                ),
                LessonPlan(
                    title: "At a Pharmacy",
                    description: "Asking about medications and their side effects.",
                    prompt: "You're at a pharmacy picking up a prescription. Ask the AI pharmacist about your medication and possible side effects.",
                    rules: "- Focus on asking clear questions about medications\n- Teach vocabulary related to dosage and side effects\n- Practice requesting clarification on instructions"
                ),
                LessonPlan(
                    title: "At a Bank",
                    description: "Inquiring about account details, transactions, or loans.",
                    prompt: "You're at a bank and need to inquire about your account details, recent transactions, or loan options. Speak with the AI bank representative.",
                    rules: "- Teach financial vocabulary\n- Practice formal business interactions\n- Guide on maintaining privacy while discussing finances\n- Focus on clear questions about complex topics"
                ),
                LessonPlan(
                    title: "While Commuting",
                    description: "Asking for directions, talking to a taxi driver or ride share driver.",
                    prompt: "You're in a taxi or rideshare. Ask the AI driver for directions or engage in conversation during your commute."
                ),
                LessonPlan(
                    title: "At a Retail Store",
                    description: "Asking about sizes, discounts, or return policies.",
                    prompt: "You're shopping at a retail store. Ask the AI sales associate about sizes, current discounts, and the store's return policy.",
                    rules: "- Practice vocabulary for clothing and retail items\n- Guide on polite negotiations\n- Teach phrases for requesting assistance\n- Focus on comparing options"
                ),
                LessonPlan(
                    title: "While Traveling",
                    description: "Communicating with airport staff, booking hotels, asking for recommendations.",
                    prompt: "You're traveling abroad. Practice communicating with the AI airport staff, hotel receptionist, or local guide for recommendations."
                )
            ]
        ),
        LessonCategory(
            title: "Charisma/Rizz/Romance",
            description: "Develop charm, romantic communication, and interpersonal skills",
            lessons: [
                LessonPlan(
                    title: "First Meeting",
                    description: "Breaking the ice, getting to know someone, keeping the conversation engaging and flowing naturally.",
                    prompt: "You've just met someone new at a social event. Practice breaking the ice and keeping the conversation engaging with the AI.",
                    rules: "- Focus on open-ended questions\n- Guide on active listening techniques\n- Teach transitions between topics\n- Practice showing genuine interest"
                ),
                LessonPlan(
                    title: "Honeymoon Phase",
                    description: "Establish connection with interesting topics, complimenting genuinely, flirting in a fun and respectful way, expressing emotions.",
                    prompt: "You're in the early stages of dating someone. Practice establishing connection, giving genuine compliments, and expressing your emotions to the AI.",
                    rules: "- Guide on genuine compliments vs flattery\n- Teach respectful flirting techniques\n- Focus on expressing emotions clearly\n- Practice finding common interests"
                ),
                LessonPlan(
                    title: "Romance \"Reality Check\"",
                    description: "Expressing love and appreciation, resolving conflicts or misunderstandings, planning future goals together, discussing finances and responsibilities, comforting and supporting each other emotionally.",
                    prompt: "You're in a long-term relationship. Practice resolving conflicts, planning future goals, and providing emotional support with your AI partner.",
                    rules: "- Teach conflict resolution strategies\n- Guide on clear expression of needs\n- Focus on active listening during disagreements\n- Practice validation and emotional support techniques"
                ),
                LessonPlan(
                    title: "Being Charming in Front of People",
                    description: "Having confidence and positive body language, making people laugh with good humor, telling interesting stories or experiences, complimenting without overdoing it, engaging everyone in the conversation.",
                    prompt: "You're at a dinner party with several people. Practice being charming, telling stories, and engaging everyone in conversation.",
                    rules: "- Guide on inclusive conversation techniques\n- Teach storytelling structure\n- Focus on reading social cues\n- Practice appropriate humor and timing"
                )
            ]
        ),
        LessonCategory(
            title: "Social Communication",
            description: "Enhance your social interaction skills",
            lessons: [
                LessonPlan(
                    title: "Meeting New People",
                    description: "Introducing oneself, making small talk.",
                    prompt: "You're at an event where you don't know anyone. Practice introducing yourself and making small talk with the AI.",
                    rules: "- Teach appropriate self-introduction techniques\n- Guide on finding common interests\n- Focus on asking engaging questions\n- Practice active listening"
                ),
                LessonPlan(
                    title: "At a Party or Social Gathering",
                    description: "Engaging in casual conversations.",
                    prompt: "You're at a house party with a mix of friends and strangers. Practice engaging in casual conversations with the AI partygoers."
                ),
                LessonPlan(
                    title: "With Friends and Family",
                    description: "Expressing thoughts, resolving conflicts.",
                    prompt: "You're having a gathering with close friends or family. Practice expressing your thoughts clearly and resolving any conflicts that arise.",
                    rules: "- Guide on setting boundaries respectfully\n- Teach handling difficult topics with loved ones\n- Focus on expressing disagreement without aggression\n- Practice de-escalation techniques"
                ),
                LessonPlan(
                    title: "At a Wedding or Family Event",
                    description: "Giving a speech, congratulating others.",
                    prompt: "You're attending a wedding or family celebration. Practice giving a congratulatory speech or interacting with relatives you haven't seen in a while."
                )
            ]
        ),
        LessonCategory(
            title: "Educational Communication",
            description: "Improve your communication in educational settings",
            lessons: [
                LessonPlan(
                    title: "In a Classroom",
                    description: "Asking questions, participating in discussions.",
                    prompt: "You're in a classroom setting. Practice asking insightful questions and participating in a discussion led by the AI instructor.",
                    rules: "- Guide on formulating clear questions\n- Teach techniques for meaningful contributions to discussions\n- Focus on academic vocabulary and discourse\n- Practice giving and receiving feedback"
                ),
                LessonPlan(
                    title: "During an Exam or Assessment",
                    description: "Clarifying doubts with the instructor.",
                    prompt: "You're taking an important exam and need clarification on a question. Practice asking the AI instructor for clarification without seeking the answer itself."
                ),
                LessonPlan(
                    title: "At a Library",
                    description: "Asking for book recommendations or assistance.",
                    prompt: "You're at a library researching a topic. Practice asking the AI librarian for book recommendations and assistance finding resources."
                )
            ]
        ),
        LessonCategory(
            title: "Professional Communication",
            description: "Enhance your workplace communication skills",
            lessons: [
                LessonPlan(
                    title: "At Work",
                    description: "Discussing tasks, attending meetings, asking for feedback.",
                    prompt: "You're in a workplace setting. Practice discussing projects, participating in meetings, and asking for feedback from the AI colleague or manager.",
                    rules: "- Focus on clear and concise professional language\n- Teach vocabulary for task management\n- Guide on requesting constructive feedback\n- Practice diplomatic communication with colleagues"
                ),
                LessonPlan(
                    title: "During Interviews",
                    description: "Answering questions, negotiating salary, discussing roles.",
                    prompt: "You're interviewing for a job in your field. Practice answering the AI interviewer's questions and negotiating terms.",
                    rules: "- Teach concise responses to common interview questions\n- Guide on highlighting skills without sounding arrogant\n- Focus on professional salary negotiation tactics\n- Practice asking insightful questions about the role"
                ),
                LessonPlan(
                    title: "On a Business Call",
                    description: "Scheduling meetings, handling customer inquiries, problem-solving.",
                    prompt: "You're on a business call. Practice scheduling meetings, addressing customer concerns, or solving problems with the AI business contact."
                ),
                LessonPlan(
                    title: "At Networking Events",
                    description: "Introducing oneself, discussing career goals.",
                    prompt: "You're at an industry networking event. Practice introducing yourself to the AI professionals and discussing your career goals.",
                    rules: "- Guide on crafting concise professional introductions\n- Teach exchange of contact information\n- Focus on asking industry-specific questions\n- Practice memorable follow-up conversation"
                ),
                LessonPlan(
                    title: "During a Presentation",
                    description: "Explaining ideas, answering questions.",
                    prompt: "You're giving a presentation to a team or client. Practice explaining your ideas clearly and answering questions from the AI audience."
                ),
                LessonPlan(
                    title: "While Negotiating",
                    description: "Settling deals, discussing terms with vendors or partners.",
                    prompt: "You're negotiating a business deal. Practice discussing terms and reaching an agreement with the AI business partner.",
                    rules: "- Teach setting clear boundaries and BATNA\n- Guide on identifying win-win solutions\n- Focus on managing emotions during tense negotiations\n- Practice listening for underlying interests"
                )
            ]
        ),
        LessonCategory(
            title: "Emergency Communication",
            description: "Learn to communicate effectively in urgent situations",
            lessons: [
                LessonPlan(
                    title: "Calling Emergency Services",
                    description: "Reporting an accident, seeking help.",
                    prompt: "You need to call emergency services. Practice reporting an accident and seeking help from the AI emergency operator.",
                    rules: "- Focus on clear, concise communication of critical information\n- Teach staying calm under pressure\n- Guide on prioritizing relevant details\n- Practice following operator instructions"
                ),
                LessonPlan(
                    title: "Explaining a Situation to Authorities",
                    description: "Talking to police, security, or legal representatives.",
                    prompt: "You need to explain a situation to authorities. Practice providing clear information to the AI police officer or security personnel."
                ),
                LessonPlan(
                    title: "At a Hospital",
                    description: "Asking for urgent medical assistance.",
                    prompt: "You're at a hospital emergency room. Practice explaining your medical emergency to the AI medical staff and asking for assistance."
                ),
                LessonPlan(
                    title: "Serious or Difficult Conversations",
                    description: "Apologizing for a mistake, confronting someone about an issue, breaking bad news, asking for a favor or help, having a deep emotional talk.",
                    prompt: "You need to have a difficult conversation. Practice apologizing, confronting an issue, breaking bad news, or having a deep emotional talk with the AI.",
                    rules: "- Guide on using 'I' statements for confrontation\n- Teach empathetic communication techniques\n- Focus on active listening during emotional exchanges\n- Practice validating feelings while maintaining boundaries"
                )
            ]
        )
    ]

    init() {
        setupSubscriptionObserver()

        // Also listen for subscription change notifications
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleSubscriptionChange),
            name: NSNotification.Name("SubscriptionStatusChanged"),
            object: nil
        )
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    @objc private func handleSubscriptionChange(_ notification: Notification) {
        if let isSubscribed = notification.userInfo?["isSubscribed"] as? Bool {
            DispatchQueue.main.async {
                self.isSubscribed = isSubscribed
                print("CommunicationViewModel updated subscription state to: \(isSubscribed)")
            }
        }
    }

    private func setupSubscriptionObserver() {
        // Observe changes in subscription status from SubscriptionService
        subscriptionService.$isSubscribed
            .sink { [weak self] isSubscribed in
                self?.isSubscribed = isSubscribed
                print("CommunicationViewModel subscription observer fired: \(isSubscribed)")
            }
            .store(in: &cancellables)
    }

    // MARK: - Persistence Methods

    // For testing purposes - clears all stored data
    func clearAllData() {
        // Clear all chat message history
        clearAllChatHistory()
    }

    // Clear chat history for a specific lesson
    func clearChatHistory(for lessonId: String) {
        UserDefaults.standard.removeObject(forKey: "chatMessages_\(lessonId)")
    }

    // Optimized method to clear all chat history for all lessons
    func clearAllChatHistory() {
        // Get all UserDefaults keys
        let userDefaults = UserDefaults.standard

        // More efficient approach - filter keys first, then remove in batch
        let chatKeys = userDefaults.dictionaryRepresentation().keys.filter { $0.hasPrefix("chatMessages_") }

        // Remove all chat message keys at once
        for key in chatKeys {
            userDefaults.removeObject(forKey: key)
        }

        // Force synchronize to ensure changes are saved immediately
        userDefaults.synchronize()
    }

    // MARK: - Subscription Methods

    func toggleSubscription() {
        // This method is only for development/testing
        // In production, subscription should only be set through RevenueCat
        #if DEBUG
        subscriptionService.isSubscribed.toggle()
        #endif
    }

    private func saveSubscriptionStatus() {
        UserDefaults.standard.set(isSubscribed, forKey: subscriptionKey)
        UserDefaults.standard.synchronize()
    }

    private func loadSubscriptionStatus() {
        isSubscribed = UserDefaults.standard.bool(forKey: subscriptionKey)
    }
}
