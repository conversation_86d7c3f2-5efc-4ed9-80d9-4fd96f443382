import SwiftUI

// MARK: - Message Input View
struct MessageInputView: View {
    @Binding var messageText: String
    let speechManager: SpeechManager
    let onSend: () -> Void
    let onToggleRecording: () -> Void
    var onClearText: (() -> Void)? = nil

    @State private var isTextFieldActive = true
    @State private var pulseScale = 1.0
    @State private var pulseOpacity = 0.3
    @State private var stopButtonScale = 1.0
    @State private var stopButtonRotation = 0.0
    @State private var stopGlowOpacity = 0.0
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        VStack(spacing: 10) {
            // Text input or transcription display
            inputView

            // Controls row - fixed height
            controlsRow
                .frame(height: 60)
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(Color(.systemBackground))
        .frame(maxWidth: .infinity)
        .fixedSize(horizontal: false, vertical: true)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isTextFieldActive)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: speechManager.isRecording)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: messageText)
    }

    // MARK: - Component Views

    private var inputView: some View {
        Group {
            ZStack(alignment: .leading) {
                // Placeholder for minimum height
                Color.clear
                    .frame(maxWidth: .infinity, minHeight: 44)

                if speechManager.isRecording {
                    Text(speechManager.transcribedText.isEmpty ? "Listening..." : speechManager.transcribedText)
                        .padding(.vertical, 12)
                        .padding(.horizontal, 16)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(Color.red.opacity(0.1))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .strokeBorder(Color.red.opacity(0.3), lineWidth: 1)
                                )
                        )
                        .foregroundColor(colorScheme == .dark ? .white : .black)
                        .transition(.opacity)
                        .id("recording-\(speechManager.isRecording)-\(speechManager.transcribedText)")
                } else {
                    HStack(spacing: 12) {
                        TextField("Tap to talk", text: $messageText, axis: .vertical)
                            .padding(.vertical, 12)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .lineLimit(5)
                            .foregroundColor(.primary)
                            .disabled(true)
                            .onTapGesture {
                                onToggleRecording()
                            }
                    }
                    .padding(.horizontal, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(colorScheme == .dark ? Color(.systemGray6) : Color(.systemGray6))
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .strokeBorder(
                                        colorScheme == .dark ? Color.white.opacity(0.1) : Color.black.opacity(0.1),
                                        lineWidth: 0.5
                                    )
                            )
                    )
                    .transition(.opacity)
                    .id("textfield-\(speechManager.isRecording)")
                }
            }
            .frame(maxWidth: .infinity)
        }
    }

    private var controlsRow: some View {
        ZStack {
            // Center microphone button
            HStack {
                Spacer()
                microphoneButton
                Spacer()
            }

            // Left and right controls on opposite sides
            HStack {
                // Left section with clear button or empty space
                if !messageText.isEmpty || speechManager.isRecording {
                    clearButton
                        .transition(.opacity.combined(with: .scale))
                } else {
                    Color.clear
                        .frame(width: 70)
                }

                Spacer()

                // Right section with send button
                sendButton
            }
        }
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: messageText)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: speechManager.isRecording)
    }

    private var microphoneButton: some View {
        ZStack {
            // Enhanced pulsating background effect when recording
            if speechManager.isRecording {
                ZStack {
                    // Multiple pulsating circles for a richer effect
                    Circle()
                        .fill(Color.red.opacity(pulseOpacity * 0.7))
                        .frame(width: 65, height: 65)
                        .scaleEffect(pulseScale)

                    Circle()
                        .fill(Color.red.opacity(pulseOpacity * 0.5))
                        .frame(width: 55, height: 55)
                        .scaleEffect(pulseScale * 1.1)
                }
                .onAppear {
                    withAnimation(.easeInOut(duration: 1.2).repeatForever(autoreverses: true)) {
                        pulseScale = 1.25
                        pulseOpacity = 0.3
                    }
                }
            }

            Button(action: {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    onToggleRecording()
                }
            }) {
                ZStack {
                    // Stop button glow effect
                    if speechManager.isRecording {
                        Circle()
                            .fill(Color.red.opacity(stopGlowOpacity))
                            .frame(width: 48, height: 48)
                            .blur(radius: 5)
                    }

                    Image(systemName: speechManager.isRecording ? "stop.circle.fill" : "mic.circle.fill")
                        .font(.system(size: 34))
                        .foregroundStyle(speechManager.isRecording ? .red : .white)
                        .shadow(color: .black.opacity(0.2), radius: 3, x: 0, y: 2)
                        .padding(8)
                        .scaleEffect(speechManager.isRecording ? stopButtonScale : 1.0)
                        .rotationEffect(.degrees(speechManager.isRecording ? stopButtonRotation : 0))
                        .background(
                            Circle()
                                .fill(
                                    LinearGradient(
                                        colors: speechManager.isRecording ?
                                            [Color.red.opacity(0.2), Color.red.opacity(0.3)] :
                                            [Color.blue.opacity(0.7), Color.purple.opacity(0.7)],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                                .shadow(color: speechManager.isRecording ? .red.opacity(0.3) : .black.opacity(0.15), radius: 5, x: 0, y: 2)
                        )
                }
                .onAppear {
                    if speechManager.isRecording {
                        startStopButtonAnimations()
                    }
                }
                .onChange(of: speechManager.isRecording) { _, isRecording in
                    if isRecording {
                        startStopButtonAnimations()
                    } else {
                        // Reset animations when not recording
                        stopButtonScale = 1.0
                        stopButtonRotation = 0.0
                        stopGlowOpacity = 0.0
                    }
                }
            }
            .frame(width: 60, height: 60)
            .contentShape(Circle())
        }
    }

    // New function to start stop button animations
    private func startStopButtonAnimations() {
        // Pulse animation
        withAnimation(.easeInOut(duration: 0.8).repeatForever(autoreverses: true)) {
            stopButtonScale = 1.08
        }

        // Subtle rotation
        withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
            stopButtonRotation = 5
        }

        // Glow effect
        withAnimation(.easeInOut(duration: 0.7).repeatForever(autoreverses: true)) {
            stopGlowOpacity = 0.4
        }
    }

    private var clearButton: some View {
        Button(action: clearAction) {
            Text("Clear")
                .font(.system(size: 14, weight: .medium))
                .foregroundStyle(.white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    Capsule()
                        .fill(
                            LinearGradient(
                                colors: [Color.gray.opacity(0.5), Color.gray.opacity(0.7)],
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                )
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        }
    }

    private var sendButton: some View {
        Button(action: onSend) {
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: messageText.isEmpty && !speechManager.isRecording ?
                                [Color.gray.opacity(0.3), Color.gray.opacity(0.3)] :
                                [Color.blue.opacity(0.7), Color.purple.opacity(0.7)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 44, height: 44)
                    .shadow(color: .black.opacity(messageText.isEmpty && !speechManager.isRecording ? 0 : 0.2), radius: 3, x: 0, y: 2)

                Image(systemName: "arrow.up")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundStyle(messageText.isEmpty && !speechManager.isRecording ? Color.white.opacity(0.3) : .white)
            }
        }
        .disabled(messageText.isEmpty && !speechManager.isRecording)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: messageText.isEmpty && !speechManager.isRecording)
    }

    // MARK: - Actions

    private func clearAction() {
        // Set flag to ignore transcription updates during clearing
        onClearText?()

        // If recording is active, stop it first
        if speechManager.isRecording {
            speechManager.stopRecording()
        }

        // Clear texts immediately with animation
        messageText = ""
        speechManager.transcribedText = ""

        // Start recording after a short delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            // Reset texts again to ensure they're clear
            self.messageText = ""
            self.speechManager.transcribedText = ""

            // Start recording
            do {
                try self.speechManager.startRecording()
            } catch {
                print("Error starting recording: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - Chat Messages View
struct ChatMessagesView: View {
    let messages: [ChatMessage]
    let onMessageTap: (ChatMessage) -> Void
    @Environment(\.colorScheme) private var colorScheme
    @State private var hasAppeared = false
    @Binding var scrollToLatest: Bool

    var body: some View {
        ScrollView {
            ScrollViewReader { proxy in
                LazyVStack(alignment: .leading, spacing: 16) {
                    // Group messages by date for better organization
                    ForEach(groupedMessagesByDate.keys.sorted().reversed(), id: \.self) { date in
                        if let messagesForDate = groupedMessagesByDate[date] {
                            // Date header
                            if showDateHeader(for: date) {
                                dateHeader(for: date)
                                    .padding(.top, 10)
                                    .padding(.bottom, 6)
                            }

                            // Messages for this date
                            ForEach(messagesForDate) { message in
                                ChatBubbleView(message: message)
                                    .id(message.id)
                                    .onTapGesture {
                                        onMessageTap(message)
                                    }
                                    .transition(.asymmetric(
                                        insertion: .scale(scale: 0.9).combined(with: .opacity).animation(.spring(response: 0.4, dampingFraction: 0.7)),
                                        removal: .opacity.animation(.easeOut(duration: 0.2))
                                    ))
                            }
                        }
                    }

                    // Empty view at the bottom to scroll to
                    Color.clear
                        .frame(height: 15)
                        .id("bottomID")
                }
                .padding(.horizontal)
                .padding(.vertical, 8)
                .onChange(of: messages.count) { _, _ in
                    scrollToBottom(proxy: proxy)
                }
                .onChange(of: scrollToLatest) { _, shouldScroll in
                    if shouldScroll {
                        scrollToBottom(proxy: proxy)
                        // Reset the flag after scrolling
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            scrollToLatest = false
                        }
                    }
                }
                .onAppear {
                    // Use a small delay to ensure view is fully rendered before scrolling
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        scrollToBottom(proxy: proxy)
                    }
                    hasAppeared = true
                }
            }
        }
        .scrollIndicators(.hidden)
        .background(
            // Simplified background for better performance
            Color(.systemBackground)
        )
    }

    // MARK: - Helper Properties and Methods

    // Cached message grouping for better performance
    private var groupedMessagesByDate: [Date: [ChatMessage]] {
        // Use lazy evaluation to improve performance
        let calendar = Calendar.current
        return Dictionary(grouping: messages) { message in
            calendar.startOfDay(for: message.timestamp)
        }
    }

    // Determine if we should show a date header
    private func showDateHeader(for date: Date) -> Bool {
        // Always show date headers to improve message organization
        return true
    }

    // Date header view
    private func dateHeader(for date: Date) -> some View {
        // Format the date appropriately
        let dateText = formatDate(date)

        return Text(dateText)
            .font(.system(size: 13, weight: .medium))
            .foregroundColor(.secondary)
            .frame(maxWidth: .infinity, alignment: .center)
            .padding(.vertical, 4)
            .padding(.horizontal, 16)
            .background(
                Capsule()
                    .fill(colorScheme == .dark ? Color(.systemGray6) : Color(.systemGray6))
            )
    }

    // Format the date for display
    private func formatDate(_ date: Date) -> String {
        let today = Calendar.current.startOfDay(for: Date())
        let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: today)!

        if Calendar.current.isDate(date, inSameDayAs: today) {
            return "Today"
        } else if Calendar.current.isDate(date, inSameDayAs: yesterday) {
            return "Yesterday"
        } else {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .none
            return formatter.string(from: date)
        }
    }

    // Helper function to ensure consistent scroll behavior
    private func scrollToBottom(proxy: ScrollViewProxy) {
        withAnimation(.easeOut(duration: 0.2)) {
            proxy.scrollTo("bottomID", anchor: .bottom)
        }
    }
}

// MARK: - Main View
struct TextChatView: View {
    let lesson: LessonPlan
    var viewModel: CommunicationViewModel
    @StateObject private var geminiViewModel = GeminiViewModel()
    @StateObject var speechManager = SpeechManager()
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.scenePhase) private var scenePhase

    // State variables for chat
    @State private var messageText = ""
    @State private var chatMessages: [ChatMessage] = []
    // Score-related state variables removed
    @State private var conversationStartTime: Date?
    @State private var conversationDuration: TimeInterval = 0
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var ignoreTranscriptionUpdates = false
    @State private var showTypingIndicator = false
    @State private var showPrompt = true
    @State private var hasShownPromptHint = false
    @State private var viewHasAppeared = false
    @State private var scrollToLatestMessage = false
    @State private var showingClearConfirmation = false

    // Computed properties for error handling
    private var isRateLimitError: Bool {
        if let error = lastError as NSError?, error.domain == "GeminiViewModel" && error.code == 429 {
            return true
        }
        return false
    }

    private var errorTitle: String {
        isRateLimitError ? "Daily Limit Reached" : "Error"
    }

    // Store the last error for checking error type
    @State private var lastError: Error?

    init(lesson: LessonPlan, viewModel: CommunicationViewModel) {
        self.lesson = lesson
        self.viewModel = viewModel

        // Initialize with any saved chat messages from UserDefaults
        let lessonId = lesson.id.uuidString
        if let savedMessages = Self.loadChatMessages(for: lessonId) {
            _chatMessages = State(initialValue: savedMessages)
        }
    }

    // Static version of loadChatMessages for use in init
    private static func loadChatMessages(for lessonId: String) -> [ChatMessage]? {
        guard let data = UserDefaults.standard.data(forKey: "chatMessages_\(lessonId)") else {
            return nil
        }

        do {
            let decoder = JSONDecoder()
            let messages = try decoder.decode([ChatMessage].self, from: data)
            return messages
        } catch {
            print("Error loading chat messages in init: \(error.localizedDescription)")
            return nil
        }
    }

    var body: some View {
        ZStack(alignment: .bottom) {
            // Background with subtle pattern
            backgroundView

            VStack(spacing: 0) {
                // Lesson prompt section
                promptSection

                // Chat messages area with improved styling
                messagesSection

                // Input area with gradient divider
                inputSection
            }

            // Score overlay removed
        }
        .navigationTitle(lesson.title)
        .navigationBarTitleDisplayMode(.inline)
        .toolbarBackground(.visible, for: .navigationBar)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button("Cancel") {
                    dismiss()
                }
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Done") {
                    // Save final chat messages state
                    saveChatMessages(chatMessages, for: lesson.id.uuidString)

                    // Return to previous screen
                    dismiss()
                }
            }
        }
        .navigationBarBackButtonHidden(true)
        .alert(errorTitle, isPresented: $showError) {
            if isRateLimitError {
                Button("Sign In", role: .none) {
                    // Navigate to auth view or trigger auth flow
                    NotificationCenter.default.post(name: NSNotification.Name("ShowAuthView"), object: nil)
                    dismiss()
                }
                Button("Cancel", role: .cancel) { }
            } else {
                Button("OK", role: .cancel) { }
            }
        } message: {
            Text(errorMessage)
        }
        .onChange(of: speechManager.transcribedText) { _, newValue in
            // Only update messageText if we're not ignoring updates and recording is active
            if !ignoreTranscriptionUpdates && !newValue.isEmpty && speechManager.isRecording {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    messageText = newValue
                }
            }
        }
        .onChange(of: chatMessages.count) { oldCount, newCount in
            // Hide prompt after first message exchange (when there are 2 or more messages)
            if oldCount < 2 && newCount >= 2 && showPrompt {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                    showPrompt = false
                }
            }
        }
        .onChange(of: scenePhase) { oldPhase, newPhase in
            // When returning to active state from background, ensure chat is synced
            if newPhase == .active && oldPhase != .active && viewHasAppeared {
                syncChatMessagesWithHistory()
            }

            // Save chat messages when app moves to background or inactive state
            if newPhase == .background || newPhase == .inactive {
                let lessonId = lesson.id.uuidString
                saveChatMessages(chatMessages, for: lessonId)
            }
        }
        .onAppear {
            // Start conversation timer if this is first appearance
            if !viewHasAppeared {
                conversationStartTime = Date()
                viewHasAppeared = true
            }

            // Sync chat messages with history when view appears
            syncChatMessagesWithHistory()

            // Set initial prompt visibility based on existing messages
            if chatMessages.count >= 2 {
                showPrompt = false
            }
        }
        .onDisappear {
            // Save chat messages when view disappears
            let lessonId = lesson.id.uuidString
            saveChatMessages(chatMessages, for: lessonId)
        }
        .animation(.spring(response: 0.5, dampingFraction: 0.7), value: showPrompt)
    }

    // MARK: - Body Components

    // Simplified background for better performance
    private var backgroundView: some View {
        Color(colorScheme == .dark ? .black : .white)
            .ignoresSafeArea()
    }

    private var promptSection: some View {
        Group {
            if showPrompt {
                promptView
                    .transition(.move(edge: .top).combined(with: .opacity))
            } else {
                // Collapsed prompt - tap to show
                collapsedPromptButton
                    .transition(.move(edge: .top).combined(with: .opacity))
            }
        }
    }

    private var messagesSection: some View {
        ZStack(alignment: .bottom) {
            // Chat messages with scroll
            ChatMessagesView(messages: chatMessages, onMessageTap: { message in
                if !message.isUser {
                    speechManager.speak(message.content)
                }
            }, scrollToLatest: $scrollToLatestMessage)

            // Typing indicator when AI is generating response
            if showTypingIndicator {
                typingIndicatorView
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private var typingIndicatorView: some View {
        HStack(alignment: .bottom, spacing: 8) {
            // AI avatar
            Circle()
                .fill(LinearGradient(
                    colors: [.purple.opacity(0.7), .blue.opacity(0.7)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
                .frame(width: 32, height: 32)
                .overlay(
                    Image(systemName: "brain")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                )
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)

            TypingIndicator()

            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 8)
        .transition(.move(edge: .bottom).combined(with: .opacity))
    }

    private var inputSection: some View {
        VStack(spacing: 0) {
            Rectangle()
                .frame(height: 1)
                .foregroundStyle(
                    LinearGradient(
                        colors: [.blue.opacity(0.2), .purple.opacity(0.2)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )

            MessageInputView(
                messageText: $messageText,
                speechManager: speechManager,
                onSend: sendMessage,
                onToggleRecording: toggleRecording,
                onClearText: {
                    // Set flag to ignore transcription updates during clearing
                    ignoreTranscriptionUpdates = true

                    // Clear texts
                    messageText = ""
                    speechManager.transcribedText = ""

                    // Reset flag after a delay
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        self.ignoreTranscriptionUpdates = false
                    }
                }
            )
        }
        .background(
            Color(.systemBackground)
                .shadow(color: .black.opacity(0.05), radius: 5, y: -2)
        )
    }

    // MARK: - Prompt Views

    private var promptView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Conversation Prompt")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.secondary)
                .padding(.horizontal, 16)
                .padding(.top, 8)

            Text(lesson.prompt)
                .font(.system(size: 17))
                .padding(16)
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(Color(.systemGray6))
                )
                .padding(.horizontal, 16)
                .padding(.bottom, 8)

            // Hide button (right-aligned)
            HStack {
                Spacer()

                Button {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                        showPrompt = false
                        hasShownPromptHint = true
                    }
                } label: {
                    Text("Hide Prompt")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.blue)
                }
                .padding(.trailing, 16)
                .padding(.bottom, 8)
            }
        }
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray5), lineWidth: 0.5)
        )
        .padding(.horizontal, 16)
        .padding(.top, 16)
    }

    private var collapsedPromptButton: some View {
        Button {
            withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                showPrompt = true
            }
        } label: {
            Text("Show Conversation Prompt")
                .font(.system(size: 15, weight: .medium))
                .foregroundColor(.blue)
                .frame(maxWidth: .infinity, alignment: .center)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(Color(.systemGray6))
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color(.systemGray5), lineWidth: 0.5)
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel("Show conversation prompt")
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
    }

    // Sync chat messages with the chat history from GeminiViewModel
    private func syncChatMessagesWithHistory() {
        let lessonId = lesson.id.uuidString

        // Try to load messages from local storage first
        if let loadedMessages = loadChatMessages(for: lessonId), !loadedMessages.isEmpty {
            chatMessages = loadedMessages

            // Use a longer delay to ensure view has time to update with new messages
            // before scrolling to bottom for a smoother experience
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                self.scrollToLatestMessage = true
            }

            // Also ensure these messages are synced to GeminiViewModel
            syncToGeminiViewModel(messages: loadedMessages, lessonId: lessonId)
        } else {
            // Fall back to GeminiViewModel history if no local messages
            let lessonHistory = geminiViewModel.historyForLesson(lessonId)

            // Convert history to chat messages
            let historyMessages = lessonHistory.map { history in
                ChatMessage(
                    id: UUID(),
                    content: history.content,
                    isUser: history.role == "user",
                    timestamp: history.timestamp ?? Date()
                )
            }

            // Only update if there's actual data to prevent unnecessary reloads
            if !historyMessages.isEmpty {
                chatMessages = historyMessages

                // Use a longer delay to ensure view has time to update with new messages
                // before scrolling to bottom for a smoother experience
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    self.scrollToLatestMessage = true
                }

                // Save messages to local storage
                saveChatMessages(chatMessages, for: lessonId)
            }
        }

        // Set initial prompt visibility based on existing messages
        if chatMessages.count >= 2 {
            // If there are already messages, hide the prompt
            showPrompt = false
        }
    }

    // Helper function to sync messages from local storage to GeminiViewModel
    private func syncToGeminiViewModel(messages: [ChatMessage], lessonId: String) {
        // Convert ChatMessage to ChatHistory
        let historyEntries = messages.map { message in
            ChatHistory(
                role: message.isUser ? "user" : "model",
                content: message.content,
                lessonId: lessonId,
                timestamp: message.timestamp
            )
        }

        // Update GeminiViewModel's chat history with these entries
        // (only adding entries that don't already exist)
        for entry in historyEntries {
            // Check if this message already exists in the history
            let messageExists = geminiViewModel.historyForLesson(lessonId).contains { history in
                history.role == entry.role &&
                history.content == entry.content &&
                history.lessonId == entry.lessonId
            }

            if !messageExists {
                geminiViewModel.addToHistory(entry)
            }
        }
    }

    // MARK: - Chat Message Persistence

    private func saveChatMessages(_ messages: [ChatMessage], for lessonId: String) {
        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(messages)
            UserDefaults.standard.set(data, forKey: "chatMessages_\(lessonId)")
            // Force UserDefaults to synchronize to ensure data is written immediately
            UserDefaults.standard.synchronize()
        } catch {
            print("Error saving chat messages: \(error.localizedDescription)")
        }
    }

    private func loadChatMessages(for lessonId: String) -> [ChatMessage]? {
        guard let data = UserDefaults.standard.data(forKey: "chatMessages_\(lessonId)") else {
            return nil
        }

        do {
            let decoder = JSONDecoder()
            let messages = try decoder.decode([ChatMessage].self, from: data)
            return messages
        } catch {
            print("Error loading chat messages: \(error.localizedDescription)")
            return nil
        }
    }

    private func toggleRecording() {
        // Stop any ongoing speech when microphone is pressed
        if speechManager.isSpeaking {
            speechManager.stopSpeaking()
        }

        if speechManager.isRecording {
            // Set flag to ignore transcription updates during stopping
            ignoreTranscriptionUpdates = true

            // Clear texts
            messageText = ""
            speechManager.transcribedText = ""

            // Stop recording
            speechManager.stopRecording()

            // Schedule a task to reset the flag and ensure message text is cleared
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                // Reset texts again to ensure they're clear
                self.messageText = ""
                self.speechManager.transcribedText = ""

                // Reset flag after texts are cleared
                self.ignoreTranscriptionUpdates = false
            }
        } else {
            // Reset flag when starting recording
            ignoreTranscriptionUpdates = false

            // Clear text with animation
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                messageText = ""
            }

            do {
                try speechManager.startRecording()
            } catch {
                lastError = error
                errorMessage = "Could not start recording: \(error.localizedDescription)"
                showError = true
            }
        }
    }

    private func sendMessage() {
        guard !messageText.isEmpty || speechManager.isRecording else { return }

        // Set conversation start time if this is the first message
        if conversationStartTime == nil {
            conversationStartTime = Date()
        }

        // Set flag to ignore transcription updates during sending
        ignoreTranscriptionUpdates = true

        if speechManager.isRecording {
            speechManager.stopRecording()
        }

        let userContent = messageText.isEmpty && speechManager.isRecording ?
            speechManager.transcribedText : messageText

        let userMessage = ChatMessage(
            id: UUID(),
            content: userContent,
            isUser: true,
            timestamp: Date()
        )

        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            chatMessages.append(userMessage)
        }

        // Save chat messages
        saveChatMessages(chatMessages, for: lesson.id.uuidString)

        let userText = userContent

        // Clear texts immediately with animation
        speechManager.transcribedText = ""
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            messageText = ""
        }

        // Schedule a task to ensure text fields are cleared completely
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            // Reset texts again to ensure they're clear
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                self.messageText = ""
            }
            self.speechManager.transcribedText = ""

            // Reset flag after texts are cleared
            self.ignoreTranscriptionUpdates = false
        }

        // Show typing indicator
        withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
            showTypingIndicator = true
        }

        // Get AI response
        Task {
            await getAIResponse(for: userText)
        }
    }

    private func getAIResponse(for message: String) async {
        do {
            let response = try await geminiViewModel.generateResponse(for: message, lesson: lesson)

            // Artificial delay for a more natural conversation feel
            try await Task.sleep(for: .seconds(0.7))

            await MainActor.run {
                // Hide typing indicator
                withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                    showTypingIndicator = false
                }

                // Small delay before showing message for natural feel
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    let aiMessage = ChatMessage(
                        id: UUID(),
                        content: response,
                        isUser: false,
                        timestamp: Date()
                    )

                    withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                        chatMessages.append(aiMessage)
                    }

                    // Save chat messages after AI response
                    self.saveChatMessages(self.chatMessages, for: self.lesson.id.uuidString)

                    // Automatically speak the AI response
                    speechManager.speak(response)
                }
            }
        } catch {
            await MainActor.run {
                // Hide typing indicator on error
                withAnimation {
                    showTypingIndicator = false
                }
                // Store the error for type checking
                lastError = error
                errorMessage = error.localizedDescription
                showError = true
            }
        }
    }

    // Score overlay view removed
}

// MARK: - Supporting Types
// ChatMessage moved to ChatModels.swift

struct ChatBubbleView: View {
    let message: ChatMessage
    @State private var isAnimating = false
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        HStack(alignment: .bottom, spacing: 8) {
            if !message.isUser {
                // Avatar for AI responses
                Circle()
                    .fill(LinearGradient(
                        colors: [.purple.opacity(0.7), .blue.opacity(0.7)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 28, height: 28)
                    .overlay(
                        Image(systemName: "brain")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                    )
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            }

            VStack(alignment: message.isUser ? .trailing : .leading, spacing: 2) {
                // Message bubble
                Text(message.content)
                    .fixedSize(horizontal: false, vertical: true) // Important for text wrapping
                    .font(.system(size: 17))
                    .lineSpacing(2)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 10)
                    .foregroundColor(message.isUser ? .white : .primary)
                    .background(
                        message.isUser ?
                            Color.blue :
                            (colorScheme == .dark ? Color(.systemGray5) : Color(.systemGray6))
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 16, style: .continuous))

                // Timestamp
                Text(formattedTime)
                    .font(.system(size: 11))
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 4)
                    .padding(.bottom, 2)
            }
            .frame(maxWidth: message.isUser ? .infinity : 270, alignment: message.isUser ? .trailing : .leading)

            // Removed user avatar
        }
        .padding(.horizontal, message.isUser ? 16 : 8)
        .padding(.vertical, 4)
        .id(message.id)
        // Simplified animation for better performance
        .opacity(isAnimating ? 1 : 0.7)
        .offset(y: isAnimating ? 0 : 10)
        .onAppear {
            withAnimation(.easeOut(duration: 0.2)) {
                isAnimating = true
            }
        }
    }

    private var formattedTime: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: message.timestamp)
    }
}

// MARK: - Loading Indicator for AI typing
struct TypingIndicator: View {
    @State private var firstDotOpacity: Double = 0.4
    @State private var secondDotOpacity: Double = 0.4
    @State private var thirdDotOpacity: Double = 0.4

    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .frame(width: 8, height: 8)
                .opacity(firstDotOpacity)
            Circle()
                .frame(width: 8, height: 8)
                .opacity(secondDotOpacity)
            Circle()
                .frame(width: 8, height: 8)
                .opacity(thirdDotOpacity)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemGray6))
        .clipShape(RoundedRectangle(cornerRadius: 18))
        .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        .onAppear {
            animateDots()
        }
    }

    // Optimized animation with fewer dispatch calls
    private func animateDots() {
        let duration = 0.8
        let animation = Animation.easeInOut(duration: duration).repeatForever(autoreverses: true)

        // Animate all dots with a single withAnimation call
        withAnimation(animation) {
            firstDotOpacity = 1.0
        }

        // Use a single dispatch for second dot
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            withAnimation(animation) {
                secondDotOpacity = 1.0
            }

            // Chain the third dot animation inside the same dispatch
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                withAnimation(animation) {
                    thirdDotOpacity = 1.0
                }
            }
        }
    }
}

#Preview {
    NavigationStack {
        TextChatView(
            lesson: LessonPlan(
                title: "Preview Lesson",
                description: "Test description",
                prompt: "This is a test prompt for preview"
            ),
            viewModel: CommunicationViewModel()
        )
    }
}
