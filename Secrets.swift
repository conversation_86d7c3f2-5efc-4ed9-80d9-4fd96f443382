import Foundation

// IMPORTANT: The API key is now stored securely in Firebase
// This file is kept for backward compatibility only
struct Secrets {
    // This empty struct is kept for backward compatibility
    // The Gemini API key is now securely stored in Firebase Cloud Functions
    
    // For development and testing purposes, you can use the emulator
    // See the README.md for setup instructions
    
    // For production deployment, update the Firebase console configuration:
    // firebase functions:config:set gemini.key="YOUR_API_KEY"
}
