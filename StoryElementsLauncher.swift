import SwiftUI

struct StoryElementsLauncher: View {
    var body: some View {
        NavigationLink(destination: StoryElementsViewWrapper()) {
            VStack(spacing: 16) {
                Text("Key Story Elements and Structure")
                    .font(.headline)
                    .multilineTextAlignment(.center)
                
                Text("Learn about essential elements and structure for effective storytelling")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Text("Tap to Open")
                    .font(.callout)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
            }
            .padding()
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
            )
            .padding()
        }
    }
}

struct StoryElementsViewWrapper: View {
    var body: some View {
        // This is a wrapper that loads the actual StoryElementsView
        // We use this approach to avoid direct import issues
        StoryElementsView()
    }
}

#Preview {
    StoryElementsLauncher()
}
