import SwiftUI
import WebKit

struct TermsConsentView: View {
    @ObservedObject private var termsManager = TermsConsentManager.shared
    @State private var isShowingTerms = false
    
    // URL for the terms of use
    private let termsURL = "https://www.rashedslab.com/terms-of-use"
    
    var body: some View {
        ZStack {
            // Solid background to block interaction with content behind
            Color(.systemBackground) // Use system background for proper dark/light mode
                .ignoresSafeArea()
            
            VStack(spacing: 24) {
                Image(systemName: "doc.text")
                    .font(.system(size: 60))
                    .foregroundColor(.blue)
                    .padding()
                    .background(
                        Circle()
                            .fill(Color.blue.opacity(0.2))
                            .frame(width: 120, height: 120)
                    )
                
                Text("Terms of Use")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("Before using Talk Maxer, you must review and agree to our Terms of Use.")
                    .font(.body)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                // View Terms button
                Button {
                    isShowingTerms = true
                } label: {
                    Text("View Terms of Use")
                        .fontWeight(.medium)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue.opacity(0.1))
                        .foregroundColor(.blue)
                        .cornerRadius(12)
                }
                .padding(.horizontal)
                
                Spacer()
                
                // Agree button
                Button {
                    termsManager.acceptTerms()
                } label: {
                    Text("I Agree")
                        .fontWeight(.bold)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                }
                .padding(.horizontal)
                
                // Decline button
                Button {
                    // Exit the app
                    exit(0)
                } label: {
                    Text("I Decline")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .foregroundColor(.secondary)
                        .cornerRadius(12)
                }
                .padding(.horizontal)
                .padding(.bottom, 16)
            }
            .padding()
        }
        .sheet(isPresented: $isShowingTerms) {
            WebViewSheet(title: "Terms of Use", urlString: termsURL)
        }
    }
}

#Preview {
    TermsConsentView()
} 
