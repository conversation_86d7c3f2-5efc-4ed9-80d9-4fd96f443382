import SwiftUI

struct ArticleRephraseView: View {
    // View model for AI interactions
    @StateObject private var geminiViewModel = GeminiViewModel()

    // UI State
    @State private var article = ""
    @State private var userParaphrase = ""
    @State private var isLoading = true
    @State private var showArticle = true
    @State private var showFeedback = false
    @State private var feedbackMessage = ""
    @State private var paraphraseScore = 0
    @State private var showError = false
    @State private var errorMessage = ""

    // Environment
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Text("Reading & Paraphrasing")
                            .font(.title2)
                            .fontWeight(.bold)

                        Text("Read the article and paraphrase it in your own words")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color(.systemBackground))

                    if isLoading {
                        // Loading indicator
                        VStack(spacing: 20) {
                            ProgressView()
                                .scaleEffect(1.5)

                            Text("Generating article...")
                                .foregroundColor(.secondary)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 100)
                    } else {
                        // Main content
                        VStack(spacing: 24) {
                            // Article section
                            if showArticle {
                                articleSection
                            }

                            // Paraphrase section
                            if !showArticle {
                                paraphraseSection
                            }

                            // Action buttons
                            actionButtons

                            // Feedback section
                            if showFeedback {
                                feedbackSection
                            }
                        }
                        .padding(.horizontal)
                    }

                    Spacer(minLength: 40)
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("New Article") {
                        resetAndRequestNewArticle()
                    }
                }
            }
            .alert("Error", isPresented: $showError) {
                Button("OK", role: .cancel) { }
            } message: {
                Text(errorMessage)
            }
            .onAppear {
                if article.isEmpty {
                    requestNewArticle()
                }
            }
        }
    }

    // MARK: - View Components

    private var articleSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Article:")
                .font(.headline)
                .foregroundColor(.primary)

            Text(article)
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.secondarySystemBackground))
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                )
        }
    }

    private var paraphraseSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Your Paraphrase:")
                .font(.headline)
                .foregroundColor(.primary)

            CustomTextEditor(
                text: $userParaphrase,
                placeholder: "Write your paraphrase of the article in your own words...",
                onDone: {
                    if !userParaphrase.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                        submitParaphrase()
                    }
                }
            )
            .frame(minHeight: 200)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
            )

            // Submit button
            Button {
                submitParaphrase()
            } label: {
                Text("Submit")
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(userParaphrase.isEmpty ? Color.gray : Color.blue)
                    )
            }
            .disabled(userParaphrase.isEmpty)
        }
    }

    private var actionButtons: some View {
        Group {
            if showArticle {
                Button {
                    withAnimation {
                        showArticle = false
                    }
                } label: {
                    Text("Start Paraphrasing")
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(
                                    LinearGradient(
                                        colors: [.blue, .purple.opacity(0.8)],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                        )
                }
            } else if showFeedback {
                Button {
                    resetAndRequestNewArticle()
                } label: {
                    Text("Try Another Article")
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(
                                    LinearGradient(
                                        colors: [.blue, .purple.opacity(0.8)],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                        )
                }
            }
        }
    }

    private var feedbackSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Feedback:")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(spacing: 16) {
                // Score display
                HStack {
                    Text("Score:")
                        .font(.headline)

                    Text("\(paraphraseScore)/10")
                        .font(.headline)
                        .foregroundColor(scoreColor)

                    Spacer()
                }

                // Score bar
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        // Background bar
                        Rectangle()
                            .frame(width: geometry.size.width, height: 8)
                            .opacity(0.3)
                            .foregroundColor(Color.gray)
                            .cornerRadius(4)

                        // Score bar
                        Rectangle()
                            .frame(width: geometry.size.width * CGFloat(paraphraseScore) / 10, height: 8)
                            .foregroundColor(scoreColor)
                            .cornerRadius(4)
                    }
                }
                .frame(height: 8)

                // Feedback text
                Text(feedbackMessage)
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.secondarySystemBackground))
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(scoreColor.opacity(0.5), lineWidth: 1)
                    )
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 5, y: 2)
            )
        }
    }

    // MARK: - Helper Properties

    private var scoreColor: Color {
        if paraphraseScore >= 8 {
            return .green
        } else if paraphraseScore >= 5 {
            return .orange
        } else {
            return .red
        }
    }

    // MARK: - Functions

    private func requestNewArticle() {
        isLoading = true

        // Request a new article from AI
        Task {
            do {
                // Base prompt for generating an article
                let prompt = """
                Generate a short, interesting article about a general knowledge topic (science, history, culture, etc.).

                The article should:
                1. Be 3-4 paragraphs long
                2. Be factually accurate
                3. Be written at a high school reading level
                4. Include some interesting facts or statistics
                5. Be suitable for a paraphrasing exercise

                Provide ONLY the article text with no additional explanations or notes.
                """

                // Create a dummy lesson plan for the API call
                let dummyLesson = LessonPlan(
                    title: "Article Paraphrasing Practice",
                    description: "Practice paraphrasing articles",
                    prompt: prompt,
                    rules: "Generate only the article with no additional text."
                )

                // Get a new article from AI
                print("Requesting new article...")
                let generatedArticle = try await geminiViewModel.generateResponse(for: prompt, lesson: dummyLesson)

                await MainActor.run {
                    article = generatedArticle.trimmingCharacters(in: .whitespacesAndNewlines)
                    isLoading = false
                    showArticle = true
                    showFeedback = false
                    userParaphrase = ""
                }
            } catch {
                print("Error generating article: \(error.localizedDescription)")

                await MainActor.run {
                    // Use fallback article
                    article = """
                    Climate change continues to be one of the most pressing issues of our time. Scientists around the world have documented rising global temperatures, with the last decade being the warmest on record. According to NASA, the Earth's average surface temperature has risen about 1.18 degrees Celsius since the late 19th century, largely driven by increased carbon dioxide emissions and other human activities.

                    The effects of climate change are far-reaching and increasingly visible. Glaciers are retreating, sea levels are rising, and extreme weather events like hurricanes, floods, and wildfires are becoming more frequent and intense. The Arctic is warming at almost twice the global average rate, resulting in dramatic loss of sea ice. These changes not only affect ecosystems but also human communities, particularly those in vulnerable coastal regions.

                    While the challenges are significant, there are reasons for hope. Renewable energy sources like solar and wind power have become increasingly affordable and widespread. Many countries, cities, and businesses have committed to reducing their carbon footprints and transitioning to cleaner energy sources. Individual actions, from reducing meat consumption to using public transportation, can also make a difference when adopted collectively. Addressing climate change requires global cooperation, innovation, and a commitment to creating a sustainable future for all.
                    """

                    isLoading = false
                    showArticle = true
                    showFeedback = false
                    userParaphrase = ""

                    // Show error
                    errorMessage = "Error generating article: \(error.localizedDescription)"
                    showError = true
                }
            }
        }
    }

    private func submitParaphrase() {
        // Trim whitespace and check if text is empty
        let trimmedText = userParaphrase.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedText.isEmpty else { return }

        // Hide keyboard
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)

        // Show loading indicator
        isLoading = true

        // Generate AI feedback on the paraphrase
        Task {
            do {
                // Request AI feedback on the paraphrase
                let prompt = """
                Original article:
                "\(article)"

                User's paraphrase:
                "\(userParaphrase)"

                Evaluate how well the user paraphrased the article. Consider:
                1. Accuracy - Does it maintain the core meaning and key points?
                2. Originality - Is it in the user's own words or too similar to the original?
                3. Completeness - Does it cover all the main ideas from the original?
                4. Clarity - Is it clear and well-structured?

                First, give a score from 1-10 (where 10 is perfect).

                Then provide specific feedback on what was done well and what could be improved.

                Format your response exactly like this:
                SCORE: [number]
                FEEDBACK: [your detailed feedback]
                """

                // Create a dummy lesson plan for the API call
                let dummyFeedbackLesson = LessonPlan(
                    title: "Paraphrase Feedback",
                    description: "Evaluate paraphrasing skills",
                    prompt: prompt,
                    rules: "Evaluate the paraphrase and provide constructive feedback with a score."
                )

                print("Requesting feedback...")
                let response = try await geminiViewModel.generateResponse(for: prompt, lesson: dummyFeedbackLesson)

                await MainActor.run {
                    isLoading = false

                    // Process the response to extract score and feedback
                    let lines = response.components(separatedBy: "\n")
                    var score = 0
                    var feedback = ""

                    for line in lines {
                        if line.starts(with: "SCORE:") {
                            let scoreText = line.replacingOccurrences(of: "SCORE:", with: "").trimmingCharacters(in: .whitespacesAndNewlines)
                            score = Int(scoreText) ?? 0
                        } else if line.starts(with: "FEEDBACK:") {
                            feedback = line.replacingOccurrences(of: "FEEDBACK:", with: "").trimmingCharacters(in: .whitespacesAndNewlines)
                        } else if !line.isEmpty && !feedback.isEmpty {
                            // Append additional feedback lines
                            feedback += "\n" + line
                        }
                    }

                    // If parsing failed, use the whole response as feedback with a default score
                    if feedback.isEmpty {
                        feedback = response
                        score = 5 // Default middle score
                    }

                    // Update UI
                    paraphraseScore = score
                    feedbackMessage = feedback.trimmingCharacters(in: .whitespacesAndNewlines)

                    withAnimation {
                        showFeedback = true
                    }
                }
            } catch {
                print("Error generating feedback: \(error.localizedDescription)")

                await MainActor.run {
                    isLoading = false

                    // Show error
                    errorMessage = "Error generating feedback: \(error.localizedDescription)"
                    showError = true

                    // Show generic feedback as fallback
                    paraphraseScore = 5 // Default middle score
                    feedbackMessage = "I couldn't evaluate your paraphrase. Try to ensure your paraphrase captures the main ideas of the article while using your own words and sentence structures."

                    withAnimation {
                        showFeedback = true
                    }
                }
            }
        }
    }

    private func resetAndRequestNewArticle() {
        withAnimation {
            article = ""
            userParaphrase = ""
            showArticle = true
            showFeedback = false
        }
        requestNewArticle()
    }
}

// Using CustomTextEditor from SentenceRewriteView

#Preview {
    ArticleRephraseView()
}
