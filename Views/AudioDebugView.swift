import SwiftUI
import AVFoundation

// Wrapper to make String identifiable for sheet presentation
struct IdentifiableString: Identifiable {
    let id = UUID()
    let value: String
}

struct AudioDebugView: View {
    @State private var selectedAudioFile: IdentifiableString? = nil
    @State private var audioFiles: [String] = []
    @State private var assetFiles: [String] = []
    @State private var selectedFile: String = ""
    @State private var isPlaying = false
    @State private var audioPlayer: AVAudioPlayer?
    @State private var errorMessage: String = ""

    var body: some View {
        VStack(spacing: 20) {
            Text("Audio Debug")
                .font(.title)
                .fontWeight(.bold)

            // Bundle audio files
            VStack(alignment: .leading) {
                Text("Bundle Audio Files:")
                    .font(.headline)

                ScrollView {
                    VStack(alignment: .leading) {
                        ForEach(audioFiles, id: \.self) { file in
                            HStack {
                                Text(file)
                                Spacer()
                                Button("Play") {
                                    playAudioFile(file)
                                }
                            }
                            .padding(.vertical, 4)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
                .frame(height: 200)
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }

            // Asset catalog audio files
            VStack(alignment: .leading) {
                Text("Asset Catalog Files:")
                    .font(.headline)

                ScrollView {
                    VStack(alignment: .leading) {
                        ForEach(assetFiles, id: \.self) { file in
                            HStack {
                                Text(file)
                                Spacer()
                                Button("Play") {
                                    playAssetFile(file)
                                }
                            }
                            .padding(.vertical, 4)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
                .frame(height: 200)
                .background(Color(.systemGray6))
                .cornerRadius(8)
            }

            if !errorMessage.isEmpty {
                Text(errorMessage)
                    .foregroundColor(.red)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
            }

            // Now playing
            if isPlaying {
                HStack {
                    Text("Now playing: \(selectedFile)")
                    Spacer()
                    Button("Stop") {
                        stopPlayback()
                    }
                }
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
            }

            // Direct test section
            VStack(alignment: .leading) {
                Text("Direct Test:")
                    .font(.headline)

                VStack(spacing: 10) {
                    HStack {
                        Button("Test Fundamentals Audio") {
                            navigateToSimplePlayer("Fundamentals of Effective Communication")
                        }
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(8)

                        Button("Test Storytelling Audio") {
                            navigateToSimplePlayer("The Art and Practice of Storytelling")
                        }
                        .padding()
                        .background(Color.orange)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }

                    HStack {
                        Button("Test Story Elements Audio") {
                            navigateToSimplePlayer("Key Story Elements and Structure")
                        }
                        .padding()
                        .background(Color.purple)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                }
            }
            .padding(.vertical)

            // Refresh button
            Button("Refresh Audio Files") {
                scanAudioFiles()
            }
            .padding()
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(8)
        }
        .padding()
        .onAppear {
            configureAudioSession()
            scanAudioFiles()
        }
        .sheet(item: $selectedAudioFile) { identifiableString in
            ReusableAudioPlayerView(
                audioFileName: identifiableString.value,
                title: "Audio Debug Player",
                subtitle: identifiableString.value
            )
        }
    }

    private func scanAudioFiles() {
        // Clear previous results
        audioFiles = []
        assetFiles = []
        errorMessage = ""

        // Scan bundle for audio files
        if let resourceURLs = Bundle.main.urls(forResourcesWithExtension: nil, subdirectory: nil) {
            for url in resourceURLs {
                let ext = url.pathExtension.lowercased()
                if ext == "wav" || ext == "mp3" {
                    audioFiles.append(url.lastPathComponent)
                }
            }
        }

        // Scan asset catalog for audio files
        let assetNames = ["Audios", "Fundamentals of Effective Communication",
                         "The Art and Practice of Storytelling", "Understanding and Reducing Filler Words",
                         "Key Story Elements and Structure"]

        for name in assetNames {
            if let asset = NSDataAsset(name: name) {
                assetFiles.append("\(name) (\(asset.data.count) bytes)")
            }

            // Also try with Audios/ prefix
            if name != "Audios" {
                if let asset = NSDataAsset(name: "Audios/\(name)") {
                    assetFiles.append("Audios/\(name) (\(asset.data.count) bytes)")
                }
            }
        }

        if audioFiles.isEmpty && assetFiles.isEmpty {
            errorMessage = "No audio files found in bundle or asset catalog"
        }
    }

    private func configureAudioSession() {
        let audioSession = AVAudioSession.sharedInstance()
        do {
            try audioSession.setCategory(.playback, mode: .default)
            try audioSession.setActive(true)
        } catch {
            errorMessage = "Failed to configure audio session: \(error.localizedDescription)"
        }
    }

    private func playAudioFile(_ filename: String) {
        stopPlayback()

        // Extract the base name without extension
        let components = filename.components(separatedBy: ".")
        if components.count > 1 {
            let baseName = components[0]
            let ext = components[1]

            if let url = Bundle.main.url(forResource: baseName, withExtension: ext) {
                do {
                    audioPlayer = try AVAudioPlayer(contentsOf: url)
                    audioPlayer?.prepareToPlay()
                    audioPlayer?.play()
                    isPlaying = true
                    selectedFile = filename
                } catch {
                    errorMessage = "Error playing \(filename): \(error.localizedDescription)"
                }
            } else {
                errorMessage = "Could not find \(filename) in bundle"
            }
        } else {
            errorMessage = "Invalid filename format: \(filename)"
        }
    }

    private func playAssetFile(_ assetName: String) {
        stopPlayback()

        // Extract the asset name from the display string
        let components = assetName.components(separatedBy: " (")
        let name = components[0]

        if let asset = NSDataAsset(name: name) {
            do {
                audioPlayer = try AVAudioPlayer(data: asset.data)
                audioPlayer?.prepareToPlay()
                audioPlayer?.play()
                isPlaying = true
                selectedFile = name
            } catch {
                errorMessage = "Error playing asset \(name): \(error.localizedDescription)"
            }
        } else {
            errorMessage = "Could not load asset: \(name)"
        }
    }

    private func stopPlayback() {
        audioPlayer?.stop()
        audioPlayer = nil
        isPlaying = false
    }

    private func navigateToSimplePlayer(_ fileName: String) {
        selectedAudioFile = IdentifiableString(value: fileName)
    }
}

#Preview {
    AudioDebugView()
}
