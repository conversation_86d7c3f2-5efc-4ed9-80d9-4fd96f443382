import SwiftUI
import UIKit

// Enum for sentence difficulty levels
enum SentenceDifficulty: String, CaseIterable, Identifiable {
    case easy = "Easy"
    case moderate = "Moderate"
    case complex = "Complex"

    var id: String { rawValue }

    var description: String {
        switch self {
        case .easy:
            return "Simple sentences with basic vocabulary"
        case .moderate:
            return "Moderately complex sentences with some challenging elements"
        case .complex:
            return "Complex sentences with advanced vocabulary and structure"
        }
    }

    var promptModifier: String {
        switch self {
        case .easy:
            return "Create a simple sentence that is somewhat wordy but uses basic vocabulary. Keep it between 15-25 words with 1-2 clauses."
        case .moderate:
            return "Create a moderately complex sentence with some unnecessary words and phrases. Keep it between 25-35 words with 2-3 clauses."
        case .complex:
            return "Create a very complex sentence with advanced vocabulary and multiple clauses. Keep it between 35-50 words with at least 3 clauses or modifiers."
        }
    }

    var colors: [Color] {
        switch self {
        case .easy:
            return [Color.green, Color.green.opacity(0.7)]
        case .moderate:
            return [Color.blue, Color.blue.opacity(0.7)]
        case .complex:
            return [Color.purple, Color.purple.opacity(0.7)]
        }
    }
}

struct SentenceRewriteView: View {
    // View model
    @StateObject private var geminiViewModel = GeminiViewModel()

    // UI State
    @State private var selectedDifficulty: SentenceDifficulty = .moderate
    @State private var showDifficultySelection = true
    @State private var isLoading = false
    @State private var currentSentence = ""
    @State private var userRewrite = ""
    @State private var feedbackMessage = ""
    @State private var showFeedback = false
    @State private var feedbackIsPositive = false
    @State private var showError = false
    @State private var errorMessage = ""

    // Environment
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        NavigationStack {
            Group {
                if showDifficultySelection {
                    difficultySelectionView
                } else {
                    sentenceRewriteView
                }
            }
            .navigationTitle("Sentence Rewriting")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    if !showDifficultySelection {
                        Button("Change Difficulty") {
                            withAnimation {
                                showDifficultySelection = true
                                resetState()
                            }
                        }
                    } else {
                        Button("Done") {
                            dismiss()
                        }
                    }
                }
            }
            .alert("Error", isPresented: $showError) {
                Button("OK", role: .cancel) { }
            } message: {
                Text(errorMessage)
            }
        }
    }

    // MARK: - Difficulty Selection View

    private var difficultySelectionView: some View {
        VStack(spacing: 30) {
            Text("Select difficulty level")
                .font(.title3)
                .fontWeight(.bold)
                .padding(.top, 40)

            ForEach(SentenceDifficulty.allCases) { difficulty in
                Button {
                    selectDifficulty(difficulty)
                } label: {
                    DifficultyButton(difficulty: difficulty)
                }
            }

            Spacer()
        }
        .padding()
        .background(Color(.systemGroupedBackground))
    }

    // MARK: - Sentence Rewrite View

    private var sentenceRewriteView: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Original sentence card
                VStack(alignment: .leading, spacing: 10) {
                    Text("Original Sentence:")
                        .font(.headline)
                        .foregroundColor(.secondary)

                    if isLoading {
                        ProgressView()
                            .frame(maxWidth: .infinity, alignment: .center)
                            .padding()
                    } else {
                        Text(currentSentence)
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color(.secondarySystemBackground))
                            )
                    }
                }
                .padding(.horizontal)
                .padding(.top)

                // User rewrite section
                VStack(alignment: .leading, spacing: 10) {
                    Text("Your Rewrite:")
                        .font(.headline)
                        .foregroundColor(.secondary)

                    CustomTextEditor(
                        text: $userRewrite,
                        placeholder: "Rewrite the sentence to make it clearer and more concise...",
                        onDone: submitRewrite
                    )
                    .padding(10)
                    .frame(minHeight: 100)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                    )
                }
                .padding(.horizontal)

                // Submit button
                Button {
                    submitRewrite()
                } label: {
                    Text("Submit")
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(userRewrite.isEmpty ? Color.gray : Color.blue)
                        )
                        .padding(.horizontal)
                }
                .disabled(userRewrite.isEmpty || isLoading)

                // Feedback section
                if showFeedback {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Feedback:")
                            .font(.headline)
                            .foregroundColor(.secondary)

                        Text(feedbackMessage)
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(feedbackIsPositive ? Color.green.opacity(0.1) : Color.orange.opacity(0.1))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(feedbackIsPositive ? Color.green.opacity(0.3) : Color.orange.opacity(0.3), lineWidth: 1)
                                    )
                            )
                    }
                    .padding(.horizontal)

                    // Action buttons
                    HStack(spacing: 20) {
                        if !feedbackIsPositive {
                            // Try again button
                            Button {
                                withAnimation {
                                    userRewrite = ""
                                    showFeedback = false
                                }
                            } label: {
                                Text("Try Again")
                                    .fontWeight(.medium)
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .padding()
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(Color.orange)
                                    )
                            }
                        }

                        // New sentence button
                        Button {
                            requestNewSentence()
                        } label: {
                            Text(feedbackIsPositive ? "Practice Another" : "New Sentence")
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(feedbackIsPositive ?
                                              LinearGradient(colors: [.blue, .purple.opacity(0.8)], startPoint: .leading, endPoint: .trailing) :
                                              LinearGradient(colors: [.blue, .blue.opacity(0.8)], startPoint: .leading, endPoint: .trailing))
                                )
                        }
                    }
                    .padding(.horizontal)
                }

                Spacer()
            }
        }
        .onAppear {
            if currentSentence.isEmpty {
                requestNewSentence()
            }
        }
    }

    // MARK: - Helper Views

    struct DifficultyButton: View {
        let difficulty: SentenceDifficulty

        var body: some View {
            VStack(spacing: 8) {
                Text(difficulty.rawValue)
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.top, 12)

                Text(difficulty.description)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.9))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                    .padding(.bottom, 12)
            }
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: difficulty.colors,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            )
            .shadow(color: .black.opacity(0.1), radius: 5, y: 2)
            .contentShape(Rectangle()) // Make entire area tappable
        }
    }

    // MARK: - Actions

    private func selectDifficulty(_ difficulty: SentenceDifficulty) {
        print("Selected difficulty: \(difficulty.rawValue)")
        selectedDifficulty = difficulty
        withAnimation {
            showDifficultySelection = false
        }
        requestNewSentence()
    }

    private func resetState() {
        userRewrite = ""
        showFeedback = false
        feedbackMessage = ""
        currentSentence = ""
    }

    private func requestNewSentence() {
        isLoading = true
        userRewrite = ""
        showFeedback = false

        // Fallback sentences in case API fails
        let fallbackSentences = [
            "The implementation of the new policy, which was designed by a committee of experts after months of deliberation and consultation with various stakeholders, will commence next month.",
            "Despite the fact that numerous studies have conclusively demonstrated the beneficial effects of regular exercise on both physical and mental health, many individuals continue to lead predominantly sedentary lifestyles.",
            "The company's quarterly financial report, which was prepared by the accounting department and reviewed by external auditors before being submitted to the board of directors, showed a significant increase in revenue compared to the previous fiscal period."
        ]

        // Request a new sentence from AI
        Task {
            do {
                // Base prompt for generating a sentence
                let basePrompt = """
                Generate a single sentence that would benefit from being rewritten more clearly and concisely.

                The sentence should:
                1. Be grammatically correct
                2. Be about a general topic appropriate for all audiences (business, science, education, etc.)
                3. Be factually accurate

                \(selectedDifficulty.promptModifier)

                Provide ONLY the sentence itself with no additional text, explanations, or quotation marks.
                """

                // Create a dummy lesson plan for the API call
                let dummySentenceLesson = LessonPlan(
                    title: "Sentence Rewriting Practice",
                    description: "Practice rewriting complex sentences",
                    prompt: basePrompt,
                    rules: "Generate only the sentence with no additional text."
                )

                // Get a new sentence from AI
                print("Requesting new sentence...")
                let sentence = try await geminiViewModel.generateResponse(for: basePrompt, lesson: dummySentenceLesson)

                await MainActor.run {
                    // Store the new sentence if we got a valid response
                    if !sentence.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                        currentSentence = sentence.trimmingCharacters(in: .whitespacesAndNewlines)
                    } else {
                        // Use fallback if response is empty
                        currentSentence = fallbackSentences.randomElement() ?? fallbackSentences[0]
                    }
                    isLoading = false
                }
            } catch {
                print("Error generating sentence: \(error.localizedDescription)")

                await MainActor.run {
                    // Use fallback sentence
                    currentSentence = fallbackSentences.randomElement() ?? fallbackSentences[0]
                    isLoading = false

                    // Show error
                    errorMessage = "Error generating sentence: \(error.localizedDescription)"
                    showError = true
                }
            }
        }
    }

    private func submitRewrite() {
        // Trim whitespace and check if text is empty
        let trimmedText = userRewrite.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedText.isEmpty else { return }

        // Hide keyboard
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)

        // Show loading indicator
        isLoading = true

        // Generate AI feedback on the rewritten sentence
        Task {
            do {
                // Request AI feedback on the rewritten sentence
                let prompt = """
                Original sentence: "\(currentSentence)"

                Rewritten sentence: "\(userRewrite)"

                Evaluate if the rewritten sentence maintains the core meaning while being clearer and more concise.

                First, determine if the rewrite is GOOD or NEEDS IMPROVEMENT.

                If GOOD:
                - Start with "Great job!" and explain what makes it good
                - Mention specific improvements in clarity and conciseness
                - End with "You're ready to practice another sentence!"

                If NEEDS IMPROVEMENT:
                - Start with "Let's improve this." and explain what needs work
                - Provide specific suggestions for improvement
                - Mention if meaning was lost or if it's not concise enough
                - End with "Try again or practice with a new sentence."

                Keep your response under 150 words and be encouraging.

                Begin your response with either "GOOD: " or "NEEDS_IMPROVEMENT: " (this prefix will be removed before showing to the user).
                """

                // Create a dummy lesson plan for the API call
                let dummyFeedbackLesson = LessonPlan(
                    title: "Sentence Rewriting Feedback",
                    description: "Evaluate rewritten sentences",
                    prompt: prompt,
                    rules: "Evaluate the rewritten sentence and provide constructive feedback."
                )

                print("Requesting feedback...")
                let response = try await geminiViewModel.generateResponse(for: prompt, lesson: dummyFeedbackLesson)

                await MainActor.run {
                    isLoading = false

                    // Process the response to determine if it was good or needs improvement
                    var feedbackContent = response

                    if response.starts(with: "GOOD: ") {
                        feedbackIsPositive = true
                        feedbackContent = String(response.dropFirst(6))
                    } else if response.starts(with: "NEEDS_IMPROVEMENT: ") {
                        feedbackIsPositive = false
                        feedbackContent = String(response.dropFirst(19))
                    } else {
                        // If AI didn't follow the format, make a best guess
                        feedbackIsPositive = response.lowercased().contains("great job") ||
                                response.lowercased().contains("excellent") ||
                                response.lowercased().contains("well done")
                        feedbackContent = response
                    }

                    feedbackMessage = feedbackContent
                    withAnimation {
                        showFeedback = true
                    }
                }
            } catch {
                print("Error generating feedback: \(error.localizedDescription)")

                await MainActor.run {
                    isLoading = false

                    // Show error
                    errorMessage = "Error generating feedback: \(error.localizedDescription)"
                    showError = true

                    // Show generic feedback as fallback
                    feedbackIsPositive = false
                    feedbackMessage = "I couldn't evaluate your rewrite. Try to make your sentence clearer and more concise while preserving the core meaning."
                    withAnimation {
                        showFeedback = true
                    }
                }
            }
        }
    }
}

// MARK: - Custom Text Editor with Done Button

struct CustomTextEditor: UIViewRepresentable {
    @Binding var text: String
    var placeholder: String
    var onDone: () -> Void

    func makeUIView(context: Context) -> UITextView {
        let textView = UITextView()
        textView.delegate = context.coordinator
        textView.font = UIFont.preferredFont(forTextStyle: .body)
        textView.isScrollEnabled = true
        textView.isEditable = true
        textView.isUserInteractionEnabled = true
        textView.autocapitalizationType = .sentences
        textView.returnKeyType = .done // Set return key to "Done"
        textView.backgroundColor = UIColor.systemBackground
        textView.layer.cornerRadius = 8
        textView.textContainerInset = UIEdgeInsets(top: 8, left: 4, bottom: 8, right: 4)

        // Add placeholder if text is empty
        if text.isEmpty {
            textView.text = placeholder
            textView.textColor = UIColor.placeholderText
        } else {
            textView.text = text
            textView.textColor = UIColor.label
        }

        return textView
    }

    func updateUIView(_ uiView: UITextView, context: Context) {
        // Only update if the text actually changed to avoid losing cursor position
        if uiView.text != text {
            // Handle placeholder
            if text.isEmpty && uiView.textColor != UIColor.placeholderText {
                uiView.text = placeholder
                uiView.textColor = UIColor.placeholderText
            } else if !text.isEmpty && uiView.text != text {
                uiView.text = text
                uiView.textColor = UIColor.label
            }
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UITextViewDelegate {
        var parent: CustomTextEditor

        init(_ parent: CustomTextEditor) {
            self.parent = parent
        }

        func textViewDidBeginEditing(_ textView: UITextView) {
            // Clear placeholder when editing begins
            if textView.textColor == UIColor.placeholderText {
                textView.text = ""
                textView.textColor = UIColor.label
            }
        }

        func textViewDidEndEditing(_ textView: UITextView) {
            // Restore placeholder if text is empty
            if textView.text.isEmpty {
                textView.text = parent.placeholder
                textView.textColor = UIColor.placeholderText
            }
        }

        func textViewDidChange(_ textView: UITextView) {
            // Update binding only if not showing placeholder
            if textView.textColor != UIColor.placeholderText {
                parent.text = textView.text
            }
        }

        func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
            // Handle return key as "Done" button
            if text == "\n" {
                textView.resignFirstResponder() // Hide keyboard
                parent.onDone() // Call the onDone closure
                return false
            }
            return true
        }
    }
}

#Preview {
    NavigationStack {
        SentenceRewriteView()
    }
}
