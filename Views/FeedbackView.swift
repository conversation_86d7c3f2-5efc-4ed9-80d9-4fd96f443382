import SwiftUI

struct FeedbackView: View {
    let word: String
    let sentence: String

    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @EnvironmentObject var viewModel: VocabularyViewModel

    // Callback for when the user completes a word successfully
    var onSuccessfulCompletion: (() -> Void)?

    // Computed property to get feedback for this word
    private var feedback: SentenceFeedback? {
        viewModel.sentenceFeedback[word]
    }

    // Background color based on feedback result
    private var feedbackBackgroundColor: Color {
        guard let feedback = feedback, !feedback.isChecking else {
            return Color(.systemBackground)
        }

        return feedback.isCorrect ?
            Color.green.opacity(colorScheme == .dark ? 0.2 : 0.1) :
            Color.red.opacity(colorScheme == .dark ? 0.2 : 0.1)
    }

    // Icon based on feedback result
    private var feedbackIcon: String {
        guard let feedback = feedback, !feedback.isChecking else {
            return "hourglass"
        }

        return feedback.isCorrect ? "checkmark.circle" : "exclamationmark.circle"
    }

    // Icon color based on feedback result
    private var feedbackIconColor: Color {
        guard let feedback = feedback, !feedback.isChecking else {
            return .gray
        }

        return feedback.isCorrect ? .green : .red
    }

    var body: some View {
        NavigationStack {
            VStack(alignment: .leading, spacing: 20) {
                // Word and sentence section
                VStack(alignment: .leading, spacing: 12) {
                    Text("Your sentence with \"\(word)\":")
                        .font(.headline)
                        .foregroundColor(.primary)

                    Text("\"\(sentence)\"")
                        .font(.body)
                        .italic()
                        .padding()
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                }
                .padding(.horizontal)

                Divider()

                // Feedback section
                VStack(alignment: .leading, spacing: 16) {
                    HStack {
                        Text("Feedback")
                            .font(.headline)
                            .foregroundColor(.primary)

                        Spacer()

                        if let feedback = feedback, feedback.isChecking {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle())
                        } else {
                            Image(systemName: feedbackIcon)
                                .font(.system(size: 20))
                                .foregroundColor(feedbackIconColor)
                        }
                    }

                    if let feedback = feedback {
                        if feedback.isChecking {
                            Text("Analyzing your sentence...")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        } else {
                            Text(feedback.feedback)
                                .font(.body)
                                .padding()
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(feedbackBackgroundColor)
                                .cornerRadius(8)
                        }
                    } else {
                        Text("Waiting for analysis...")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal)

                Spacer()

                // Try again button
                if let feedback = feedback, !feedback.isChecking {
                    Button(action: {
                        // Hide keyboard
                        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)

                        // Clear feedback and dismiss
                        viewModel.clearFeedback(for: word)

                        // If the feedback was correct, call the completion handler
                        if feedback.isCorrect {
                            // Dismiss the sheet
                            dismiss()

                            // Call the completion handler after a short delay to allow the sheet to dismiss
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                onSuccessfulCompletion?()
                            }
                        } else {
                            dismiss()
                        }
                    }) {
                        Text(feedback.isCorrect ? "Great! Continue" : "Try Again")
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(
                                LinearGradient(
                                    gradient: Gradient(colors: [.blue, .blue.opacity(0.8)]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .cornerRadius(10)
                            .padding(.horizontal)
                    }
                    .padding(.bottom)
                }
            }
            .padding(.vertical)
            .navigationTitle("Sentence Feedback")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.gray)
                    }
                }
            }
        }
    }
}

#Preview {
    FeedbackView(word: "Articulate", sentence: "She articulated her thoughts clearly during the presentation.")
        .environmentObject(VocabularyViewModel())
}
