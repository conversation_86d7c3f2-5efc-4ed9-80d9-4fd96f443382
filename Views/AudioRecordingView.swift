import SwiftUI

struct AudioRecordingView: View {
    @StateObject private var speechManager = SpeechManager()
    @State private var recordedText = ""
    @State private var isPlayingBack = false
    @State private var showingFeedback = false
    @State private var fillerWordsCount = 0
    
    var body: some View {
        VStack(spacing: 20) {
            // Header
            Text("Record Yourself")
                .font(.title)
                .fontWeight(.bold)
            
            Text("Explain a topic clearly, then listen back to identify areas for improvement")
                .font(.subheadline)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Spacer()
            
            // Recording area
            VStack {
                if speechManager.isRecording {
                    // Show transcription while recording
                    ScrollView {
                        Text(speechManager.transcribedText.isEmpty ? "Speak now..." : speechManager.transcribedText)
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    .frame(height: 200)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.red.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .strokeBorder(Color.red.opacity(0.3), lineWidth: 1)
                            )
                    )
                } else if !recordedText.isEmpty {
                    // Show recorded text when not recording
                    ScrollView {
                        Text(recordedText)
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    .frame(height: 200)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .strokeBorder(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                    )
                    
                    // Show filler words count if we have recorded text
                    HStack {
                        Image(systemName: "exclamationmark.bubble")
                            .foregroundColor(.orange)
                        
                        Text("Filler words detected: \(fillerWordsCount)")
                            .font(.subheadline)
                            .foregroundColor(.orange)
                    }
                    .padding(.top, 8)
                } else {
                    // Empty state
                    VStack {
                        Image(systemName: "mic.circle")
                            .font(.system(size: 50))
                            .foregroundColor(.blue)
                        
                        Text("Tap the microphone button below to start recording")
                            .font(.subheadline)
                            .multilineTextAlignment(.center)
                            .padding()
                    }
                    .frame(height: 200)
                    .frame(maxWidth: .infinity)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .strokeBorder(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                    )
                }
            }
            .padding()
            
            // Tips for avoiding filler words
            if !recordedText.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Tips to reduce filler words:")
                        .font(.headline)
                        .padding(.bottom, 4)
                    
                    HStack(alignment: .top) {
                        Text("•")
                        Text("Pause instead of saying 'um' or 'uh'")
                    }
                    
                    HStack(alignment: .top) {
                        Text("•")
                        Text("Prepare your thoughts before speaking")
                    }
                    
                    HStack(alignment: .top) {
                        Text("•")
                        Text("Practice speaking more slowly")
                    }
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.blue.opacity(0.1))
                )
                .padding(.horizontal)
            }
            
            Spacer()
            
            // Controls
            HStack(spacing: 40) {
                // Record button
                Button(action: toggleRecording) {
                    ZStack {
                        Circle()
                            .fill(speechManager.isRecording ? Color.red : Color.blue)
                            .frame(width: 70, height: 70)
                            .shadow(radius: 5)
                        
                        Image(systemName: speechManager.isRecording ? "stop.fill" : "mic.fill")
                            .font(.system(size: 30))
                            .foregroundColor(.white)
                    }
                }
                
                // Playback button (only enabled when there's recorded text)
                Button(action: togglePlayback) {
                    ZStack {
                        Circle()
                            .fill(isPlayingBack ? Color.purple : Color.blue)
                            .frame(width: 70, height: 70)
                            .shadow(radius: 5)
                            .opacity(recordedText.isEmpty ? 0.5 : 1.0)
                        
                        Image(systemName: isPlayingBack ? "pause.fill" : "play.fill")
                            .font(.system(size: 30))
                            .foregroundColor(.white)
                    }
                }
                .disabled(recordedText.isEmpty)
            }
            .padding(.bottom, 30)
        }
        .padding()
        .onChange(of: speechManager.isRecording) { _, isRecording in
            if !isRecording && !speechManager.transcribedText.isEmpty {
                recordedText = speechManager.transcribedText
                countFillerWords()
            }
        }
    }
    
    private func toggleRecording() {
        if speechManager.isRecording {
            speechManager.stopRecording()
        } else {
            recordedText = ""
            fillerWordsCount = 0
            do {
                try speechManager.startRecording()
            } catch {
                print("Error starting recording: \(error.localizedDescription)")
            }
        }
    }
    
    private func togglePlayback() {
        if isPlayingBack {
            speechManager.stopSpeaking()
            isPlayingBack = false
        } else {
            speechManager.speak(recordedText)
            isPlayingBack = true
            
            // Set isPlayingBack to false when speech finishes
            DispatchQueue.main.asyncAfter(deadline: .now() + Double(recordedText.count) / 10) {
                self.isPlayingBack = false
            }
        }
    }
    
    private func countFillerWords() {
        // Common filler words to detect
        let fillerWords = ["um", "uh", "er", "ah", "like", "you know", "hmm", "uhm", "actually", "basically", "literally", "really", "very", "just", "so"]
        
        // Convert text to lowercase for case-insensitive matching
        let lowercasedText = recordedText.lowercased()
        
        // Count occurrences of each filler word
        var count = 0
        for word in fillerWords {
            // Create a pattern that matches whole words only
            let pattern = "\\b\(word)\\b"
            if let regex = try? NSRegularExpression(pattern: pattern) {
                let matches = regex.matches(in: lowercasedText, range: NSRange(lowercasedText.startIndex..., in: lowercasedText))
                count += matches.count
            }
        }
        
        fillerWordsCount = count
    }
}

#Preview {
    AudioRecordingView()
}
