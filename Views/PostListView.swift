import SwiftUI
import Firebase
import RevenueCat

struct PostListView: View {
    @StateObject private var viewModel = PostViewModel()
    @ObservedObject private var subscriptionService = SubscriptionService.shared
    @State private var showingCreatePost = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var showingPaywall = false

    var body: some View {
        NavigationStack {
            ZStack {
                // Background
                Color(UIColor.systemBackground)
                    .ignoresSafeArea()

                // Content
                VStack {
                    if !subscriptionService.isPostFeatureAvailable() {
                        // Locked UI for premium feature
                        VStack(spacing: 24) {
                            Image(systemName: "lock.fill")
                                .font(.system(size: 70))
                                .foregroundColor(.orange)
                                .padding()
                                .background(
                                    Circle()
                                        .fill(Color.orange.opacity(0.2))
                                        .frame(width: 150, height: 150)
                                )

                            Text("Premium Feature")
                                .font(.title)
                                .fontWeight(.bold)
                                .multilineTextAlignment(.center)

                            Text("The Community Posts feature is available exclusively to premium subscribers.")
                                .font(.headline)
                                .foregroundStyle(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)

                            // Features List
                            VStack(alignment: .leading, spacing: 12) {
                                FeatureRow(icon: "text.bubble.fill", text: "Share your thoughts with the community")
                                FeatureRow(icon: "hand.thumbsup.fill", text: "Like and comment on posts")
                                FeatureRow(icon: "person.2.fill", text: "Connect with other learners")
                                FeatureRow(icon: "lightbulb.fill", text: "Get inspired by others' experiences")
                            }
                            .padding()
                            .background(Color(.systemGray6).opacity(0.7))
                            .cornerRadius(16)
                            .padding(.horizontal)

                            // Unlock button
                            Button(action: {
                                showingPaywall = true
                            }) {
                                HStack {
                                    Image(systemName: "star.fill")
                                        .foregroundColor(.yellow)
                                    Text("Unlock Premium")
                                        .fontWeight(.semibold)
                                        .foregroundColor(.white)
                                }
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(
                                    LinearGradient(
                                        gradient: Gradient(colors: [Color.blue, Color.purple]),
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .cornerRadius(12)
                            }
                            .padding(.horizontal)
                            .padding(.top, 16)
                        }
                        .padding()
                    } else if viewModel.isLoading {
                        ProgressView("Loading posts...")
                            .padding()
                    } else if viewModel.posts.isEmpty {
                        VStack(spacing: 20) {
                            Image(systemName: "text.bubble")
                                .font(.system(size: 60))
                                .foregroundColor(.gray)

                            Text("No posts yet")
                                .font(.title2)
                                .fontWeight(.medium)

                            Text("Be the first to share your thoughts!")
                                .foregroundColor(.secondary)

                            Button(action: {
                                showingCreatePost = true
                            }) {
                                Text("Create Post")
                                    .fontWeight(.semibold)
                                    .padding(.horizontal, 20)
                                    .padding(.vertical, 10)
                                    .background(Color.blue)
                                    .foregroundColor(.white)
                                    .cornerRadius(8)
                            }
                            .padding(.top, 10)
                        }
                        .padding()
                    } else {
                        // Use ScrollViewReader to maintain scroll position
                        ScrollViewReader { scrollProxy in
                            ScrollView {
                                LazyVStack(spacing: 16) {
                                    // Use a ForEach with a stable ID that doesn't change when posts are updated
                                    // This ensures posts aren't reordered when a comment is added
                                    ForEach(viewModel.posts) { post in
                                        PostCardView(post: post, viewModel: viewModel, scrollProxy: scrollProxy)
                                            .padding(.horizontal)
                                            .transition(.opacity.combined(with: .move(edge: .top)))
                                            // Use a stable ID that doesn't change when the post is updated
                                            .id("post-\(post.id ?? UUID().uuidString)")
                                    }
                                }
                                .padding(.vertical)
                                // Don't animate based on posts count to prevent UI jumps
                            }
                            .refreshable {
                                // Manual refresh when user pulls down
                                viewModel.fetchAllPosts()
                            }
                        }
                    }
                }
            }
            .navigationTitle("Community Posts")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        if subscriptionService.isPostFeatureAvailable() {
                            showingCreatePost = true
                        } else {
                            showingPaywall = true
                        }
                    }) {
                        Image(systemName: "square.and.pencil")
                    }
                }
            }
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        if subscriptionService.isPostFeatureAvailable() {
                            viewModel.fetchAllPosts()
                        }
                    }) {
                        Image(systemName: "arrow.clockwise")
                    }
                    .disabled(!subscriptionService.isPostFeatureAvailable())
                    .opacity(subscriptionService.isPostFeatureAvailable() ? 1.0 : 0.5)
                }
            }
            .sheet(isPresented: $showingCreatePost) {
                CreatePostView { success, message, newPost in
                    if success, let newPost = newPost {
                        // Manually add the new post to the beginning of the posts array
                        // This ensures the current user sees their post immediately
                        DispatchQueue.main.async {
                            // Insert at the beginning since posts are sorted by timestamp (newest first)
                            viewModel.posts.insert(newPost, at: 0)
                            print("✅ Manually added new post to posts array in PostListView")

                            // Also ensure it's in the userPosts array for the profile view
                            if let currentUser = AuthService.shared.user, newPost.authorId == currentUser.uid {
                                if !viewModel.userPosts.contains(where: { $0.id == newPost.id }) {
                                    viewModel.userPosts.insert(newPost, at: 0)
                                    print("✅ Manually added new post to userPosts array in PostListView")
                                }
                            }
                        }
                    } else if let message = message {
                        alertMessage = message
                        showingAlert = true
                    }
                }
                .environmentObject(AuthService.shared)
            }
            .sheet(isPresented: $showingPaywall) {
                PaywallView()
                    .onDisappear {
                        // Refresh subscription status when paywall is dismissed
                        subscriptionService.forceCheckSubscriptionStatus()
                    }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("Error"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
            .onAppear {
                if subscriptionService.isPostFeatureAvailable() {
                    viewModel.fetchAllPosts()
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("SubscriptionStatusChanged"))) { _ in
                // Refresh posts if subscription status changes to subscribed
                if subscriptionService.isPostFeatureAvailable() {
                    viewModel.fetchAllPosts()
                }
            }
        }
    }
}

struct PostCardView: View {
    // MARK: - Properties
    let post: Post
    var viewModel: PostViewModel // Changed from @ObservedObject to avoid binding issues
    var scrollProxy: ScrollViewProxy? // Add ScrollViewProxy to maintain scroll position
    @EnvironmentObject private var authService: AuthService
    @Environment(\.colorScheme) private var colorScheme

    // State variables - simplified for clarity
    @State private var localPost: Post
    @State private var showingComments = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var commentSectionId = UUID() // Unique ID for the comment section to prevent recreation

    // Like button specific states
    @State private var isLiked = false
    @State private var isUpdating = false
    @State private var animateLike = false
    @State private var likeCount: Int = 0 // Separate state for like count

    // MARK: - Initialization
    init(post: Post, viewModel: PostViewModel, scrollProxy: ScrollViewProxy? = nil) {
        self.post = post
        self.viewModel = viewModel
        self.scrollProxy = scrollProxy
        _localPost = State(initialValue: post)
        _likeCount = State(initialValue: post.likeCount) // Initialize like count

        // We'll handle the notification in the body using onReceive instead

        // Initialize like state
        if let userId = AuthService.shared.user?.uid {
            let isLikedInPost = post.likedBy.contains(userId)
            _isLiked = State(initialValue: isLikedInPost)

            // If not liked in the post, check the separate collection
            if !isLikedInPost, let postId = post.id {
                // This is a background check that will update the UI if a like is found
                checkLikeInSeparateCollection(postId: postId, userId: userId)
            }
        }
    }

    // Check if the user has liked the post in the separate collection
    private func checkLikeInSeparateCollection(postId: String, userId: String) {
        let db = Firestore.firestore()
        let likeDocRef = db.collection("postLikes").document("\(postId)_\(userId)")

        likeDocRef.getDocument { snapshot, error in
            if let error = error {
                print("⚠️ Error checking like in separate collection: \(error.localizedDescription)")
                return
            }

            // Check if the document exists and has liked=true
            if let snapshot = snapshot, snapshot.exists,
               let data = snapshot.data() {

                // Document exists, check if liked is true
                let isLiked = data["liked"] as? Bool ?? false

                if isLiked {
                    print("🔍 Found like in separate collection for post \(postId) by user \(userId)")

                    DispatchQueue.main.async {
                        // Update the like state
                        self.isLiked = true

                        // Update the like count if needed
                        if !self.localPost.likedBy.contains(userId) {
                            self.likeCount += 1

                            // Update the local post
                            var updatedPost = self.localPost
                            updatedPost.likeCount = self.likeCount
                            updatedPost.likedBy.append(userId)
                            self.localPost = updatedPost

                            // Update the post in the view model
                            if let index = self.viewModel.posts.firstIndex(where: { $0.id == postId }) {
                                self.viewModel.posts[index] = updatedPost
                            }
                        }
                    }
                } else {
                    print("🔍 Found unlike in separate collection for post \(postId) by user \(userId)")
                }
            } else {
                print("🔍 No like document found in separate collection for post \(postId) by user \(userId)")
            }
        }
    }

    // MARK: - Computed Properties
    private var cardBackgroundColor: Color {
        colorScheme == .dark ? Color(.systemGray6) : Color.white
    }

    // MARK: - Body
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Author info
            HStack(spacing: 12) {
                VStack(alignment: .leading, spacing: 2) {
                    Text(localPost.authorName)
                        .font(.system(size: 15, weight: .semibold, design: .rounded))
                        .foregroundColor(.primary)

                    Text(localPost.timeAgo)
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }

                Spacer()

                // Time indicator (replacing the post menu)
                Text(localPost.timeAgo)
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
                    .padding(8)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)

            // Post content
            Text(localPost.content)
                .font(.system(size: 16, weight: .regular, design: .rounded))
                .lineLimit(nil)
                .fixedSize(horizontal: false, vertical: true)
                .padding(.horizontal, 16)
                .padding(.vertical, 14)

            // Interaction stats with integrated buttons
            HStack(spacing: 16) {
                // Like count and button
                Button {
                    handleLikeAction()
                } label: {
                    HStack(spacing: 6) {
                        // Like icon
                        if isUpdating {
                            ProgressView()
                                .scaleEffect(0.7)
                                .frame(width: 14, height: 14)
                        } else {
                            Image(systemName: isLiked ? "hand.thumbsup.fill" : "hand.thumbsup")
                                .font(.system(size: 14, weight: isLiked ? .bold : .regular))
                                .foregroundColor(isLiked ? .blue : .secondary)
                                .scaleEffect(animateLike ? 1.2 : 1.0)
                                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: animateLike)
                        }

                        // Like count with highlight effect when changed
                        Text("\(likeCount)")
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(isLiked ? .blue.opacity(0.8) : .secondary)
                            .id("likeCount-\(likeCount)") // Force refresh when count changes
                            .transition(.scale.combined(with: .opacity))
                            .animation(.spring(response: 0.2, dampingFraction: 0.7), value: likeCount)
                    }
                    .padding(.vertical, 4)
                    .padding(.horizontal, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(isLiked ? Color.blue.opacity(0.1) : Color.clear)
                    )
                }
                .buttonStyle(BorderlessButtonStyle())
                .disabled(isUpdating)

                // Comment count and button
                Button {
                    print("🔍 Comment button tapped for post ID: \(localPost.id ?? "unknown")")

                    // Scroll to this post to ensure it stays visible
                    if let scrollProxy = scrollProxy, let postId = localPost.id {
                        withAnimation {
                            scrollProxy.scrollTo("post-\(postId)", anchor: .top)
                        }
                    }

                    // Use a more refined spring animation with better parameters for smoother effect
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.75, blendDuration: 0.2)) {
                        showingComments.toggle()

                        if showingComments {
                            if let postId = localPost.id {
                                // Only fetch comments if we don't already have them
                                if viewModel.comments.isEmpty || viewModel.selectedPostId != postId {
                                    viewModel.fetchComments(for: postId)
                                }
                            }
                        }
                    }
                } label: {
                    HStack(spacing: 6) {
                        Image(systemName: "bubble.left")
                            .font(.system(size: 14))
                            .foregroundColor(showingComments ? .blue : .secondary)
                            // Add subtle scale animation to the icon
                            .scaleEffect(showingComments ? 1.1 : 1.0)
                            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: showingComments)

                        Text("\(localPost.commentCount)")
                            .font(.system(size: 13))
                            .foregroundColor(showingComments ? .blue : .secondary)
                    }
                    .padding(.vertical, 4)
                    .padding(.horizontal, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(showingComments ? Color.blue.opacity(0.1) : Color.clear)
                    )
                    // Add subtle scale animation to the entire button
                    .scaleEffect(showingComments ? 1.05 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: showingComments)
                }
                .buttonStyle(BorderlessButtonStyle())

                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)

            // Comments section
            if showingComments {
                // Use a fixed ID for the CommentView to prevent it from being recreated
                // when the post is updated
                CommentView(viewModel: viewModel, postId: localPost.id ?? "")
                    // Use a stable ID that DOESN'T change when comment count changes
                    // This ensures the view is NOT recreated when comments are added
                    .id("commentView-\(localPost.id ?? "")-\(commentSectionId)")
                    .frame(maxHeight: 400)
                    // Simplified transition with smoother animation
                    .transition(.opacity.combined(with: .move(edge: .bottom)))
                    // Add a subtle shadow for depth
                    .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
                    // Update local post when the post in viewModel changes
                    .onChange(of: viewModel.posts) { newPosts in
                        if let postId = localPost.id, let updatedPost = newPosts.first(where: { $0.id == postId }) {
                            // Only update if the comment count has changed
                            if updatedPost.commentCount != localPost.commentCount {
                                print("📊 Updating localPost comment count from \(localPost.commentCount) to \(updatedPost.commentCount)")
                                // Create a copy of the updated post
                                var updatedPostCopy = updatedPost
                                // Update the local post while ensuring the comment section stays open
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                    localPost = updatedPostCopy
                                }
                                // Make sure we're still showing comments
                                print("🔍 Ensuring comment section stays open after post update")
                            }
                        }
                    }
                    // Add an onDisappear handler to ensure we don't lose comments when the view updates
                    .onDisappear {
                        print("🔍 CommentView disappeared, but we'll keep showingComments state: \(showingComments)")

                        // CRITICAL: Force the comment section to stay open
                        if showingComments {
                            print("🔍 Forcing comment section to stay open for post ID: \(localPost.id ?? "unknown")")

                            // Post a notification to keep the comment section open
                            if let postId = localPost.id {
                                NotificationCenter.default.post(
                                    name: NSNotification.Name("KeepCommentSectionOpen"),
                                    object: nil,
                                    userInfo: ["postId": postId]
                                )
                            }

                            // Scroll to this post to ensure it stays visible
                            if let scrollProxy = scrollProxy, let postId = localPost.id {
                                DispatchQueue.main.async {
                                    withAnimation {
                                        scrollProxy.scrollTo("post-\(postId)", anchor: .top)
                                    }
                                }
                            }
                        }
                    }
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(cardBackgroundColor)
                .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 2)
        )
        .alert(isPresented: $showingAlert) {
            Alert(
                title: Text("Error"),
                message: Text(alertMessage),
                dismissButton: .default(Text("OK"))
            )
        }
        // Update comment count when posts array changes
        // Listen for notifications to keep comment section open
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("KeepCommentSectionOpen"))) { notification in
            if let postId = notification.userInfo?["postId"] as? String, postId == localPost.id {
                print("🔍 Received notification to keep comment section open for post ID: \(postId)")

                // Force the comment section to stay open
                if !showingComments {
                    print("🔍 Forcing comment section to open for post ID: \(postId)")
                    showingComments = true
                }

                // Scroll to this post to ensure it stays visible
                if let scrollProxy = scrollProxy {
                    DispatchQueue.main.async {
                        withAnimation {
                            scrollProxy.scrollTo("post-\(postId)", anchor: .top)
                        }
                    }
                }
            }
        }
        .onChange(of: viewModel.posts) { newPosts in
            if let postId = localPost.id, let updatedPost = newPosts.first(where: { $0.id == postId }) {
                // Only update if the comment count has changed
                if updatedPost.commentCount != localPost.commentCount {
                    print("📊 Updating localPost comment count from \(localPost.commentCount) to \(updatedPost.commentCount)")

                    // Store the current showingComments state
                    let wasShowingComments = showingComments

                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        // Create a copy of the updated post to preserve the showingComments state
                        var updatedPostCopy = updatedPost
                        // Keep the comment section open if it was already open
                        if wasShowingComments {
                            print("🔍 Preserving showingComments state (keeping comment section open)")
                            // Update the local post while preserving the comment section state
                            localPost = updatedPostCopy

                            // CRITICAL: Force the comment section to stay open
                            DispatchQueue.main.async {
                                showingComments = true

                                // Scroll to this post to ensure it stays visible
                                if let scrollProxy = scrollProxy {
                                    withAnimation {
                                        scrollProxy.scrollTo("post-\(postId)", anchor: .top)
                                    }
                                }
                            }
                        } else {
                            localPost = updatedPostCopy
                        }
                    }
                }
            }
        }
    }

    // MARK: - Methods
    private func handleLikeAction() {
        // Check if user is authenticated
        guard let currentUser = authService.user else {
            print("❌ User not authenticated")
            alertMessage = "You must be logged in to like posts"
            showingAlert = true
            return
        }

        guard let postId = localPost.id, !isUpdating else {
            print("❌ Invalid post ID or already updating")
            return
        }

        let userId = currentUser.uid
        print("🔍 Current user ID: \(userId)")
        print("🔍 Post author ID: \(localPost.authorId)")
        print("🔍 Post ID: \(postId)")

        // Get current state before toggling
        let wasLiked = isLiked
        let previousLikeCount = likeCount

        print("🔍 Current like state: \(wasLiked ? "liked" : "not liked")")
        print("🔍 Current like count: \(previousLikeCount)")
        print("🔍 Current likedBy array: \(localPost.likedBy)")

        // Set updating flag
        isUpdating = true

        // The new state will be the opposite of the current state
        let newLikeState = !isLiked
        print("🔍 New like state will be: \(newLikeState ? "liked" : "unliked")")

        // Update server first - don't update UI until we get a response
        viewModel.toggleLikeWithoutRefresh(postId: postId, userId: userId, isLiked: newLikeState) { success, message in
            DispatchQueue.main.async {
                self.isUpdating = false

                if !success {
                    // Show detailed error message
                    let errorMsg = message ?? "Unknown error occurred"
                    print("❌ Like action failed: \(errorMsg)")
                    self.alertMessage = "Like action failed: \(errorMsg)"
                    self.showingAlert = true
                } else {
                    print("✅ Server update successful for post \(postId)")
                    print("✅ Updating UI with new like state: \(newLikeState ? "liked" : "unliked")")

                    // Now update UI after successful server update
                    // Toggle like state
                    self.isLiked = newLikeState
                    print("✅ Updated isLiked to: \(self.isLiked)")

                    // Create a copy of the post to modify
                    var updatedPost = self.localPost

                    if newLikeState {
                        // Add like
                        if !updatedPost.likedBy.contains(userId) {
                            updatedPost.likedBy.append(userId)
                            updatedPost.likeCount += 1
                            print("🔍 Added user ID to likedBy array: \(updatedPost.likedBy)")
                        }

                        // Remove dislike if present
                        if updatedPost.dislikedBy.contains(userId) {
                            updatedPost.dislikeCount = max(0, updatedPost.dislikeCount - 1)
                            updatedPost.dislikedBy.removeAll { $0 == userId }
                            print("🔍 Removed user ID from dislikedBy array")
                        }
                    } else {
                        // Remove like
                        updatedPost.likedBy.removeAll { $0 == userId }
                        updatedPost.likeCount = max(0, updatedPost.likeCount - 1)
                        print("🔍 Removed user ID from likedBy array: \(updatedPost.likedBy)")
                    }

                    // Update like count with animation
                    withAnimation(.spring(response: 0.2, dampingFraction: 0.7)) {
                        self.likeCount = updatedPost.likeCount
                    }

                    // Update local post
                    self.localPost = updatedPost

                    // Trigger animation
                    self.animateLike = true

                    // Reset animation after delay
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        self.animateLike = false
                    }

                    // Ensure the post in the view model is updated
                    if let index = self.viewModel.posts.firstIndex(where: { $0.id == postId }) {
                        self.viewModel.posts[index] = updatedPost
                    }
                }
            }
        }
    }
}

#Preview {
    PostListView()
        .environmentObject(AuthService.shared)
}
