import SwiftUI
import AVFoundation

/// A reusable audio player view that can be used throughout the app
struct ReusableAudioPlayerView: View {
    // MARK: - Properties

    /// The audio file name to play
    let audioFileName: String

    /// Optional title to display (defaults to file name if not provided)
    var title: String?

    /// Optional subtitle to display
    var subtitle: String?

    /// Optional accent color for the player (defaults to blue)
    var accentColor: Color = .blue

    /// Optional compact mode for smaller UI (defaults to false)
    var compactMode: Bool = false

    /// Optional callback when playback starts
    var onPlaybackStarted: (() -> Void)?

    /// Optional callback when playback ends
    var onPlaybackEnded: (() -> Void)?

    /// Audio engine for playback
    @StateObject private var audioEngine = AudioEngine()

    // MARK: - Body

    var body: some View {
        VStack(spacing: compactMode ? 12 : 20) {
            // Title and subtitle
            if let title = title {
                Text(title)
                    .font(compactMode ? .headline : .title2)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal)
            }

            if let subtitle = subtitle {
                Text(subtitle)
                    .font(compactMode ? .subheadline : .headline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal)
            }

            // Player card
            VStack(spacing: compactMode ? 10 : 16) {
                // Audio visualization (animated waveform)
                AudioWaveformView(
                    isPlaying: $audioEngine.isPlaying,
                    currentTime: $audioEngine.currentTime,
                    duration: $audioEngine.duration
                )
                .frame(height: compactMode ? 40 : 60)
                .padding(.vertical, compactMode ? 5 : 10)

                // Progress bar
                VStack(spacing: 4) {
                    Slider(
                        value: $audioEngine.progress,
                        in: 0...1,
                        onEditingChanged: { editing in
                            if !editing && audioEngine.duration > 0 {
                                audioEngine.seek(to: audioEngine.progress * audioEngine.duration)
                            }
                        }
                    )
                    .accentColor(accentColor)
                    .disabled(audioEngine.loadingState != .loaded)

                    // Time labels
                    HStack {
                        Text(formatTime(audioEngine.currentTime))
                            .font(.caption)
                            .foregroundColor(.gray)

                        Spacer()

                        Text(formatTime(audioEngine.duration))
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
                .padding(.horizontal)

                // Controls
                HStack(spacing: compactMode ? 20 : 30) {
                    // Rewind button
                    Button {
                        audioEngine.seekRelative(by: -10)
                    } label: {
                        Image(systemName: "gobackward.10")
                            .font(.system(size: compactMode ? 18 : 24))
                            .foregroundColor(accentColor)
                    }
                    .disabled(audioEngine.loadingState != .loaded)

                    // Play/Pause button
                    Button {
                        if !audioEngine.isPlaying {
                            onPlaybackStarted?()
                        }
                        audioEngine.togglePlayback()
                    } label: {
                        ZStack {
                            Circle()
                                .fill(accentColor)
                                .frame(width: compactMode ? 50 : 60, height: compactMode ? 50 : 60)

                            Image(systemName: audioEngine.isPlaying ? "pause.fill" : "play.fill")
                                .font(.system(size: compactMode ? 20 : 24))
                                .foregroundColor(.white)
                        }
                    }
                    .disabled(audioEngine.loadingState != .loaded)

                    // Forward button
                    Button {
                        audioEngine.seekRelative(by: 10)
                    } label: {
                        Image(systemName: "goforward.10")
                            .font(.system(size: compactMode ? 18 : 24))
                            .foregroundColor(accentColor)
                    }
                    .disabled(audioEngine.loadingState != .loaded)
                }
                .padding(.vertical, compactMode ? 5 : 10)

                // Status message (only in non-compact mode)
                if !compactMode {
                    statusMessage
                        .frame(height: 20)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
            )
            .padding(.horizontal)
        }
        .padding(.vertical)
        .onAppear {
            // Load the audio file when the view appears
            audioEngine.loadAudio(named: audioFileName)
        }
        .onDisappear {
            // Clean up resources when the view disappears
            audioEngine.cleanup()
        }
        .onChange(of: audioEngine.isPlaying) { newValue in
            // Call the playback ended callback when playback stops
            if !newValue && audioEngine.currentTime > 0 &&
               abs(audioEngine.currentTime - audioEngine.duration) < 0.5 {
                onPlaybackEnded?()
            }
        }
    }

    // MARK: - Helper Views

    /// Status message based on loading state
    private var statusMessage: some View {
        Group {
            switch audioEngine.loadingState {
            case .notLoaded, .loading:
                Text("Loading audio...")
                    .foregroundColor(.gray)
            case .loaded:
                if !audioEngine.isPlaying {
                    Text("Ready to play")
                        .foregroundColor(.gray)
                } else {
                    Text("Playing")
                        .foregroundColor(accentColor)
                }
            case .error(let message):
                Text("Error: \(message)")
                    .foregroundColor(.red)
                    .multilineTextAlignment(.center)
                    .font(.caption)
            }
        }
    }

    // MARK: - Helper Functions

    /// Format time in seconds to MM:SS format
    private func formatTime(_ timeInSeconds: TimeInterval) -> String {
        let minutes = Int(timeInSeconds) / 60
        let seconds = Int(timeInSeconds) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - Preview

#Preview("Standard") {
    ReusableAudioPlayerView(
        audioFileName: "Fundamentals of Effective Communication",
        title: "Fundamentals of Effective Communication",
        subtitle: "Learn the basics of effective communication"
    )
    .padding()
}

#Preview("Compact") {
    ReusableAudioPlayerView(
        audioFileName: "Fundamentals of Effective Communication",
        title: "Fundamentals of Effective Communication",
        subtitle: "Compact mode with custom color",
        accentColor: .purple,
        compactMode: true
    )
    .padding()
}
