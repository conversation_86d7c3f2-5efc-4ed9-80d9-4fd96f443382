import SwiftUI

struct LessonPlansListView: View {
    @ObservedObject var viewModel: CommunicationViewModel
    @ObservedObject private var subscriptionService = SubscriptionService.shared
    @State private var showingPaywall = false
    @State private var selectedLockedLesson: LessonPlan?
    @State private var refreshTrigger = UUID()
    @Environment(\.colorScheme) private var colorScheme

    // Dynamic colors based on color scheme
    private var textColor: Color {
        colorScheme == .dark ? .white : .black
    }

    private var subtleTextColor: Color {
        colorScheme == .dark ? .white.opacity(0.8) : .black.opacity(0.8)
    }

    private var glassStrokeGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: colorScheme == .dark ?
                [Color.white.opacity(0.5), Color.white.opacity(0.1)] :
                [Color.white.opacity(0.8), Color.black.opacity(0.1)]
            ),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    var body: some View {
        // Use LazyVStack for better scrolling performance
        LazyVStack(alignment: .leading, spacing: 16) {
            // Only show first two categories initially for faster loading
            ForEach(Array(viewModel.lessonCategories.prefix(2))) { category in
                VStack(alignment: .leading, spacing: 8) {
                    // Category header
                    Text(category.title)
                        .font(.headline)
                        .foregroundColor(textColor)
                        .padding(.horizontal, 25)
                        .padding(.vertical, 8)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    // Category description
                    Text(category.description)
                        .font(.subheadline)
                        .foregroundStyle(subtleTextColor)
                        .padding(.horizontal, 25)
                        .padding(.bottom, 4)

                    // Limit initial lessons to improve performance
                    ForEach(Array(category.lessons.prefix(4))) { lesson in
                        let isLocked = !subscriptionService.isLessonAvailable(title: lesson.title)

                        Group {
                            if isLocked {
                                lockedLessonView(lesson: lesson)
                            } else {
                                NavigationLink(destination: TextChatView(lesson: lesson, viewModel: viewModel)) {
                                    lessonRowContent(lesson: lesson)
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .padding(.horizontal)
                        .id("\(lesson.id)-\(isLocked ? "locked" : "unlocked")")
                    }
                }
                .padding(.bottom, 16)
            }

            // Load remaining categories after a delay
            if viewModel.lessonCategories.count > 2 {
                ForEach(Array(viewModel.lessonCategories.dropFirst(2))) { category in
                    VStack(alignment: .leading, spacing: 8) {
                        Text(category.title)
                            .font(.headline)
                            .foregroundColor(textColor)
                            .padding(.horizontal, 25)
                            .padding(.top, 4)

                        Text(category.description)
                            .font(.subheadline)
                            .foregroundStyle(subtleTextColor)
                            .padding(.horizontal, 25)
                            .padding(.bottom, 4)

                        ForEach(category.lessons) { lesson in
                            let isLocked = !subscriptionService.isLessonAvailable(title: lesson.title)

                            Group {
                                if isLocked {
                                    lockedLessonView(lesson: lesson)
                                } else {
                                    NavigationLink(destination: TextChatView(lesson: lesson, viewModel: viewModel)) {
                                        lessonRowContent(lesson: lesson)
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                }
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .padding(.horizontal)
                            .id("\(lesson.id)-\(isLocked ? "locked" : "unlocked")")
                        }
                    }
                    .padding(.bottom, 16)
                }
            }

            // Add bottom padding
            Spacer().frame(height: 20)
        }
        .id(refreshTrigger)
        .sheet(isPresented: $showingPaywall) {
            PaywallView()
                .onDisappear {
                    subscriptionService.forceCheckSubscriptionStatus()
                    refreshTrigger = UUID()
                }
        }
        .onAppear {
            subscriptionService.forceCheckSubscriptionStatus()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("SubscriptionStatusChanged"))) { _ in
            refreshTrigger = UUID()
        }
    }

    // Optimized lesson row with simplified styling
    private func lessonRowContent(lesson: LessonPlan) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 6) {
                Text(lesson.title)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(textColor)

                Text(lesson.description)
                    .font(.caption)
                    .foregroundStyle(subtleTextColor)
            }
            .padding(.vertical, 10)
            .padding(.horizontal, 4)

            Spacer()

            if hasChatHistory(for: lesson.id.uuidString) {
                Image(systemName: "bubble.left.fill")
                    .font(.caption)
                    .foregroundColor(colorScheme == .dark ? .white.opacity(0.9) : .blue.opacity(0.8))
            }

            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(textColor.opacity(0.7))
                .padding(.leading, 4)
        }
        .padding(.horizontal)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.secondarySystemBackground))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.gray.opacity(0.2), lineWidth: 0.5)
        )
    }

    // Optimized locked lesson view with simplified styling
    private func lockedLessonView(lesson: LessonPlan) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 6) {
                Text(lesson.title)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(textColor.opacity(0.6))

                Text(lesson.description)
                    .font(.caption)
                    .foregroundStyle(textColor.opacity(0.5))
            }
            .padding(.vertical, 10)
            .padding(.horizontal, 4)

            Spacer()

            Image(systemName: "lock.fill")
                .font(.caption)
                .foregroundColor(textColor.opacity(0.5))
                .padding(.leading, 4)
        }
        .padding(.horizontal)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.tertiarySystemBackground))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.gray.opacity(0.15), lineWidth: 0.5)
        )
        .opacity(0.7)
        .onTapGesture {
            selectedLockedLesson = lesson
            showingPaywall = true
        }
    }

    // Check if there's saved chat history for this lesson
    private func hasChatHistory(for lessonId: String) -> Bool {
        return UserDefaults.standard.object(forKey: "chatMessages_\(lessonId)") != nil
    }
}

#Preview {
    NavigationStack {
        ScrollView {
            LessonPlansListView(viewModel: CommunicationViewModel())
        }
    }
}
