import SwiftUI
import AVKit

struct VideoRephraseView: View {
    @StateObject private var speechManager = SpeechManager()
    @State private var messageText = ""
    @State private var recordedText = ""
    @State private var isPlayingVideo = false
    @State private var showingFeedback = false
    @State private var feedbackMessage = ""
    @State private var fillerWordsCount = 0
    @State private var videoPlayer = AVPlayer()
    @State private var selectedVideoIndex = 0
    
    // Sample videos for rephrasing practice
    let sampleVideos = [
        (title: "Introduction to Communication", url: "https://example.com/video1.mp4", transcript: "Effective communication is the cornerstone of successful personal and professional relationships. It involves not just speaking clearly, but also active listening, understanding non-verbal cues, and adapting your message to your audience."),
        (title: "The Power of Body Language", url: "https://example.com/video2.mp4", transcript: "Body language accounts for over 50% of communication. Your posture, facial expressions, and gestures can either reinforce or contradict your spoken words. Being aware of your non-verbal cues can significantly improve how others perceive your message."),
        (title: "Overcoming Communication Barriers", url: "https://example.com/video3.mp4", transcript: "Communication barriers include language differences, cultural misunderstandings, and environmental distractions. Recognizing these barriers and developing strategies to overcome them is essential for effective communication in diverse settings.")
    ]
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            VStack(spacing: 8) {
                Text("Video Rephrasing Practice")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Watch the video and rephrase the content in your own words")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            .padding()
            .frame(maxWidth: .infinity)
            .background(Color(.systemBackground))
            
            ScrollView {
                VStack(spacing: 20) {
                    // Video player
                    VStack(spacing: 8) {
                        Text(sampleVideos[selectedVideoIndex].title)
                            .font(.headline)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        // Video player placeholder (in a real app, this would be a real video)
                        ZStack {
                            Rectangle()
                                .fill(Color.black)
                                .aspectRatio(16/9, contentMode: .fit)
                                .cornerRadius(12)
                            
                            if isPlayingVideo {
                                // This would be a real video player in a production app
                                Image(systemName: "play.fill")
                                    .font(.system(size: 50))
                                    .foregroundColor(.white.opacity(0.5))
                            } else {
                                Button(action: {
                                    isPlayingVideo = true
                                    // In a real app, this would play the actual video
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                                        isPlayingVideo = false
                                    }
                                }) {
                                    VStack {
                                        Image(systemName: "play.circle.fill")
                                            .font(.system(size: 50))
                                            .foregroundColor(.white)
                                        
                                        Text("Tap to play video")
                                            .foregroundColor(.white)
                                            .font(.caption)
                                            .padding(.top, 4)
                                    }
                                }
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .shadow(radius: 5)
                    }
                    .padding(.horizontal)
                    
                    // Video transcript
                    VStack(spacing: 8) {
                        Text("Video Transcript:")
                            .font(.headline)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        Text(sampleVideos[selectedVideoIndex].transcript)
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.blue.opacity(0.1))
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                            )
                    }
                    .padding(.horizontal)
                    
                    // Recording section
                    VStack(spacing: 12) {
                        Text("Your Rephrasing:")
                            .font(.headline)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        if speechManager.isRecording {
                            // Show transcription while recording
                            ScrollView {
                                Text(speechManager.transcribedText.isEmpty ? "Speak now..." : speechManager.transcribedText)
                                    .padding()
                                    .frame(maxWidth: .infinity, alignment: .leading)
                            }
                            .frame(height: 150)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.red.opacity(0.1))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .strokeBorder(Color.red.opacity(0.3), lineWidth: 1)
                                    )
                            )
                        } else if !recordedText.isEmpty {
                            // Show recorded text when not recording
                            ScrollView {
                                Text(recordedText)
                                    .padding()
                                    .frame(maxWidth: .infinity, alignment: .leading)
                            }
                            .frame(height: 150)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color(.systemGray6))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .strokeBorder(Color.gray.opacity(0.3), lineWidth: 1)
                                    )
                            )
                            
                            // Show filler words count if we have recorded text
                            HStack {
                                Image(systemName: "exclamationmark.bubble")
                                    .foregroundColor(.orange)
                                
                                Text("Filler words detected: \(fillerWordsCount)")
                                    .font(.subheadline)
                                    .foregroundColor(.orange)
                            }
                            .padding(.top, 4)
                        } else {
                            // Empty state
                            VStack {
                                Image(systemName: "mic.circle")
                                    .font(.system(size: 40))
                                    .foregroundColor(.blue)
                                
                                Text("Tap the microphone button below to start recording your rephrasing")
                                    .font(.subheadline)
                                    .multilineTextAlignment(.center)
                                    .padding()
                            }
                            .frame(height: 150)
                            .frame(maxWidth: .infinity)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color(.systemGray6))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .strokeBorder(Color.gray.opacity(0.3), lineWidth: 1)
                                    )
                            )
                        }
                        
                        // Controls
                        HStack(spacing: 40) {
                            // Record button
                            Button(action: toggleRecording) {
                                ZStack {
                                    Circle()
                                        .fill(speechManager.isRecording ? Color.red : Color.blue)
                                        .frame(width: 60, height: 60)
                                        .shadow(radius: 5)
                                    
                                    Image(systemName: speechManager.isRecording ? "stop.fill" : "mic.fill")
                                        .font(.system(size: 24))
                                        .foregroundColor(.white)
                                }
                            }
                            
                            if !recordedText.isEmpty {
                                // Submit button
                                Button(action: submitRephrasing) {
                                    ZStack {
                                        Circle()
                                            .fill(Color.green)
                                            .frame(width: 60, height: 60)
                                            .shadow(radius: 5)
                                        
                                        Image(systemName: "checkmark")
                                            .font(.system(size: 24))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                        }
                        .padding(.vertical, 16)
                    }
                    .padding(.horizontal)
                }
                .padding(.vertical, 20)
            }
        }
        .onChange(of: speechManager.isRecording) { _, isRecording in
            if !isRecording && !speechManager.transcribedText.isEmpty {
                recordedText = speechManager.transcribedText
                countFillerWords()
            }
        }
        .alert("Feedback", isPresented: $showingFeedback) {
            Button("Next Video", role: .cancel) {
                moveToNextVideo()
            }
        } message: {
            Text(feedbackMessage)
        }
    }
    
    private func toggleRecording() {
        if speechManager.isRecording {
            speechManager.stopRecording()
        } else {
            recordedText = ""
            fillerWordsCount = 0
            do {
                try speechManager.startRecording()
            } catch {
                print("Error starting recording: \(error.localizedDescription)")
            }
        }
    }
    
    private func countFillerWords() {
        // Common filler words to detect
        let fillerWords = ["um", "uh", "er", "ah", "like", "you know", "hmm", "uhm", "actually", "basically", "literally", "really", "very", "just", "so"]
        
        // Convert text to lowercase for case-insensitive matching
        let lowercasedText = recordedText.lowercased()
        
        // Count occurrences of each filler word
        var count = 0
        for word in fillerWords {
            // Create a pattern that matches whole words only
            let pattern = "\\b\(word)\\b"
            if let regex = try? NSRegularExpression(pattern: pattern) {
                let matches = regex.matches(in: lowercasedText, range: NSRange(lowercasedText.startIndex..., in: lowercasedText))
                count += matches.count
            }
        }
        
        fillerWordsCount = count
    }
    
    private func submitRephrasing() {
        // Calculate similarity score (this would be more sophisticated in a real app)
        let originalWords = Set(sampleVideos[selectedVideoIndex].transcript.lowercased().split(separator: " ").map { String($0) })
        let rephrasedWords = Set(recordedText.lowercased().split(separator: " ").map { String($0) })
        
        // Calculate word count
        let originalWordCount = sampleVideos[selectedVideoIndex].transcript.split(separator: " ").count
        let rephrasedWordCount = recordedText.split(separator: " ").count
        
        // Generate feedback
        if fillerWordsCount > 5 {
            feedbackMessage = "You used \(fillerWordsCount) filler words. Try to reduce these by pausing instead of saying 'um' or 'like'. Your rephrasing captured the main points, but work on being more concise."
        } else if fillerWordsCount > 0 {
            feedbackMessage = "Good job! You used only \(fillerWordsCount) filler words. Your rephrasing was clear and captured most of the key points from the video."
        } else {
            feedbackMessage = "Excellent! You didn't use any filler words. Your rephrasing was clear, concise, and captured the key points from the video."
        }
        
        // Show feedback
        showingFeedback = true
    }
    
    private func moveToNextVideo() {
        // Move to next video or loop back to beginning
        selectedVideoIndex = (selectedVideoIndex + 1) % sampleVideos.count
        
        // Reset state
        recordedText = ""
        fillerWordsCount = 0
    }
}

#Preview {
    VideoRephraseView()
}
