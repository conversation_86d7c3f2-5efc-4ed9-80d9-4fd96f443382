import SwiftUI

struct CommentView: View {
    @ObservedObject var viewModel: PostViewModel
    @EnvironmentObject private var authService: AuthService
    @Environment(\.colorScheme) private var colorScheme
    let postId: String

    @State private var commentText = ""
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var animateNewComment = false
    @FocusState private var isCommentFieldFocused: Bool

    // Computed property to get comments for this post only
    private var comments: [Comment] {
        let filteredComments = viewModel.comments.filter { $0.postId == postId }
        print("🔍 CommentView - Filtered comments count: \(filteredComments.count) for postId: \(postId)")
        return filteredComments
    }

    private var inputBackgroundColor: Color {
        colorScheme == .dark ? Color(.systemGray6) : Color(.systemGray6).opacity(0.5)
    }

    var body: some View {
        VStack(spacing: 0) {
            // Comments header
            HStack {
                Text("Comments")
                    .font(.system(size: 16, weight: .semibold, design: .rounded))
                    .foregroundColor(.primary)

                Spacer()

                // Debug button to manually refresh comments
                Button(action: {
                    print("🔍 Manual refresh button tapped for postId: \(postId)")
                    viewModel.fetchComments(for: postId)
                }) {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 14))
                        .foregroundColor(.blue)
                }
                .padding(.horizontal, 8)

                HStack(spacing: 4) {
                    Text("\(comments.count)")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)

                    Text(comments.count == 1 ? "comment" : "comments")
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)

            // Divider
            Rectangle()
                .fill(Color.gray.opacity(0.2))
                .frame(height: 1)

            // Comments list
            if viewModel.isLoading && comments.isEmpty {
                Spacer()
                ProgressView()
                    .scaleEffect(1.2)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemBackground))
                            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
                    )
                Spacer()
            } else if comments.isEmpty {
                Spacer()
                VStack(spacing: 16) {
                    Image(systemName: "bubble.left")
                        .font(.system(size: 40))
                        .foregroundColor(.blue.opacity(0.7))

                    Text("No comments yet")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.primary)

                    Text("Be the first to share your thoughts!")
                        .font(.system(size: 14))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.vertical, 30)
                Spacer()
            } else {
                ScrollView {
                    LazyVStack(spacing: 16) {
                        ForEach(comments) { comment in
                            CommentCardView(comment: comment) {
                                deleteComment(commentId: comment.id ?? "")
                            }
                            .padding(.horizontal, 16)
                            // Simplified transition for individual comments
                            .transition(.opacity.combined(with: .move(edge: .bottom)))
                            // Add a subtle shadow for depth
                            .shadow(color: Color.black.opacity(0.03), radius: 3, x: 0, y: 1)
                            // Add a subtle animation when comments appear
                            .id("comment-\(comment.id ?? UUID().uuidString)")
                        }
                    }
                    .padding(.vertical, 12)
                    // Add animation to the entire list
                    .animation(.spring(response: 0.4, dampingFraction: 0.75), value: comments.count)
                }
            }

            // Comment input
            VStack(spacing: 0) {
                Rectangle()
                    .fill(Color.gray.opacity(0.2))
                    .frame(height: 1)

                HStack(spacing: 12) {
                    // User avatar
                    ZStack {
                        Circle()
                            .fill(Color.blue.opacity(0.7))
                            .frame(width: 32, height: 32)

                        if let email = authService.user?.email, !email.isEmpty {
                            Text(String(email.prefix(1).uppercased()))
                                .font(.system(size: 14, weight: .bold))
                                .foregroundColor(.white)
                        } else {
                            Image(systemName: "person.fill")
                                .font(.system(size: 14))
                                .foregroundColor(.white)
                        }
                    }

                    // Text field
                    ZStack(alignment: .trailing) {
                        TextField("Write a comment...", text: $commentText)
                            .padding(.vertical, 10)
                            .padding(.horizontal, 14)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(inputBackgroundColor)
                            )
                            .focused($isCommentFieldFocused)

                        if !commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                            Button(action: submitComment) {
                                Image(systemName: "arrow.up.circle.fill")
                                    .font(.system(size: 24))
                                    .foregroundColor(.blue)
                                    .padding(.trailing, 8)
                            }
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
            }
            .background(
                Rectangle()
                    .fill(colorScheme == .dark ? Color(.systemGray6) : Color.white)
                    .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: -2)
            )
        }
        .onAppear {
            // Fetch comments when the view appears
            print("🔍 CommentView onAppear - Fetching comments for postId: \(postId)")
            print("🔍 Current viewModel.comments count: \(viewModel.comments.count)")
            viewModel.fetchComments(for: postId)
        }
        .alert(isPresented: $showingAlert) {
            Alert(
                title: Text("Error"),
                message: Text(alertMessage),
                dismissButton: .default(Text("OK"))
            )
        }
    }

    private func submitComment() {
        guard !commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty,
              let currentUser = authService.user else {
            print("❌ Comment submission failed: Empty comment or user not authenticated")
            return
        }

        let commentContent = commentText.trimmingCharacters(in: .whitespacesAndNewlines)
        print("🔍 Submitting comment: \"\(commentContent)\" for post ID: \(postId)")

        // IMPORTANT: Store the current showingComments state to ensure it stays open
        let wasShowingComments = true

        // Clear the text field immediately
        commentText = ""
        isCommentFieldFocused = false

        // Create a local comment immediately
        let tempId = UUID().uuidString

        // Get user's name from profile or use email as fallback
        let authorName: String
        if let userProfile = authService.userProfile {
            authorName = userProfile.fullName
        } else {
            authorName = currentUser.email?.components(separatedBy: "@").first ?? "Unknown User"
        }

        let newComment = Comment(
            id: tempId, // Temporary ID
            postId: postId,
            authorId: currentUser.uid,
            authorEmail: currentUser.email ?? "Unknown",
            authorName: authorName,
            content: commentContent,
            timestamp: Date()
        )
        print("🔍 Created local comment with temporary ID: \(tempId)")

        // Add the comment to the local UI immediately with enhanced animation
        withAnimation(.spring(response: 0.4, dampingFraction: 0.7, blendDuration: 0.2)) {
            // Add the new comment to the local view
            viewModel.comments.append(newComment)
            animateNewComment = true

            // Update the post's comment count immediately in the UI
            // This ensures the comment count is updated right away
            if let index = viewModel.posts.firstIndex(where: { $0.id == postId }) {
                var updatedPost = viewModel.posts[index]
                updatedPost.commentCount += 1
                viewModel.posts[index] = updatedPost
                print("✅ Immediately updated post comment count in UI to: \(updatedPost.commentCount)")
            }

            // Scroll to the bottom to show the new comment
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeOut(duration: 0.3)) {
                    // This will trigger a scroll to the bottom in the next render cycle
                    // The delay ensures the comment is added to the view before scrolling
                }
            }

            // Notify the parent view to keep the comment section open
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                NotificationCenter.default.post(
                    name: NSNotification.Name("KeepCommentSectionOpen"),
                    object: nil,
                    userInfo: ["postId": postId]
                )
            }
        }

        // Reset animation flag after delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            self.animateNewComment = false
        }

        // Send to server
        print("🔍 Sending comment to server for post ID: \(postId)")
        viewModel.addComment(to: postId, content: commentContent) { success, message in
            if success {
                print("✅ Comment successfully saved to server")

                // The server-side comment will have a different ID
                // Remove our temporary comment to avoid duplication
                DispatchQueue.main.async {
                    // Remove the temporary comment (it will be replaced by the real one from the server)
                    if let index = self.viewModel.comments.firstIndex(where: { $0.id == tempId }) {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.7, blendDuration: 0.2)) {
                            self.viewModel.comments.remove(at: index)
                        }
                    }

                    // IMPORTANT: Do NOT fetch comments again as it will cause the UI to refresh
                    // and the comment section to close
                    print("🔍 NOT fetching comments again to prevent UI refresh and comment section closing")

                    // Instead, we'll rely on the real-time listener that's already set up
                    // The new comment will be added automatically by the listener

                    // CRITICAL: Notify the parent view to maintain the comment section state
                    // This is a workaround to ensure the comment section stays open
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        // Delay the notification to ensure it's processed after any UI updates
                        NotificationCenter.default.post(
                            name: NSNotification.Name("KeepCommentSectionOpen"),
                            object: nil,
                            userInfo: ["postId": postId]
                        )
                    }
                }
            } else {
                print("❌ Failed to save comment: \(message ?? "Unknown error")")

                // Remove the temporary comment since it failed to save
                DispatchQueue.main.async {
                    if let index = self.viewModel.comments.firstIndex(where: { $0.id == tempId }) {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.7, blendDuration: 0.2)) {
                            self.viewModel.comments.remove(at: index)
                        }
                    }

                    // Show error message
                    self.alertMessage = message ?? "Failed to save comment"
                    self.showingAlert = true
                }
            }
        }
    }

    private func deleteComment(commentId: String) {
        // Update the UI immediately before sending to server
        withAnimation {
            // Remove the comment from the local view immediately
            viewModel.comments.removeAll { $0.id == commentId }

            // Update the post's comment count immediately in the UI
            if let index = viewModel.posts.firstIndex(where: { $0.id == postId }) {
                var updatedPost = viewModel.posts[index]
                updatedPost.commentCount = max(0, updatedPost.commentCount - 1)
                viewModel.posts[index] = updatedPost
                print("✅ Immediately updated post comment count in UI to: \(updatedPost.commentCount)")
            }
        }

        // Send to server - the listener will handle any errors
        viewModel.deleteComment(commentId: commentId, postId: postId) { success, message in
            if !success, let message = message {
                DispatchQueue.main.async {
                    self.alertMessage = message
                    self.showingAlert = true

                    // If there was an error, refresh comments to restore the correct state
                    self.viewModel.fetchComments(for: self.postId)
                }
            }
        }
    }
}

struct CommentCardView: View {
    let comment: Comment
    let onDelete: () -> Void

    @Environment(\.colorScheme) private var colorScheme
    @EnvironmentObject private var authService: AuthService
    @State private var showDeleteConfirmation = false
    @State private var isPressed = false
    @State private var appearAnimation = false

    private var isCurrentUserComment: Bool {
        authService.user?.uid == comment.authorId
    }

    // Get the appropriate background fill based on user and color scheme
    private func backgroundFill() -> AnyShapeStyle {
        if isCurrentUserComment {
            // Use gradient for current user's comments
            return AnyShapeStyle(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.blue.opacity(0.1),
                        Color.blue.opacity(0.05)
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
        } else {
            // Use solid color for other users' comments
            if colorScheme == .dark {
                return AnyShapeStyle(Color(.systemGray6))
            } else {
                return AnyShapeStyle(Color(.systemGray6).opacity(0.5))
            }
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            // Comment bubble
            VStack(alignment: .leading, spacing: 8) {
                // Author and timestamp
                HStack {
                    Text(comment.authorName)
                        .font(.system(size: 13, weight: .semibold))
                        .foregroundColor(isCurrentUserComment ? .blue : .primary)

                    Spacer()

                    Text(comment.timeAgo)
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }

                // Comment content
                Text(comment.content)
                    .font(.system(size: 15, weight: .regular, design: .rounded))
                    .lineLimit(nil)
                    .fixedSize(horizontal: false, vertical: true)
            }
            .padding(12)
            .background(
                ZStack {
                    // Background fill
                    RoundedRectangle(cornerRadius: 16)
                        .fill(backgroundFill())

                    // Border
                    RoundedRectangle(cornerRadius: 16)
                        .strokeBorder(
                            isCurrentUserComment ? Color.blue.opacity(0.2) : Color.gray.opacity(0.1),
                            lineWidth: 1
                        )
                }
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
            // Add a subtle press effect
            .onTapGesture {
                // Provide haptic feedback
                let generator = UIImpactFeedbackGenerator(style: .light)
                generator.impactOccurred()

                // Animate press effect
                withAnimation(.spring(response: 0.2, dampingFraction: 0.6)) {
                    isPressed = true
                }

                // Reset after short delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    withAnimation(.spring(response: 0.2, dampingFraction: 0.6)) {
                        isPressed = false
                    }
                }
            }

            // Delete option (only for current user's comments)
            if isCurrentUserComment {
                HStack {
                    Spacer()

                    Button(action: {
                        showDeleteConfirmation = true
                    }) {
                        Text("Delete")
                            .font(.system(size: 12))
                            .foregroundColor(.red.opacity(0.8))
                            .padding(.vertical, 4)
                            .padding(.horizontal, 8)
                            .background(
                                Group {
                                    Capsule()
                                        .fill(Color.red.opacity(0.1))
                                        .opacity(isPressed ? 0.7 : 0)
                                }
                            )
                    }
                    .buttonStyle(BorderlessButtonStyle())
                }
                .padding(.top, 2)
                .padding(.trailing, 4)
            }
        }
        .opacity(appearAnimation ? 1 : 0)
        .offset(y: appearAnimation ? 0 : 10)
        .onAppear {
            withAnimation(.easeOut(duration: 0.3)) {
                appearAnimation = true
            }
        }
        .confirmationDialog(
            "Delete Comment",
            isPresented: $showDeleteConfirmation,
            titleVisibility: .visible
        ) {
            Button("Delete", role: .destructive, action: onDelete)
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Are you sure you want to delete this comment?")
        }
    }
}

#Preview {
    CommentView(viewModel: PostViewModel(), postId: "previewPostId")
        .environmentObject(AuthService.shared)
}
