import SwiftUI

struct EffectiveCommunicationMapView: View {
    // MARK: - Properties

    // State to track which topics are expanded
    @State private var isMainTopicExpanded = false
    @State private var expandedSubTopics: Set<String> = []

    // Define the subtopics
    let subTopics = [
        "Types of Communication",
        "Key Skills & Techniques",
        "Importance of Effective Communication",
        "Challenges",
        "Foundation of Communication",
        "Improving Skills"
    ]

    // Content for each subtopic
    let topicContent: [String: [String]] = [
        "Types of Communication": [
            "Verbal Communication: Speaking, listening, and language use",
            "Non-verbal Communication: Body language, facial expressions, gestures",
            "Written Communication: Emails, reports, messages",
            "Visual Communication: Charts, graphs, images",
            "Digital Communication: Social media, video calls, online platforms"
        ],
        "Key Skills & Techniques": [
            "Active Listening: Focus, understand, respond, remember",
            "Clarity & Conciseness: Be clear and to the point",
            "Empathy: Understand others' perspectives",
            "Feedback: Constructive and specific",
            "Confidence: Speak with conviction",
            "Open-mindedness: Be receptive to new ideas"
        ],
        "Importance of Effective Communication": [
            "Builds stronger relationships",
            "Prevents misunderstandings",
            "Increases productivity and efficiency",
            "Enhances problem-solving capabilities",
            "Creates a positive work/social environment",
            "Facilitates personal and professional growth"
        ],
        "Challenges": [
            "Language barriers",
            "Cultural differences",
            "Physical barriers (distance, noise)",
            "Emotional barriers (stress, anxiety)",
            "Technological issues",
            "Information overload"
        ],
        "Foundation of Communication": [
            "Intent: The purpose behind communication",
            "Message: The content being communicated",
            "Medium: The channel used for communication",
            "Audience: Who receives the message",
            "Feedback: Response to the communication",
            "Context: The situation surrounding the communication"
        ],
        "Improving Skills": [
            "Practice regularly",
            "Seek feedback from others",
            "Take communication courses",
            "Read books on effective communication",
            "Record and analyze your communication",
            "Observe skilled communicators"
        ]
    ]

    // Environment values
    @Environment(\.colorScheme) private var colorScheme

    // MARK: - Body

    var body: some View {
        ScrollView {
            VStack(spacing: 30) {
                // Title
                Text("Effective Communication Map")
                    .font(.title)
                    .fontWeight(.bold)
                    .padding(.top)

                // Main content
                ZStack {
                    // Background
                    Color(.systemBackground)
                        .opacity(0.01)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .onTapGesture {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                                isMainTopicExpanded = false
                                expandedSubTopics.removeAll()
                            }
                        }

                    // Mind map content
                    VStack {
                        // Main topic
                        mainTopicView

                        // Subtopics (visible when main topic is expanded)
                        if isMainTopicExpanded {
                            VStack(spacing: 20) {
                                ForEach(subTopics, id: \.self) { topic in
                                    subTopicView(topic)
                                }
                            }
                            .transition(.opacity.combined(with: .scale))
                            .padding(.top, 30)
                        }
                    }
                    .padding()
                }
                .frame(maxWidth: .infinity)

                // Instructions
                if !isMainTopicExpanded {
                    Text("Tap on 'Effective Communication' to explore the mind map")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .padding()
                        .transition(.opacity)
                }

                Spacer(minLength: 50)
            }
            .padding()
        }
        .navigationTitle("Effective Communication")
        .navigationBarTitleDisplayMode(.inline)
        .background(Color(.systemGroupedBackground))
    }

    // MARK: - Subviews

    // Main topic view
    private var mainTopicView: some View {
        HStack {
            Text("Effective Communication")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding(.vertical, 12)
                .padding(.horizontal, 16)

            Spacer()

            Image(systemName: isMainTopicExpanded ? "chevron.left" : "chevron.right")
                .foregroundColor(.white.opacity(0.8))
                .padding(.trailing, 8)
        }
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color(hex: "4A4E69"))
                .shadow(color: Color.black.opacity(0.1), radius: 5, y: 2)
        )
        .onTapGesture {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                isMainTopicExpanded.toggle()
                if !isMainTopicExpanded {
                    expandedSubTopics.removeAll()
                }
            }
        }
    }

    // Subtopic view
    private func subTopicView(_ topic: String) -> some View {
        VStack(alignment: .leading, spacing: 10) {
            // Topic header
            HStack {
                Spacer()

                // Connection line
                if isMainTopicExpanded {
                    connectionLine()
                }

                // Topic button
                HStack {
                    Text(topic)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .padding(.vertical, 10)
                        .padding(.horizontal, 14)

                    Spacer()

                    Image(systemName: expandedSubTopics.contains(topic) ? "chevron.down" : "chevron.right")
                        .foregroundColor(.white.opacity(0.8))
                        .padding(.trailing, 8)
                }
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(hex: "22223B").opacity(0.8))
                        .shadow(color: Color.black.opacity(0.1), radius: 3, y: 1)
                )
                .frame(width: 250)
                .onTapGesture {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        if expandedSubTopics.contains(topic) {
                            expandedSubTopics.remove(topic)
                        } else {
                            expandedSubTopics.insert(topic)
                        }
                    }
                }
            }

            // Topic content (visible when topic is expanded)
            if expandedSubTopics.contains(topic) {
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(topicContent[topic] ?? [], id: \.self) { item in
                        HStack(alignment: .top) {
                            Circle()
                                .fill(Color.blue)
                                .frame(width: 6, height: 6)
                                .padding(.top, 6)

                            Text(item)
                                .font(.subheadline)
                                .foregroundColor(.primary)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                        .padding(.horizontal)
                        .padding(.vertical, 4)
                    }
                }
                .padding(.vertical, 8)
                .padding(.leading, 50)
                .transition(.opacity.combined(with: .move(edge: .top)))
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(.secondarySystemBackground))
                        .padding(.leading, 40)
                )
            }
        }
    }

    // Connection line from main topic to subtopic
    private func connectionLine() -> some View {
        Path { path in
            path.move(to: CGPoint(x: 0, y: 15))
            path.addLine(to: CGPoint(x: 40, y: 15))
        }
        .stroke(Color.gray.opacity(0.5), style: StrokeStyle(lineWidth: 1.5, dash: [2]))
        .frame(width: 40, height: 30)
    }
}

// MARK: - Helper Extensions

// Note: Using Color(hex:) extension defined in ContentView.swift

// MARK: - Preview

#Preview {
    NavigationStack {
        EffectiveCommunicationMapView()
    }
}
