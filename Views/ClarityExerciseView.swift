import SwiftUI
import AVFoundation

struct ClarityExerciseView: View {
    // MARK: - Properties

    /// The title of the exercise
    var exerciseTitle: String

    /// The instructions for the exercise
    var instructions: String

    /// Audio recorder for recording voice
    @StateObject private var audioRecorder = AudioRecorder()

    /// Selected recording for playback
    @State private var selectedRecording: Recording?

    /// Audio engine for playback
    @StateObject private var audioEngine = AudioEngine()

    /// Whether to show the rename alert
    @State private var showingRenameAlert = false

    /// New name for renaming a recording
    @State private var newRecordingName = ""

    /// Index of the recording to rename
    @State private var recordingToRenameIndex = 0

    // MARK: - Body

    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Exercise header
                VStack(spacing: 16) {
                    Text(exerciseTitle)
                        .font(.title)
                        .fontWeight(.bold)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    Text(instructions)
                        .font(.body)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }

                // Recording card
                VStack(spacing: 16) {
                    // Recording visualization
                    ZStack {
                        // Background
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color(.systemGray6))
                            .frame(height: 120)

                        // Recording waveform or icon
                        if audioRecorder.isRecording || audioRecorder.recordingState == .paused {
                            // Animated recording waveform
                            RecordingWaveformView(audioRecorder: audioRecorder)
                                .padding(.horizontal, 20)
                        } else {
                            // Microphone icon
                            Image(systemName: "waveform.circle")
                                .font(.system(size: 60))
                                .foregroundColor(Color(.systemGray))
                        }
                    }
                    .padding(.horizontal)

                    // Recording duration
                    Text(formatTime(audioRecorder.recordingDuration))
                        .font(.title2)
                        .monospacedDigit()

                    // Recording controls
                    HStack(spacing: 30) {
                        // Record/Stop button (hidden when paused)
                        if audioRecorder.recordingState != .paused {
                            Button {
                                if audioRecorder.isRecording {
                                    audioRecorder.stopRecording()
                                } else {
                                    audioRecorder.startRecording()
                                }
                            } label: {
                                ZStack {
                                    Circle()
                                        .fill(audioRecorder.isRecording ? Color.red : Color.blue)
                                        .frame(width: 70, height: 70)
                                        .shadow(radius: 4)

                                    Image(systemName: audioRecorder.isRecording ? "stop.fill" : "mic.fill")
                                        .font(.system(size: 30))
                                        .foregroundColor(.white)
                                }
                            }
                        }

                        // Pause/Resume button (only visible during recording)
                        if audioRecorder.recordingState == .recording || audioRecorder.recordingState == .paused {
                            Button {
                                if audioRecorder.recordingState == .recording {
                                    audioRecorder.pauseRecording()
                                } else {
                                    audioRecorder.resumeRecording()
                                }
                            } label: {
                                ZStack {
                                    Circle()
                                        .fill(Color.orange)
                                        .frame(width: 60, height: 60)
                                        .shadow(radius: 3)

                                    Image(systemName: audioRecorder.recordingState == .recording ? "pause.fill" : "play.fill")
                                        .font(.system(size: 24))
                                        .foregroundColor(.white)
                                }
                            }
                        }
                    }
                    .padding(.vertical, 10)

                    // Recording status
                    Group {
                        switch audioRecorder.recordingState {
                        case .ready:
                            Text("Ready to record")
                                .foregroundColor(.gray)
                        case .recording:
                            Text("Recording...")
                                .foregroundColor(.red)
                        case .paused:
                            Text("Recording paused")
                                .foregroundColor(.orange)
                        case .error(let message):
                            Text("Error: \(message)")
                                .foregroundColor(.red)
                                .multilineTextAlignment(.center)
                                .padding()
                        }
                    }
                    .padding(.bottom, 10)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemBackground))
                        .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
                )
                .padding(.horizontal)

                // Recordings list
                if !audioRecorder.recordings.isEmpty {
                    VStack(alignment: .leading, spacing: 16) {
                        Text("Your Recordings")
                            .font(.headline)
                            .padding(.horizontal)

                        ForEach(audioRecorder.recordings) { recording in
                            VStack(spacing: 0) {
                                // Recording item
                                RecordingItemView(
                                    recording: recording,
                                    isPlaying: Binding(
                                        get: { selectedRecording?.id == recording.id && audioEngine.isPlaying },
                                        set: { _ in }
                                    ),
                                    onPlay: {
                                        playRecording(recording)
                                    },
                                    onRename: {
                                        prepareToRename(recording)
                                    },
                                    onDelete: {
                                        deleteRecording(recording)
                                    }
                                )

                                // Audio player view appears directly below the selected recording
                                if selectedRecording?.id == recording.id {
                                    AudioPlayerView(recording: recording, audioEngine: audioEngine)
                                        .padding(.top, 8)
                                        .transition(.opacity)
                                        .animation(.easeInOut(duration: 0.3), value: selectedRecording?.id)
                                }
                            }
                            .padding(.horizontal)
                            .padding(.bottom, selectedRecording?.id == recording.id ? 16 : 8)
                        }
                    }
                    .padding(.vertical)
                }
            }
            .padding(.vertical)
        }
        .alert("Rename Recording", isPresented: $showingRenameAlert) {
            TextField("New name", text: $newRecordingName)

            Button("Cancel", role: .cancel) {
                showingRenameAlert = false
            }

            Button("Rename") {
                if !newRecordingName.isEmpty {
                    audioRecorder.renameRecording(at: recordingToRenameIndex, to: newRecordingName)
                    showingRenameAlert = false
                    newRecordingName = ""
                }
            }
        } message: {
            Text("Enter a new name for this recording")
        }
        .onAppear {
            // Request microphone permission when the view appears
            requestMicrophonePermission()
        }
    }

    // MARK: - Helper Methods

    /// Format time in seconds to MM:SS format
    private func formatTime(_ timeInSeconds: TimeInterval) -> String {
        let minutes = Int(timeInSeconds) / 60
        let seconds = Int(timeInSeconds) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }

    /// Request permission to use the microphone
    private func requestMicrophonePermission() {
        AVAudioSession.sharedInstance().requestRecordPermission { granted in
            DispatchQueue.main.async {
                if !granted {
                    // Handle permission denied
                    audioRecorder.recordingState = .error("Microphone access denied")
                }
            }
        }
    }

    /// Play a recording
    private func playRecording(_ recording: Recording) {
        // If the same recording is already selected, just toggle playback
        if let selected = selectedRecording, selected.id == recording.id {
            audioEngine.togglePlayback()
            return
        }

        // Otherwise, set the new selected recording
        selectedRecording = recording

        // Use the new method to set the audio player directly
        print("Loading recording from: \(recording.fileURL.path)")
        if audioEngine.setAudioPlayer(with: recording.fileURL) {
            // Start playback after a short delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                self.audioEngine.play()
            }
        } else {
            print("Failed to load recording")
        }
    }

    /// Prepare to rename a recording
    private func prepareToRename(_ recording: Recording) {
        if let index = audioRecorder.recordings.firstIndex(where: { $0.id == recording.id }) {
            recordingToRenameIndex = index
            newRecordingName = recording.fileName
            showingRenameAlert = true
        }
    }

    /// Delete a recording
    private func deleteRecording(_ recording: Recording) {
        if let index = audioRecorder.recordings.firstIndex(where: { $0.id == recording.id }) {
            audioRecorder.deleteRecording(at: IndexSet(integer: index))

            // If the deleted recording was selected, clear the selection
            if selectedRecording?.id == recording.id {
                selectedRecording = nil
            }
        }
    }
}

// MARK: - Recording Item View

struct RecordingItemView: View {
    let recording: Recording
    @Binding var isPlaying: Bool
    let onPlay: () -> Void
    let onRename: () -> Void
    let onDelete: () -> Void

    var body: some View {
        HStack {
            // Play button
            Button(action: onPlay) {
                ZStack {
                    Circle()
                        .fill(isPlaying ? Color.red : Color.blue)
                        .frame(width: 40, height: 40)

                    Image(systemName: isPlaying ? "pause.fill" : "play.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                }
            }
            .padding(.trailing, 8)

            // Recording info
            VStack(alignment: .leading, spacing: 4) {
                Text(recording.fileName)
                    .font(.headline)
                    .lineLimit(1)

                HStack {
                    Text(recording.formattedDuration)
                        .font(.caption)
                        .foregroundColor(.gray)

                    Text("•")
                        .font(.caption)
                        .foregroundColor(.gray)

                    Text(recording.formattedFileSize)
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }

            Spacer()

            // Action buttons
            HStack(spacing: 16) {
                // Rename button
                Button(action: onRename) {
                    Image(systemName: "pencil")
                        .foregroundColor(.blue)
                }

                // Delete button
                Button(action: onDelete) {
                    Image(systemName: "trash")
                        .foregroundColor(.red)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Recording Waveform View

struct RecordingWaveformView: View {
    // Reference to the audio recorder to access power levels
    @ObservedObject var audioRecorder: AudioRecorder

    // Number of bars in the waveform
    private let barCount = 30

    // Animation properties
    @State private var phase: CGFloat = 0

    // Initialize with the audio recorder
    init(audioRecorder: AudioRecorder = AudioRecorder()) {
        self.audioRecorder = audioRecorder
    }

    var body: some View {
        GeometryReader { geometry in
            HStack(spacing: 4) {
                ForEach(0..<barCount, id: \.self) { index in
                    RoundedRectangle(cornerRadius: 3)
                        .fill(barColor(for: index))
                        .frame(width: barWidth(for: geometry.size.width),
                               height: barHeight(for: index, in: geometry))
                        .animation(.easeInOut(duration: 0.1), value: audioRecorder.powerLevels)
                }
            }
            .frame(height: geometry.size.height)
            .frame(maxWidth: .infinity)
        }
        .frame(height: 80)
    }

    // Calculate the width of each bar based on available space
    private func barWidth(for totalWidth: CGFloat) -> CGFloat {
        let availableWidth = totalWidth - (CGFloat(barCount - 1) * 4) // Account for spacing
        return max(2, availableWidth / CGFloat(barCount))
    }

    // Calculate the height of each bar based on audio levels
    private func barHeight(for index: Int, in geometry: GeometryProxy) -> CGFloat {
        // Ensure we have valid dimensions
        guard geometry.size.height > 0 else { return 10 } // Fallback height if geometry is invalid

        let maxHeight = geometry.size.height * 0.8
        let minHeight = max(geometry.size.height * 0.1, 1) // Ensure minimum height is at least 1

        // Default height for safety
        var height: CGFloat = minHeight

        // If we're in the paused state, keep the last known levels
        if audioRecorder.recordingState == .paused {
            // Get the power level for this bar (safely)
            let powerLevel = index < audioRecorder.powerLevels.count ? audioRecorder.powerLevels[index] : 0

            // Ensure power level is within valid range
            let safePowerLevel = max(-1, min(1, powerLevel))

            // Convert to a height value (higher power = taller bar)
            let heightPercentage = (CGFloat(safePowerLevel) + 1) / 2 // Convert from -1...1 to 0...1
            height = minHeight + (maxHeight - minHeight) * heightPercentage
        }
        // For active recording, use the current power levels with some animation
        else if audioRecorder.isRecording {
            // Get the power level for this bar (safely)
            let powerLevel = index < audioRecorder.powerLevels.count ? audioRecorder.powerLevels[index] : 0

            // Ensure power level is within valid range
            let safePowerLevel = max(-1, min(1, powerLevel))

            // Convert to a height value (higher power = taller bar)
            let heightPercentage = (CGFloat(safePowerLevel) + 1) / 2 // Convert from -1...1 to 0...1
            height = minHeight + (maxHeight - minHeight) * heightPercentage
        }
        // When not recording, show a flat line with slight variation
        else {
            height = minHeight + 5 + CGFloat.random(in: 0...3)
        }

        // Final safety check to ensure positive height
        return max(1, height)
    }

    // Determine the color of each bar
    private func barColor(for index: Int) -> Color {
        if audioRecorder.recordingState == .paused {
            // Orange color for paused state
            return Color.orange
        } else if audioRecorder.isRecording {
            // Red color with varying opacity based on power level
            let powerLevel = index < audioRecorder.powerLevels.count ? audioRecorder.powerLevels[index] : 0

            // Ensure power level is within valid range
            let safePowerLevel = max(-1, min(1, powerLevel))

            // Calculate opacity (ensure it's between 0.5 and 1.0)
            let opacityFactor = (CGFloat(safePowerLevel) + 1) / 2 // Convert from -1...1 to 0...1
            let opacity = 0.5 + (opacityFactor * 0.5) // Scale to 0.5-1.0 range

            return Color.red.opacity(opacity)
        } else {
            // Gray for inactive state
            return Color.gray.opacity(0.5)
        }
    }
}

// MARK: - Audio Player View

struct AudioPlayerView: View {
    let recording: Recording
    @ObservedObject var audioEngine: AudioEngine

    var body: some View {
        VStack(spacing: 8) {
            // Player card
            VStack(spacing: 8) {
                // Audio visualization (animated waveform)
                AudioWaveformView(
                    isPlaying: .constant(audioEngine.isPlaying),
                    currentTime: .constant(audioEngine.currentTime),
                    duration: .constant(audioEngine.duration)
                )
                .frame(height: 30)

                // Progress bar
                VStack(spacing: 4) {
                    Slider(
                        value: Binding(
                            get: { audioEngine.progress },
                            set: { newValue in
                                if audioEngine.duration > 0 {
                                    audioEngine.seek(to: newValue * audioEngine.duration)
                                }
                            }
                        ),
                        in: 0...1
                    )
                    .accentColor(.purple)
                    .disabled(audioEngine.loadingState != .loaded)

                    // Time labels
                    HStack {
                        Text(formatTime(audioEngine.currentTime))
                            .font(.caption)
                            .foregroundColor(.gray)

                        Spacer()

                        Text(formatTime(audioEngine.duration))
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }

                // Controls
                HStack(spacing: 20) {
                    // Rewind button
                    Button {
                        audioEngine.seekRelative(by: -5)
                    } label: {
                        Image(systemName: "gobackward.5")
                            .font(.system(size: 16))
                            .foregroundColor(.purple)
                    }
                    .disabled(audioEngine.loadingState != .loaded)

                    // Play/Pause button
                    Button {
                        audioEngine.togglePlayback()
                    } label: {
                        ZStack {
                            Circle()
                                .fill(Color.purple)
                                .frame(width: 44, height: 44)

                            Image(systemName: audioEngine.isPlaying ? "pause.fill" : "play.fill")
                                .font(.system(size: 18))
                                .foregroundColor(.white)
                        }
                    }
                    .disabled(audioEngine.loadingState != .loaded)

                    // Forward button
                    Button {
                        audioEngine.seekRelative(by: 5)
                    } label: {
                        Image(systemName: "goforward.5")
                            .font(.system(size: 16))
                            .foregroundColor(.purple)
                    }
                    .disabled(audioEngine.loadingState != .loaded)
                }
                .padding(.vertical, 4)
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
    }

    /// Format time in seconds to MM:SS format
    private func formatTime(_ timeInSeconds: TimeInterval) -> String {
        let minutes = Int(timeInSeconds) / 60
        let seconds = Int(timeInSeconds) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - Preview

#Preview {
    ClarityExerciseView(
        exerciseTitle: "Voice Clarity Exercise",
        instructions: "Record yourself speaking clearly and concisely. Focus on articulation and pacing. After recording, listen to your voice and identify areas for improvement."
    )
}
