import SwiftUI

struct ChatRewriteView: View {
    @StateObject private var speechManager = SpeechManager()
    @State private var messageText = ""
    @State private var chatMessages: [ChatMessage] = []
    @State private var showTypingIndicator = false
    @State private var ignoreTranscriptionUpdates = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var scrollToLatestMessage = false

    // Sample complex sentences to rewrite
    let complexSentences = [
        "The implementation of the new policy, which was designed by a committee of experts after months of deliberation and consultation with various stakeholders, will commence next month.",
        "Despite the inclement weather conditions that persisted throughout the duration of the outdoor event, the attendees, who had traveled from various locations, remained enthusiastic and engaged.",
        "The acquisition of the multinational corporation, which operates in diverse sectors including technology, healthcare, and finance, by the investment firm was finalized after extensive negotiations.",
        "The professor, whose research has been published in numerous prestigious academic journals and has received recognition from international organizations, delivered a compelling lecture on the subject.",
        "The renovation project, which encompasses structural modifications, electrical upgrades, and aesthetic enhancements, is expected to be completed within the specified timeframe despite unforeseen challenges."
    ]

    @State private var currentSentenceIndex = 0

    var body: some View {
        VStack(spacing: 0) {
            // Header
            VStack(spacing: 8) {
                Text("Sentence Rewriting Practice")
                    .font(.title2)
                    .fontWeight(.bold)

                Text("Rewrite complex sentences in simpler language")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .padding()
            .frame(maxWidth: .infinity)
            .background(Color(.systemBackground))

            // Original sentence to rewrite
            VStack(spacing: 8) {
                Text("Original Sentence:")
                    .font(.headline)
                    .frame(maxWidth: .infinity, alignment: .leading)

                Text(complexSentences[currentSentenceIndex])
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.blue.opacity(0.1))
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                    )
            }
            .padding()

            // Chat messages
            ZStack(alignment: .bottom) {
                // Chat messages with scroll
                ChatMessagesView(messages: chatMessages, onMessageTap: { message in
                    if !message.isUser {
                        speechManager.speak(message.content)
                    }
                }, scrollToLatest: $scrollToLatestMessage)

                // Typing indicator when AI is generating response
                if showTypingIndicator {
                    HStack(spacing: 4) {
                        ForEach(0..<3) { index in
                            Circle()
                                .fill(Color.gray.opacity(0.5))
                                .frame(width: 8, height: 8)
                                .scaleEffect(showTypingIndicator ? 1.0 : 0.5)
                                .animation(
                                    Animation.easeInOut(duration: 0.5)
                                        .repeatForever()
                                        .delay(Double(index) * 0.2),
                                    value: showTypingIndicator
                                )
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color(.systemGray6))
                    )
                    .padding(.leading, 16)
                    .padding(.bottom, 8)
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color(.systemGroupedBackground))

            // Input area
            MessageInputView(
                messageText: $messageText,
                speechManager: speechManager,
                onSend: sendMessage,
                onToggleRecording: toggleRecording,
                onClearText: {
                    messageText = ""
                    speechManager.transcribedText = ""
                }
            )
            .padding(.vertical, 8)
            .background(Color(.systemBackground))
        }
        .onChange(of: speechManager.transcribedText) { _, newValue in
            // Only update messageText if we're not ignoring updates and recording is active
            if !ignoreTranscriptionUpdates && !newValue.isEmpty && speechManager.isRecording {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    messageText = newValue
                }
            }
        }
        .alert(isPresented: $showError) {
            Alert(
                title: Text("Error"),
                message: Text(errorMessage),
                dismissButton: .default(Text("OK"))
            )
        }
        .onAppear {
            // Add initial instruction message
            let initialMessage = ChatMessage(
                id: UUID(),
                content: "Please rewrite the sentence above in simpler language. Focus on clarity and brevity.",
                isUser: false,
                timestamp: Date()
            )
            chatMessages.append(initialMessage)
        }
    }

    private func toggleRecording() {
        if speechManager.isRecording {
            // Set flag to ignore transcription updates during stopping
            ignoreTranscriptionUpdates = true

            // Clear texts
            messageText = ""
            speechManager.transcribedText = ""

            // Stop recording
            speechManager.stopRecording()

            // Schedule a task to reset the flag and ensure message text is cleared
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                // Reset texts again to ensure they're clear
                self.messageText = ""
                self.speechManager.transcribedText = ""

                // Reset flag after texts are cleared
                self.ignoreTranscriptionUpdates = false
            }
        } else {
            // Reset flag when starting recording
            ignoreTranscriptionUpdates = false

            // Clear text with animation
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                messageText = ""
            }

            do {
                try speechManager.startRecording()
            } catch {
                errorMessage = "Could not start recording: \(error.localizedDescription)"
                showError = true
            }
        }
    }

    private func sendMessage() {
        guard !messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }

        // Add user message
        let userMessage = ChatMessage(
            id: UUID(),
            content: messageText,
            isUser: true,
            timestamp: Date()
        )

        withAnimation {
            chatMessages.append(userMessage)
        }

        // Clear input
        messageText = ""

        // Show typing indicator
        withAnimation {
            showTypingIndicator = true
        }

        // Scroll to latest message
        scrollToLatestMessage = true

        // Generate AI response
        Task {
            do {
                // Simulate AI processing time
                try await Task.sleep(nanoseconds: 1_500_000_000)

                // Generate feedback on the rewritten sentence
                let feedback = generateFeedback(for: userMessage.content, original: complexSentences[currentSentenceIndex])

                await MainActor.run {
                    // Hide typing indicator
                    withAnimation {
                        showTypingIndicator = false
                    }

                    // Add AI response
                    let aiMessage = ChatMessage(
                        id: UUID(),
                        content: feedback,
                        isUser: false,
                        timestamp: Date()
                    )

                    withAnimation {
                        chatMessages.append(aiMessage)
                    }

                    // Scroll to latest message
                    scrollToLatestMessage = true

                    // Move to next sentence after a delay
                    DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                        moveToNextSentence()
                    }
                }
            } catch {
                await MainActor.run {
                    // Hide typing indicator
                    withAnimation {
                        showTypingIndicator = false
                    }

                    // Show error
                    errorMessage = "Error generating response: \(error.localizedDescription)"
                    showError = true
                }
            }
        }
    }

    private func generateFeedback(for rewritten: String, original: String) -> String {
        // Calculate word count reduction
        let originalWords = original.split(separator: " ").count
        let rewrittenWords = rewritten.split(separator: " ").count
        let reduction = originalWords - rewrittenWords
        let percentReduction = (Double(reduction) / Double(originalWords)) * 100

        // Simple feedback based on word count reduction
        if percentReduction >= 30 {
            return "Excellent job! You reduced the word count by about \(Int(percentReduction))% while maintaining the core meaning. Your sentence is much clearer and more direct.\n\nLet's try another one!"
        } else if percentReduction >= 15 {
            return "Good work! You simplified the sentence and reduced the word count by about \(Int(percentReduction))%. Consider if there are any other phrases that could be made more concise.\n\nLet's try another one!"
        } else if percentReduction > 0 {
            return "You've made some progress in simplifying the sentence, but there's room for improvement. Try to identify unnecessary phrases and replace complex terms with simpler alternatives.\n\nLet's try another one!"
        } else {
            return "Your rewritten sentence is about the same length or longer than the original. Remember, the goal is to make the sentence simpler and more concise while preserving the core meaning.\n\nLet's try another one!"
        }
    }

    private func moveToNextSentence() {
        withAnimation {
            // Move to next sentence or loop back to beginning
            currentSentenceIndex = (currentSentenceIndex + 1) % complexSentences.count

            // Clear chat messages except for the initial instruction
            if chatMessages.count > 0 {
                let initialMessage = chatMessages[0]
                chatMessages = [initialMessage]
            }
        }
    }
}

#Preview {
    ChatRewriteView()
}
