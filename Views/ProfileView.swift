import SwiftUI
import FirebaseAuth

struct ProfileView: View {
    @StateObject private var authService = AuthService.shared
    @State private var showingSignOutAlert = false
    @State private var showingError = false
    @State private var errorMessage = ""

    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("User Information")) {
                    VStack(alignment: .leading, spacing: 4) {
                        if let user = authService.user {
                            if let userProfile = authService.userProfile {
                                Text(userProfile.fullName)
                                    .font(.headline)
                            } else {
                                Text(user.email ?? "No email")
                                    .font(.headline)
                            }

                            Text(user.email ?? "No email")
                                .font(.subheadline)
                                .foregroundColor(.secondary)

                            Text("Account ID: \(user.uid.prefix(8))...")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            if let creationDate = user.metadata.creationDate {
                                Text("Member since: \(formattedDate(creationDate))")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        } else {
                            Text("Not signed in")
                                .font(.headline)
                        }
                    }
                    .padding(.vertical, 8)
                }

                Section {
                    Button {
                        showingSignOutAlert = true
                    } label: {
                        HStack {
                            Text("Sign Out")
                                .foregroundColor(.red)
                            Spacer()
                            Image(systemName: "rectangle.portrait.and.arrow.right")
                                .foregroundColor(.red)
                        }
                    }
                }
            }
            .navigationTitle("Profile")
            .alert("Sign Out", isPresented: $showingSignOutAlert) {
                Button("Cancel", role: .cancel) { }
                Button("Sign Out", role: .destructive) {
                    signOut()
                }
            } message: {
                Text("Are you sure you want to sign out?")
            }
            .alert("Error", isPresented: $showingError) {
                Button("OK", role: .cancel) { }
            } message: {
                Text(errorMessage)
            }
        }
    }

    private func signOut() {
        do {
            try authService.signOut()
        } catch {
            errorMessage = "Failed to sign out: \(error.localizedDescription)"
            showingError = true
        }
    }

    private func formattedDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }
}

#Preview {
    ProfileView()
}