import SwiftUI
import RevenueCat

struct PaywallView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var subscriptionService = SubscriptionService.shared
    @State private var selectedPackage: Package?
    @State private var isPurchasing = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var showSuccess = false
    @State private var showSubscriptionInfo = false

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // Premium Features Header
                    VStack(spacing: 16) {
                        Image(systemName: "star.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.yellow)
                            .padding()
                            .background(
                                Circle()
                                    .fill(Color.yellow.opacity(0.2))
                                    .frame(width: 120, height: 120)
                            )

                        Text("Unlock Talk Maxer Premium")
                            .font(.title)
                            .fontWeight(.bold)
                            .multilineTextAlignment(.center)

                        Text("Access all lessons and enhance your communication skills")
                            .font(.headline)
                            .foregroundStyle(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)

                        // Add collapsible subscription info button
                        Button {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showSubscriptionInfo.toggle()
                            }
                        } label: {
                            HStack {
                                Text("Subscription Information")
                                    .font(.headline)
                                    .foregroundColor(.primary)

                                Spacer()

                                Image(systemName: showSubscriptionInfo ? "chevron.up" : "chevron.down")
                                    .foregroundColor(.blue)
                                    .animation(.easeInOut, value: showSubscriptionInfo)
                            }
                            .padding()
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(12)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .padding(.horizontal)

                        // Subscription information details (collapsible)
                        if showSubscriptionInfo {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("• Monthly and annual subscription options available")
                                    .font(.subheadline)

                                Text("• Full access to all premium lessons during subscription period")
                                    .font(.subheadline)

                                Text("• Auto-renews until canceled")
                                    .font(.subheadline)

                                Text("• Cancel anytime through App Store settings")
                                    .font(.subheadline)
                            }
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding()
                            .background(Color.blue.opacity(0.05))
                            .cornerRadius(12)
                            .padding(.horizontal)
                            .transition(.move(edge: .top).combined(with: .opacity))
                        }
                    }
                    .padding(.top, 32)

                    // Features List
                    VStack(alignment: .leading, spacing: 16) {
                        FeatureRow(icon: "checkmark.circle.fill", text: "Access to all 25+ conversation lessons")
                        FeatureRow(icon: "checkmark.circle.fill", text: "RIZZ!!! Become good at Romance!")
                        FeatureRow(icon: "checkmark.circle.fill", text: "Charming!!! Get pro at Charisma")
                        FeatureRow(icon: "checkmark.circle.fill", text: "Practice everyday situations")
                        FeatureRow(icon: "checkmark.circle.fill", text: "Improve social and professional communication")
                        FeatureRow(icon: "checkmark.circle.fill", text: "Handle emergency and difficult conversations")
                    }
                    .padding()
                    .background(Color(.systemGray6).opacity(0.7))
                    .cornerRadius(16)
                    .padding(.horizontal)

                    // Subscription Options
                    if subscriptionService.isLoadingProducts {
                        ProgressView()
                            .padding()
                    } else if subscriptionService.packages.isEmpty {
                        Text("No subscription options available")
                            .foregroundStyle(.secondary)
                            .padding()
                    } else {
                        VStack(spacing: 16) {
                            // Horizontal card layout
                            HStack(spacing: 12) {
                                ForEach(subscriptionService.packages.sorted(by: { getMonthsFromPackage($0) < getMonthsFromPackage($1) }), id: \.identifier) { package in
                                    SubscriptionCard(
                                        package: package,
                                        isSelected: selectedPackage?.identifier == package.identifier,
                                        isRecommended: getMonthsFromPackage(package) == 6,
                                        discountPercentage: getDiscountPercentage(for: package)
                                    )
                                    .onTapGesture {
                                        selectedPackage = package
                                    }
                                }
                            }
                            .padding(.horizontal)

                            // Selected package subtitle
                            if let selectedPackage = selectedPackage {
                                Text("Full access for just \(selectedPackage.storeProduct.localizedPriceString)")
                                    .font(.title3)
                                    .fontWeight(.medium)
                                    .foregroundColor(.primary)
                            }
                        }
                    }

                    // Purchase Button
                    Button {
                        purchaseSelected()
                    } label: {
                        Text("Subscribe Now")
                            .fontWeight(.semibold)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.blue)
                            )
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal)
                    .disabled(selectedPackage == nil || isPurchasing)
                    .opacity(selectedPackage == nil || isPurchasing ? 0.6 : 1)
                    .overlay {
                        if isPurchasing {
                            ProgressView()
                                .tint(.white)
                        }
                    }

                    // Restore Purchases
                    Button {
                        restorePurchases()
                    } label: {
                        Text("Restore")
                            .foregroundStyle(.secondary)
                    }
                    .padding(.top, 8)
                    .disabled(isPurchasing)

                    // Privacy and Terms
                    Text("Payment will be charged to your Apple ID account at the confirmation of purchase. Subscription automatically renews unless it is canceled at least 24 hours before the end of the current period. Your account will be charged for renewal within 24 hours prior to the end of the current period. Manage and cancel your subscriptions by going to your account settings on the App Store.")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 24)
                        .padding(.top, 24)
                        .padding(.bottom, 16)
                }
                .padding(.bottom, 32)
            }
            .navigationBarTitleDisplayMode(.inline)
            .navigationTitle("Premium Access")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Close") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                // Force refresh subscription status when paywall appears
                subscriptionService.forceCheckSubscriptionStatus { isSubscribed in
                    // If user is already subscribed, dismiss the paywall
                    if isSubscribed {
                        print("User is subscribed - dismissing paywall")
                        DispatchQueue.main.async {
                            dismiss()
                        }
                    } else {
                        // Only fetch offerings if not subscribed
                        subscriptionService.fetchOfferings()
                    }
                }
            }
            .onChange(of: subscriptionService.packages) { _, packages in
                // Select 6-month package by default when packages are loaded
                if selectedPackage == nil && !packages.isEmpty {
                    // Find the 6-month package (recommended)
                    let sixMonthPackage = packages.first { package in
                        getMonthsFromPackage(package) == 6
                    }

                    // Set it as selected, or use the first package if no 6-month package is found
                    selectedPackage = sixMonthPackage ?? packages.first
                }
            }
            .onChange(of: subscriptionService.isSubscribed) { _, isNowSubscribed in
                // Dismiss if subscription status changes to subscribed
                if isNowSubscribed {
                    print("Subscription status changed to active - dismissing paywall")
                    DispatchQueue.main.async {
                        dismiss()
                    }
                }
            }
            .alert("Error", isPresented: $showError) {
                Button("OK", role: .cancel) { }
            } message: {
                Text(errorMessage)
            }
            .alert("Success", isPresented: $showSuccess) {
                Button("Great!") {
                    dismiss()
                }
            } message: {
                Text("Your subscription is now active! Enjoy access to all lessons.")
            }
        }
    }

    private func getMonthsFromPackage(_ package: Package) -> Int {
        guard let subscriptionPeriod = package.storeProduct.subscriptionPeriod else {
            return 1
        }

        switch subscriptionPeriod.unit {
        case .month:
            return subscriptionPeriod.value
        case .year:
            return subscriptionPeriod.value * 12
        default:
            return 1
        }
    }

    private func getDiscountPercentage(for package: Package) -> Int {
        let months = getMonthsFromPackage(package)
        switch months {
        case 6:
            return 16
        case 12:
            return 33
        default:
            return 0
        }
    }

    private func purchaseSelected() {
        guard let package = selectedPackage else { return }

        isPurchasing = true

        subscriptionService.purchase(package: package) { success, error in
            isPurchasing = false

            if success {
                // Force refresh subscription status
                subscriptionService.forceCheckSubscriptionStatus()
                showSuccess = true
            } else if let error = error {
                errorMessage = error.localizedDescription
                showError = true
            }
        }
    }

    private func restorePurchases() {
        isPurchasing = true

        subscriptionService.restorePurchases { success in
            isPurchasing = false

            if success {
                // Force refresh subscription status
                subscriptionService.forceCheckSubscriptionStatus()
                showSuccess = true
            } else {
                errorMessage = "No purchases found to restore. Make sure you're using the same Apple ID that was used for the original purchase."
                showError = true
            }
        }
    }
}

struct FeatureRow: View {
    let icon: String
    let text: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.green)

            Text(text)
                .font(.subheadline)

            Spacer()
        }
    }
}

struct PackageRow: View {
    let package: Package
    let isSelected: Bool

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(package.storeProduct.localizedTitle)
                    .font(.headline)

                // Display subscription length and benefits
                VStack(alignment: .leading, spacing: 2) {
                    Text(getSubscriptionPeriodText(for: package))
                        .font(.subheadline)
                        .foregroundColor(.blue)

                    Text(package.storeProduct.localizedDescription)
                        .font(.subheadline)
                        .foregroundStyle(.secondary)

                    // Add what's included in subscription
                    Text("Includes all premium features and lessons")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }

            Spacer()

            Text(package.storeProduct.localizedPriceString)
                .fontWeight(.semibold)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isSelected ? Color.blue.opacity(0.1) : Color(.systemGray6))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .strokeBorder(isSelected ? Color.blue : Color.clear, lineWidth: 2)
        )
    }

    private func getSubscriptionPeriodText(for package: Package) -> String {
        guard let subscriptionPeriod = package.storeProduct.subscriptionPeriod else {
            return "Subscription"
        }

        switch subscriptionPeriod.unit {
        case .day:
            return subscriptionPeriod.value == 1 ? "Daily subscription" : "\(subscriptionPeriod.value)-day subscription"
        case .week:
            return subscriptionPeriod.value == 1 ? "Weekly subscription" : "\(subscriptionPeriod.value)-week subscription"
        case .month:
            return subscriptionPeriod.value == 1 ? "Monthly subscription" : "\(subscriptionPeriod.value)-month subscription"
        case .year:
            return subscriptionPeriod.value == 1 ? "Annual subscription" : "\(subscriptionPeriod.value)-year subscription"
        @unknown default:
            return "Subscription"
        }
    }
}

struct SubscriptionCard: View {
    let package: Package
    let isSelected: Bool
    let isRecommended: Bool
    let discountPercentage: Int

    var body: some View {
        VStack(spacing: 6) {
            // Discount badge
            if discountPercentage > 0 {
                Text("\(discountPercentage)% OFF")
                    .font(.caption2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 10)
                    .padding(.vertical, 3)
                    .background(
                        Capsule()
                            .fill(
                                LinearGradient(
                                    colors: isRecommended ? [Color.green, Color.green.opacity(0.8)] : [Color.orange, Color.red],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .shadow(color: .black.opacity(0.2), radius: 2, x: 0, y: 1)
                    )
            } else {
                Spacer().frame(height: 18)
            }

            // Months number
            Text("\(getMonthsFromPackage(package))")
                .font(.system(size: 28, weight: .bold, design: .rounded))
                .foregroundColor(isSelected ? .blue : (isRecommended ? .green : .primary))

            // Month/months text
            Text(getMonthsFromPackage(package) == 1 ? "month" : "months")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .blue : (isRecommended ? .green : .secondary))

            // Price
            Text(package.storeProduct.localizedPriceString)
                .font(.callout)
                .fontWeight(.bold)
                .foregroundColor(isSelected ? .blue : (isRecommended ? .green : .primary))

            // Checkmark for recommended or selected
            if isRecommended || isSelected {
                Image(systemName: isSelected ? "checkmark.circle.fill" : "star.circle.fill")
                    .font(.title3)
                    .foregroundColor(isSelected ? .blue : .green)
                    .symbolEffect(.bounce, value: isSelected)
            } else {
                Spacer().frame(height: 22)
            }
        }
        .frame(width: 80, height: 135)
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: isSelected ? [Color.blue.opacity(0.15), Color.blue.opacity(0.05)] :
                                (isRecommended ? [Color.green.opacity(0.1), Color.green.opacity(0.05)] :
                                [Color(.systemBackground), Color(.systemGray6).opacity(0.3)]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .strokeBorder(
                            LinearGradient(
                                colors: isSelected ? [Color.blue, Color.blue.opacity(0.6)] :
                                        (isRecommended ? [Color.green, Color.green.opacity(0.6)] :
                                        [Color.gray.opacity(0.3), Color.gray.opacity(0.1)]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: isSelected ? 2.5 : (isRecommended ? 2 : 1)
                        )
                )
                .shadow(
                    color: isSelected ? Color.blue.opacity(0.3) :
                           (isRecommended ? Color.green.opacity(0.2) : Color.black.opacity(0.1)),
                    radius: isSelected ? 8 : (isRecommended ? 6 : 4),
                    x: 0,
                    y: isSelected ? 4 : 2
                )
        )
        .scaleEffect(isSelected ? 1.08 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }

    private func getMonthsFromPackage(_ package: Package) -> Int {
        guard let subscriptionPeriod = package.storeProduct.subscriptionPeriod else {
            return 1
        }

        switch subscriptionPeriod.unit {
        case .month:
            return subscriptionPeriod.value
        case .year:
            return subscriptionPeriod.value * 12
        default:
            return 1
        }
    }
}

#Preview {
    PaywallView()
}
