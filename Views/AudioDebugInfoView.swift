import SwiftUI
import AVFoundation
import UIKit

struct AudioDebugInfoView: View {
    @State private var bundleInfo: String = "Loading..."
    @State private var audioFiles: [String] = []
    @State private var assetInfo: String = "Loading..."

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                Text("Audio Debug Information")
                    .font(.title)
                    .fontWeight(.bold)
                    .frame(maxWidth: .infinity, alignment: .center)

                // Bundle Info Section
                VStack(alignment: .leading, spacing: 10) {
                    Text("Bundle Information")
                        .font(.headline)

                    Text(bundleInfo)
                        .font(.system(.body, design: .monospaced))
                        .lineLimit(nil)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(10)

                // Audio Files Section
                VStack(alignment: .leading, spacing: 10) {
                    Text("Audio Files in Bundle")
                        .font(.headline)

                    if audioFiles.isEmpty {
                        Text("No audio files found in bundle")
                            .foregroundColor(.red)
                    } else {
                        Text("Found \(audioFiles.count) audio files:")
                            .fontWeight(.medium)

                        ForEach(audioFiles, id: \.self) { file in
                            Text("• \(file)")
                                .font(.system(.body, design: .monospaced))
                                .lineLimit(nil)
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(10)

                // Asset Info Section
                VStack(alignment: .leading, spacing: 10) {
                    Text("Asset Catalog Information")
                        .font(.headline)

                    Text(assetInfo)
                        .font(.system(.body, design: .monospaced))
                        .lineLimit(nil)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(10)

                // Audio Session Info
                VStack(alignment: .leading, spacing: 10) {
                    Text("Audio Session Information")
                        .font(.headline)

                    let session = AVAudioSession.sharedInstance()
                    Group {
                        Text("Category: \(session.category.rawValue)")
                        Text("Mode: \(session.mode.rawValue)")
                        Text("Sample Rate: \(session.sampleRate) Hz")
                        Text("IO Buffer Duration: \(session.ioBufferDuration) sec")
                        Text("Input Available: \(session.isInputAvailable ? "Yes" : "No")")
                        Text("Output Volume: \(session.outputVolume)")
                    }
                    .font(.system(.body, design: .monospaced))
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(10)

                // Refresh Button
                Button(action: loadDebugInfo) {
                    Text("Refresh Debug Info")
                        .fontWeight(.semibold)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
            }
            .padding()
        }
        .onAppear {
            loadDebugInfo()
        }
    }

    private func loadDebugInfo() {
        // Load bundle info
        var bundleInfoText = ""
        bundleInfoText += "Bundle ID: \(Bundle.main.bundleIdentifier ?? "unknown")\n"
        bundleInfoText += "Bundle Path: \(Bundle.main.bundlePath)\n"
        bundleInfoText += "Resource Path: \(Bundle.main.resourcePath ?? "unknown")\n\n"

        // Get bundle contents
        if let resourcePath = Bundle.main.resourcePath,
           let contents = try? FileManager.default.contentsOfDirectory(atPath: resourcePath) {
            bundleInfoText += "Root Contents (\(contents.count) items):\n"
            for item in contents.prefix(10) {
                bundleInfoText += "• \(item)\n"
            }
            if contents.count > 10 {
                bundleInfoText += "... and \(contents.count - 10) more items\n"
            }
        }

        self.bundleInfo = bundleInfoText

        // Find audio files
        var foundAudioFiles: [String] = []
        if let resourcePath = Bundle.main.resourcePath {
            let enumerator = FileManager.default.enumerator(atPath: resourcePath)
            while let file = enumerator?.nextObject() as? String {
                let ext = (file as NSString).pathExtension.lowercased()
                if ["wav", "mp3", "m4a", "aac"].contains(ext) {
                    foundAudioFiles.append(file)
                }
            }
        }

        self.audioFiles = foundAudioFiles

        // Check asset catalog
        var assetInfoText = ""
        let assetNames = [
            "Audios",
            "Fundamentals of Effective Communication",
            "The Art and Practice of Storytelling",
            "Understanding and Reducing Filler Words",
            "Key Story Elements and Structure"
        ]

        var foundAssets = false
        for name in assetNames {
            if let asset = NSDataAsset(name: name) {
                assetInfoText += "Found asset: \(name)\n"
                assetInfoText += "• Size: \(asset.data.count) bytes\n"
                assetInfoText += "• Type: \(asset.typeIdentifier ?? "unknown")\n\n"
                foundAssets = true
            }
        }

        if !foundAssets {
            assetInfoText = "No audio assets found in asset catalog"
        }

        self.assetInfo = assetInfoText
    }
}

#Preview {
    AudioDebugInfoView()
}
