import SwiftUI
import Speech

struct ArticleRephraseWithRecordingView: View {
    // View models
    @StateObject private var geminiViewModel = GeminiViewModel()
    @StateObject private var speechManager = SpeechManager()

    // UI State
    @State private var article = ""
    @State private var isLoading = true
    @State private var showArticle = true
    @State private var showRecording = false
    @State private var showFeedback = false
    @State private var feedbackMessage = ""
    @State private var paraphraseScore = 0
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var selectedDifficulty: DifficultyLevel = .moderate
    @State private var transcribedText = ""
    @State private var loadingDifficulty: DifficultyLevel? = nil

    // Environment
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    // Difficulty levels
    enum DifficultyLevel: String, CaseIterable, Identifiable {
        case easy = "Easy"
        case moderate = "Moderate"
        case complex = "Complex"

        var id: String { self.rawValue }

        var description: String {
            switch self {
            case .easy:
                return "Simple vocabulary and short paragraphs"
            case .moderate:
                return "Balanced complexity and length"
            case .complex:
                return "Advanced vocabulary and longer content"
            }
        }
    }

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Text("Article Rephrasing Practice")
                            .font(.title2)
                            .fontWeight(.bold)

                        Text("Read the article and rephrase it in your own words")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color(.systemBackground))

                    if !showArticle && !showRecording && !showFeedback {
                        // Difficulty selection
                        difficultySelectionView
                    } else if isLoading {
                        // Loading indicator
                        VStack(spacing: 20) {
                            ProgressView()
                                .scaleEffect(1.5)

                            Text(showFeedback ? "Analyzing your response..." : "Generating article...")
                                .foregroundColor(.secondary)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 100)
                    } else {
                        // Main content
                        VStack(spacing: 24) {
                            // Article section
                            if showArticle {
                                articleSection
                            }

                            // Recording section
                            if showRecording {
                                recordingSection
                            }

                            // Action buttons
                            actionButtons

                            // Feedback section
                            if showFeedback {
                                feedbackSection
                            }
                        }
                        .padding(.horizontal)
                    }

                    Spacer(minLength: 40)
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("New Article") {
                        resetAndShowDifficultySelection()
                    }
                }
            }
            .alert("Error", isPresented: $showError) {
                Button("OK", role: .cancel) { }
            } message: {
                Text(errorMessage)
            }
            .onAppear {
                if article.isEmpty {
                    // Start with difficulty selection instead of auto-generating
                    showArticle = false
                    showRecording = false
                    showFeedback = false
                    isLoading = false
                }
            }
            .onChange(of: speechManager.transcribedText) { _, newValue in
                transcribedText = newValue
            }
        }
    }

    // MARK: - View Components

    private var difficultySelectionView: some View {
        VStack(spacing: 24) {
            Text("Select Difficulty Level")
                .font(.headline)
                .foregroundColor(.primary)

            ForEach(DifficultyLevel.allCases) { level in
                Button {
                    selectedDifficulty = level
                    loadingDifficulty = level
                    requestNewArticle(difficulty: level)
                } label: {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(level.rawValue)
                                .font(.headline)
                                .foregroundColor(.white)

                            Text(level.description)
                                .font(.subheadline)
                                .foregroundColor(.white.opacity(0.8))
                        }

                        Spacer()

                        if loadingDifficulty == level {
                            // Show progress indicator when this difficulty is loading
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(1.0)
                        } else {
                            Image(systemName: "chevron.right")
                                .foregroundColor(.white.opacity(0.8))
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(
                                LinearGradient(
                                    colors: difficultyGradient(for: level),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                    )
                }
                .disabled(loadingDifficulty != nil) // Disable all buttons while loading
            }
        }
        .padding(.horizontal)
        .padding(.top, 20)
    }

    private func difficultyGradient(for level: DifficultyLevel) -> [Color] {
        switch level {
        case .easy:
            return [.green, .green.opacity(0.7)]
        case .moderate:
            return [.blue, .purple.opacity(0.8)]
        case .complex:
            return [.purple, .red.opacity(0.8)]
        }
    }

    private var articleSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Article:")
                    .font(.headline)
                    .foregroundColor(.primary)

                Spacer()

                Text(selectedDifficulty.rawValue)
                    .font(.subheadline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(
                                LinearGradient(
                                    colors: difficultyGradient(for: selectedDifficulty),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                    )
            }

            Text(article)
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.secondarySystemBackground))
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                )
        }
    }

    private var recordingSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Record Your Rephrasing:")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(spacing: 20) {
                // Recording button
                Button {
                    toggleRecording()
                } label: {
                    VStack(spacing: 12) {
                        Image(systemName: speechManager.isRecording ? "stop.circle.fill" : "mic.circle.fill")
                            .font(.system(size: 64))
                            .foregroundColor(speechManager.isRecording ? .red : .blue)

                        Text(speechManager.isRecording ? "Tap to Stop" : "Tap to Record")
                            .font(.headline)
                            .foregroundColor(speechManager.isRecording ? .red : .blue)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 30)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.secondarySystemBackground))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(
                                        speechManager.isRecording ? Color.red.opacity(0.5) : Color.blue.opacity(0.5),
                                        lineWidth: 2
                                    )
                            )
                    )
                }

                // Transcription display
                if !transcribedText.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Your Rephrasing:")
                            .font(.headline)
                            .foregroundColor(.secondary)

                        Text(transcribedText)
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color(.tertiarySystemBackground))
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                            )
                    }
                }
            }
        }
    }

    private var actionButtons: some View {
        Group {
            if showArticle {
                Button {
                    withAnimation {
                        showArticle = false
                        showRecording = true
                    }
                } label: {
                    Text("Start Rephrasing")
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(
                                    LinearGradient(
                                        colors: [.blue, .purple.opacity(0.8)],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                        )
                }
            } else if showRecording {
                if !transcribedText.isEmpty && !speechManager.isRecording {
                    Button {
                        submitRephrasing()
                    } label: {
                        Text("Submit Rephrasing")
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(
                                        LinearGradient(
                                            colors: [.blue, .purple.opacity(0.8)],
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        )
                                    )
                            )
                    }
                }
            } else if showFeedback {
                Button {
                    resetAndShowDifficultySelection()
                } label: {
                    Text("Try Another Article")
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(
                                    LinearGradient(
                                        colors: [.blue, .purple.opacity(0.8)],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                        )
                }
            }
        }
    }

    private var feedbackSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Feedback:")
                .font(.headline)
                .foregroundColor(.primary)

            VStack(spacing: 16) {
                // Score display
                HStack {
                    Text("Score:")
                        .font(.headline)

                    Text("\(paraphraseScore)/10")
                        .font(.headline)
                        .foregroundColor(scoreColor)

                    Spacer()
                }

                // Score bar
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        // Background bar
                        Rectangle()
                            .frame(width: geometry.size.width, height: 8)
                            .opacity(0.3)
                            .foregroundColor(Color.gray)
                            .cornerRadius(4)

                        // Score bar
                        Rectangle()
                            .frame(width: geometry.size.width * CGFloat(paraphraseScore) / 10, height: 8)
                            .foregroundColor(scoreColor)
                            .cornerRadius(4)
                    }
                }
                .frame(height: 8)

                // Transcription display
                VStack(alignment: .leading, spacing: 8) {
                    Text("Your Rephrasing:")
                        .font(.headline)
                        .foregroundColor(.secondary)

                    Text(transcribedText)
                        .padding()
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.tertiarySystemBackground))
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                        )
                }

                // Feedback text
                Text(feedbackMessage)
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.secondarySystemBackground))
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(scoreColor.opacity(0.5), lineWidth: 1)
                    )
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 5, y: 2)
            )
        }
    }

    // MARK: - Helper Properties

    private var scoreColor: Color {
        if paraphraseScore >= 8 {
            return .green
        } else if paraphraseScore >= 5 {
            return .orange
        } else {
            return .red
        }
    }

    // MARK: - Functions

    private func toggleRecording() {
        if speechManager.isRecording {
            speechManager.stopRecording()
        } else {
            transcribedText = ""
            do {
                try speechManager.startRecording()
            } catch {
                print("Error starting recording: \(error.localizedDescription)")
                errorMessage = "Error starting recording: \(error.localizedDescription)"
                showError = true
            }
        }
    }

    private func requestNewArticle(difficulty: DifficultyLevel) {
        isLoading = true

        // Request a new article from AI
        Task {
            do {
                // Base prompt for generating an article based on difficulty
                let prompt = """
                Generate a short, interesting article about a general knowledge topic (science, history, culture, etc.).

                The article should be at a \(difficulty.rawValue.lowercased()) difficulty level:
                \(difficultyPrompt(for: difficulty))

                The article should:
                1. Be factually accurate
                2. Include some interesting facts or statistics
                3. Be suitable for a paraphrasing exercise

                Provide ONLY the article text with no additional explanations or notes.
                """

                // Create a dummy lesson plan for the API call
                let dummyLesson = LessonPlan(
                    title: "Article Paraphrasing Practice - \(difficulty.rawValue)",
                    description: "Practice paraphrasing articles",
                    prompt: prompt,
                    rules: "Generate only the article with no additional text."
                )

                // Get a new article from AI
                print("Requesting new article at \(difficulty.rawValue) level...")
                let generatedArticle = try await geminiViewModel.generateResponse(for: prompt, lesson: dummyLesson)

                await MainActor.run {
                    article = generatedArticle.trimmingCharacters(in: .whitespacesAndNewlines)
                    isLoading = false
                    showArticle = true
                    showRecording = false
                    showFeedback = false
                    transcribedText = ""
                    loadingDifficulty = nil // Reset loading state
                }
            } catch {
                print("Error generating article: \(error.localizedDescription)")

                await MainActor.run {
                    // Use fallback article
                    article = getFallbackArticle(for: difficulty)

                    isLoading = false
                    showArticle = true
                    showRecording = false
                    showFeedback = false
                    transcribedText = ""
                    loadingDifficulty = nil // Reset loading state

                    // Show error
                    errorMessage = "Error generating article: \(error.localizedDescription)"
                    showError = true
                }
            }
        }
    }

    private func difficultyPrompt(for difficulty: DifficultyLevel) -> String {
        switch difficulty {
        case .easy:
            return """
            - 2-3 short paragraphs
            - Simple vocabulary and sentence structure
            - Written at an elementary school reading level (grades 3-5)
            - Clear and straightforward concepts
            """
        case .moderate:
            return """
            - 3-4 paragraphs of moderate length
            - Balanced vocabulary with some specialized terms
            - Written at a high school reading level
            - Moderately complex concepts
            """
        case .complex:
            return """
            - 4-5 paragraphs with detailed content
            - Advanced vocabulary including specialized terminology
            - Written at a college reading level
            - Complex concepts and nuanced arguments
            """
        }
    }

    private func getFallbackArticle(for difficulty: DifficultyLevel) -> String {
        switch difficulty {
        case .easy:
            return """
            The Moon is Earth's only natural satellite. It is about one-quarter the size of Earth. The Moon orbits around our planet at an average distance of 238,855 miles. It has no air or water on its surface.

            The Moon affects life on Earth in many ways. It causes ocean tides and helps stabilize Earth's rotation. Humans first walked on the Moon in 1969 during the Apollo 11 mission. Astronaut Neil Armstrong was the first person to step on the lunar surface.
            """
        case .moderate:
            return """
            Climate change continues to be one of the most pressing issues of our time. Scientists around the world have documented rising global temperatures, with the last decade being the warmest on record. According to NASA, the Earth's average surface temperature has risen about 1.18 degrees Celsius since the late 19th century, largely driven by increased carbon dioxide emissions and other human activities.

            The effects of climate change are far-reaching and increasingly visible. Glaciers are retreating, sea levels are rising, and extreme weather events like hurricanes, floods, and wildfires are becoming more frequent and intense. The Arctic is warming at almost twice the global average rate, resulting in dramatic loss of sea ice. These changes not only affect ecosystems but also human communities, particularly those in vulnerable coastal regions.

            While the challenges are significant, there are reasons for hope. Renewable energy sources like solar and wind power have become increasingly affordable and widespread. Many countries, cities, and businesses have committed to reducing their carbon footprints and transitioning to cleaner energy sources. Individual actions, from reducing meat consumption to using public transportation, can also make a difference when adopted collectively. Addressing climate change requires global cooperation, innovation, and a commitment to creating a sustainable future for all.
            """
        case .complex:
            return """
            The emergence of artificial general intelligence (AGI) represents a paradigm shift in our understanding of cognition and computation. Unlike narrow AI systems designed for specific tasks, AGI aims to replicate the comprehensive intellectual capabilities of human beings, including abstract reasoning, creative problem-solving, and adaptive learning across domains. This technological frontier raises profound questions about consciousness, autonomy, and the fundamental nature of intelligence itself.

            Contemporary discourse surrounding AGI development encompasses multifaceted ethical considerations. The alignment problem—ensuring AGI systems adopt values congruent with human welfare—remains particularly vexing. Researchers must navigate the complex terrain of value pluralism while mitigating existential risks associated with superintelligent systems potentially operating beyond human comprehension or control. The technical challenges are equally formidable, requiring breakthroughs in unsupervised learning, causal reasoning, and computational architectures that can integrate symbolic and subsymbolic processing.

            The socioeconomic implications of AGI deployment warrant careful scrutiny. While optimistic projections envision unprecedented prosperity through automation of cognitive labor, pessimistic scenarios highlight potential labor market disruptions and wealth concentration. The distribution of benefits derived from AGI technologies will likely depend on governance frameworks established during their development phase. These considerations necessitate interdisciplinary collaboration among computer scientists, philosophers, economists, and policymakers to ensure AGI serves as a tool for collective human flourishing rather than exacerbating existing inequalities.

            Historical analysis reveals that transformative technologies often produce consequences unforeseen by their creators. The advent of AGI may similarly yield second and third-order effects beyond our current predictive capabilities. This uncertainty underscores the importance of developing robust safety measures, transparent research practices, and adaptive regulatory approaches. As we stand at this technological inflection point, our decisions regarding AGI development will profoundly shape the trajectory of human civilization in the coming decades.
            """
        }
    }

    private func submitRephrasing() {
        // Trim whitespace and check if text is empty
        let trimmedText = transcribedText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedText.isEmpty else { return }

        // Show loading indicator
        isLoading = true
        showRecording = false
        showFeedback = true

        // Generate AI feedback on the paraphrase
        Task {
            do {
                // Request AI feedback on the paraphrase
                let prompt = """
                Original article:
                "\(article)"

                User's spoken paraphrase (transcribed):
                "\(transcribedText)"

                Evaluate how well the user paraphrased the article. Consider:
                1. Accuracy - Does it maintain the core meaning and key points?
                2. Originality - Is it in the user's own words or too similar to the original?
                3. Completeness - Does it cover all the main ideas from the original?
                4. Clarity - Is it clear and well-structured?
                5. Filler Words - Did the user avoid filler words like "um," "uh," "like," "you know," etc.?

                First, give a score from 1-10 (where 10 is perfect).

                Then provide specific feedback on what was done well and what could be improved, with special attention to the use of filler words.

                Format your response exactly like this:
                SCORE: [number]
                FEEDBACK: [your detailed feedback]
                """

                // Create a dummy lesson plan for the API call
                let dummyFeedbackLesson = LessonPlan(
                    title: "Paraphrase Feedback",
                    description: "Evaluate paraphrasing skills",
                    prompt: prompt,
                    rules: "Evaluate the paraphrase and provide constructive feedback with a score."
                )

                print("Requesting feedback...")
                let response = try await geminiViewModel.generateResponse(for: prompt, lesson: dummyFeedbackLesson)

                await MainActor.run {
                    isLoading = false

                    // Process the response to extract score and feedback
                    let lines = response.components(separatedBy: "\n")
                    var score = 0
                    var feedback = ""

                    for line in lines {
                        if line.starts(with: "SCORE:") {
                            let scoreText = line.replacingOccurrences(of: "SCORE:", with: "").trimmingCharacters(in: .whitespacesAndNewlines)
                            score = Int(scoreText) ?? 0
                        } else if line.starts(with: "FEEDBACK:") {
                            feedback = line.replacingOccurrences(of: "FEEDBACK:", with: "").trimmingCharacters(in: .whitespacesAndNewlines)
                        } else if !line.isEmpty && !feedback.isEmpty {
                            // Append additional feedback lines
                            feedback += "\n" + line
                        }
                    }

                    // If parsing failed, use the whole response as feedback with a default score
                    if feedback.isEmpty {
                        feedback = response
                        score = 5 // Default middle score
                    }

                    // Update UI
                    paraphraseScore = score
                    feedbackMessage = feedback.trimmingCharacters(in: .whitespacesAndNewlines)
                }
            } catch {
                print("Error generating feedback: \(error.localizedDescription)")

                await MainActor.run {
                    isLoading = false

                    // Show error
                    errorMessage = "Error generating feedback: \(error.localizedDescription)"
                    showError = true

                    // Show generic feedback as fallback
                    paraphraseScore = 5 // Default middle score
                    feedbackMessage = "I couldn't evaluate your paraphrase. Try to ensure your paraphrase captures the main ideas of the article while using your own words and sentence structures. Also, pay attention to avoiding filler words like 'um,' 'uh,' and 'like.'"
                }
            }
        }
    }

    private func resetAndShowDifficultySelection() {
        withAnimation {
            article = ""
            transcribedText = ""
            showArticle = false
            showRecording = false
            showFeedback = false
            isLoading = false
            loadingDifficulty = nil
        }
    }
}

#Preview {
    ArticleRephraseWithRecordingView()
}
