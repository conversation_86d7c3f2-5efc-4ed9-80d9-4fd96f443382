import SwiftUI
import UIKit

struct VocabularyCardView: View {
    @State private var isFlipped = false
    @State private var userSentence = ""
    @State private var showFeedback = false
    @State private var dragAmount = CGSize.zero
    @State private var rotation: Double = 0
    @State private var isButtonPressed = false
    @State private var showingDetailedFeedback = false
    @StateObject private var speechManager = SpeechManager()
    @Environment(\.colorScheme) private var colorScheme
    @EnvironmentObject var viewModel: VocabularyViewModel

    // Dynamic colors based on color scheme
    private var cardBackgroundColor: Color {
        colorScheme == .dark ? Color(.systemGray6) : .white
    }

    private var textColor: Color {
        colorScheme == .dark ? .white : .black
    }

    // Button gradient
    private var buttonGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                colorScheme == .dark ? Color.blue : Color.blue,
                colorScheme == .dark ? Color.purple.opacity(0.8) : Color.blue.opacity(0.7)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    let card: VocabularyCard

    // Calculate dynamic height for text editor based on content
    private func calculateTextHeight() -> CGFloat {
        // Count explicit line breaks
        let explicitLineCount = userSentence.components(separatedBy: "\n").count

        // Count approximate number of lines based on character count and average chars per line
        let charsPerLine = 25.0 // Approximate characters per line (reduced for better wrapping)
        let textLines = max(1, ceil(Double(userSentence.count) / charsPerLine))

        // Use the larger of the two estimates and add a bit of extra space
        let estimatedLines = max(Double(explicitLineCount), textLines) + 0.5

        // Calculate height (22 points per line plus padding)
        let lineHeight: CGFloat = 22
        let minHeight: CGFloat = 44
        let maxHeight: CGFloat = 150 // Increased max height

        return min(maxHeight, max(minHeight, CGFloat(estimatedLines) * lineHeight))
    }

    var body: some View {
        // Configure audio session when view appears
        VStack {
            Spacer()
            ZStack {
                // Front of card (word and meaning)
                VStack(spacing: 15) {
                    HStack(spacing: 8) {
                        HStack(spacing: 6) {
                            // Completion checkmark before the word
                            if card.isCompleted {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.green)
                                    .font(.system(size: 18))
                            }

                            Text(card.word)
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(textColor)
                                .lineLimit(1)
                                .minimumScaleFactor(0.7)
                        }
                        .frame(maxWidth: .infinity, alignment: .center)

                        // Speaker button for pronunciation
                        Button(action: {
                            // Toggle between speaking and stopping
                            if speechManager.isSpeaking {
                                speechManager.stopSpeaking()
                            } else {
                                speechManager.speak(card.word)
                            }
                        }) {
                            Image(systemName: speechManager.isSpeaking ? "pause.circle.fill" : "speaker.wave.2")
                                .font(.system(size: 15))
                                .foregroundColor(speechManager.isSpeaking ? .red : .blue)
                                .frame(width: 20, height: 20)
                                .contentShape(Rectangle())
                        }
                    }
                    .padding(.horizontal, 4)

                    Text(card.meaning)
                        .font(.body)
                        .foregroundColor(textColor.opacity(0.9))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                        .lineLimit(4)
                        .minimumScaleFactor(0.8)
                        .fixedSize(horizontal: false, vertical: false)

                    Image(systemName: "arrow.2.squarepath")
                        .font(.system(size: 20))
                        .foregroundColor(.blue)

                    Text("Tap to flip")
                        .font(.caption)
                        .foregroundColor(colorScheme == .dark ? .gray.opacity(0.8) : .gray)
                }
                .padding()
                .frame(width: 300, height: 240, alignment: .center)
                .fixedSize(horizontal: true, vertical: false)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(cardBackgroundColor)
                        .shadow(radius: colorScheme == .dark ? 1 : 3)
                )
                .opacity(isFlipped ? 0 : 1)
                .rotation3DEffect(
                    .degrees(isFlipped ? 180 : 0),
                    axis: (x: 0, y: 1, z: 0)
                )

                // Back of card (example sentence and practice)
                VStack(spacing: 8) {
                    HStack {
                        Text("Example:")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(textColor)

                        Spacer()

                        // Speaker button for example sentence
                        Button(action: {
                            if speechManager.isSpeaking {
                                speechManager.stopSpeaking()
                            } else {
                                speechManager.speak(card.exampleSentence)
                            }
                        }) {
                            Image(systemName: speechManager.isSpeaking ? "pause.circle.fill" : "speaker.wave.2")
                                .font(.system(size: 16))
                                .foregroundColor(speechManager.isSpeaking ? .red : .blue)
                                .frame(width: 24, height: 24)
                                .contentShape(Rectangle())
                        }
                    }
                    .padding(.horizontal, 4)

                    Text(card.exampleSentence)
                        .font(.body)
                        .italic()
                        .foregroundColor(textColor.opacity(0.9))
                        .multilineTextAlignment(.leading)
                        .padding(.horizontal, 4)
                        .lineLimit(3)
                        .minimumScaleFactor(0.8)
                        .fixedSize(horizontal: false, vertical: false)
                        .padding(.bottom, 2)

                    HStack {
                        Text("Create your own sentence:")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(textColor)

                        Spacer()
                    }
                    .padding(.horizontal, 4)
                    .padding(.top, 2)

                    // Dynamic height text editor with placeholder
                    ZStack(alignment: .topLeading) {
                        // Custom TextEditor with "Done" button
                        CustomTextEditorWithDoneButton(text: $userSentence, textColor: textColor, height: calculateTextHeight())

                        if userSentence.isEmpty {
                            Text("Type your sentence using '\(card.word)' here...")
                                .font(.body)
                                .foregroundColor(textColor.opacity(0.4))
                                .padding(.horizontal, 12)
                                .padding(.vertical, 12)
                                .allowsHitTesting(false)
                        }
                    }
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(colorScheme == .dark ? Color(.systemGray5) : Color(.systemGray6))
                            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                    )
                    .padding(.horizontal, 4)
                }
                .padding()
                .frame(width: 300, height: 240, alignment: .center)
                .fixedSize(horizontal: true, vertical: false)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(cardBackgroundColor)
                        .shadow(radius: colorScheme == .dark ? 1 : 3)
                )
                .opacity(isFlipped ? 1 : 0)
                .rotation3DEffect(
                    .degrees(isFlipped ? 0 : -180),
                    axis: (x: 0, y: 1, z: 0)
                )
            }
            .rotation3DEffect(.degrees(rotation), axis: (x: 0, y: 1, z: 0))
            .animation(.spring(response: 0.4, dampingFraction: 0.7), value: isFlipped)
            .animation(.spring(response: 0.4, dampingFraction: 0.7), value: rotation)
            .onTapGesture {
                withAnimation {
                    isFlipped.toggle()
                }
            }
            .gesture(
                DragGesture(minimumDistance: 5)
                    .onChanged { value in
                        // Convert drag to rotation (10 points = 1 degree, capped at 90 degrees)
                        let dragToRotation = min(max(value.translation.width / 10, -90), 90)
                        rotation = dragToRotation
                    }
                    .onEnded { value in
                        // If dragged more than 45 degrees, flip the card
                        if abs(rotation) > 45 {
                            isFlipped.toggle()
                        }

                        // Reset rotation with animation
                        withAnimation(.easeOut(duration: 0.3)) {
                            rotation = 0
                        }
                    }
            )

            if isFlipped {
                Button(action: {
                    // Add subtle haptic feedback
                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                    impactFeedback.prepare()
                    impactFeedback.impactOccurred()

                    // Submit sentence to Gemini for checking
                    viewModel.checkSentence(word: card.word, sentence: userSentence)
                    showingDetailedFeedback = true
                }) {
                    Text("Submit")
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(width: 140, height: 44)
                        .background(
                            ZStack {
                                // Gradient background
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(buttonGradient)

                                // Subtle inner glow
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                    .blendMode(.overlay)

                                // Press effect
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.white)
                                    .opacity(isButtonPressed ? 0.2 : 0)
                            }
                        )
                        .shadow(color: colorScheme == .dark ?
                                Color.black.opacity(0.5) : Color.blue.opacity(0.3),
                                radius: 8, x: 0, y: 4)
                }
                .pressAction {
                    // On press down
                    withAnimation(.easeInOut(duration: 0.1)) {
                        isButtonPressed = true
                    }
                } onRelease: {
                    // On release
                    withAnimation(.easeInOut(duration: 0.1)) {
                        isButtonPressed = false
                    }
                }
                .padding(.top, 16)
                .disabled(userSentence.isEmpty)
                .opacity(userSentence.isEmpty ? 0.5 : 1)
                .scaleEffect(userSentence.isEmpty ? 0.95 : (isButtonPressed ? 0.97 : 1.0))
                .animation(.easeOut(duration: 0.2), value: userSentence.isEmpty)
                .animation(.easeOut(duration: 0.1), value: isButtonPressed)
            }

            // Removed separate pronunciation button

            Spacer()
        }
        .padding()
        .sheet(isPresented: $showingDetailedFeedback) {
            // Reset when sheet is dismissed
            userSentence = ""
        } content: {
            FeedbackView(word: card.word, sentence: userSentence, onSuccessfulCompletion: {
                // Flip back to the front side
                withAnimation {
                    isFlipped = false
                }

                // Hide keyboard
                UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
            })
            .environmentObject(viewModel)
        }
        .task {
            // Pre-configure audio session when view appears
            speechManager.prepareForSpeech()
        }
    }
}

struct VocabularyCardListView: View {
    @StateObject private var viewModel = VocabularyViewModel()

    // State variables for card animation
    @State private var currentIndex = 0
    @State private var offset = CGSize.zero
    @State private var cardScale: CGFloat = 1.0
    @State private var cardOpacity: Double = 1.0
    @State private var cardRotation: Double = 0.0
    @State private var isAnimating = false
    @Environment(\.colorScheme) private var colorScheme

    // Card background gradient
    private var cardGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                colorScheme == .dark ? Color.blue.opacity(0.6) : Color.blue.opacity(0.2),
                colorScheme == .dark ? Color.purple.opacity(0.3) : Color.purple.opacity(0.1)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    // Progress indicator color
    private var progressColor: Color {
        colorScheme == .dark ? .white.opacity(0.7) : .black.opacity(0.7)
    }

    var body: some View {
        VStack {
            // Progress indicator with card count and title
            VStack(spacing: 4) {
                if viewModel.isLoading {
                    ProgressView("Loading vocabulary words...")
                        .progressViewStyle(CircularProgressViewStyle())
                        .padding()
                } else if !viewModel.errorMessage.isEmpty {
                    Text(viewModel.errorMessage)
                        .font(.caption)
                        .foregroundColor(.red)
                        .padding()
                } else {
                    Text("Card \(currentIndex + 1) of \(viewModel.vocabularyCards.count)")
                        .font(.caption)
                        .foregroundColor(progressColor)
                        .padding(.top, 8)

                    HStack(spacing: 8) {
                        ForEach(0..<viewModel.vocabularyCards.count, id: \.self) { index in
                            Capsule()
                                .fill(currentIndex == index ? progressColor : progressColor.opacity(0.3))
                                .frame(width: 24, height: 4)
                                .scaleEffect(currentIndex == index ? 1.2 : 1.0)
                                .animation(.spring(response: 0.3), value: currentIndex)
                        }
                    }
                }
            }
            .padding(.top, 8)
            .padding(.bottom, 4)

            // Card stack
            ZStack {
                if viewModel.vocabularyCards.isEmpty && !viewModel.isLoading {
                    VStack(spacing: 16) {
                        Image(systemName: "doc.text.magnifyingglass")
                            .font(.system(size: 50))
                            .foregroundColor(.gray)

                        Text("No vocabulary words available")
                            .font(.headline)

                        Button("Refresh") {
                            viewModel.forceRefresh()
                        }
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                    .padding()
                } else if !viewModel.isLoading {
                    // Only create views for visible cards (current and next)
                    // Current card
                    if currentIndex < viewModel.vocabularyCards.count {
                        VocabularyCardView(card: viewModel.vocabularyCards[currentIndex])
                            .environmentObject(viewModel)
                            .scaleEffect(cardScale)
                            .opacity(cardOpacity)
                            .offset(offset)
                            .rotationEffect(.degrees(cardRotation))
                            .zIndex(1)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(cardGradient, lineWidth: 1.5)
                            )
                            // Simplified glow effect
                            .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 2)
                    }

                    // Next card (only if it exists)
                    if currentIndex + 1 < viewModel.vocabularyCards.count {
                        VocabularyCardView(card: viewModel.vocabularyCards[currentIndex + 1])
                            .environmentObject(viewModel)
                            .scaleEffect(0.9)
                            .opacity(0.5)
                            .zIndex(0)
                    }
                }
            }
            .frame(height: 470)
            .gesture(
                DragGesture()
                    .onChanged { gesture in
                        // Only allow horizontal dragging
                        offset = CGSize(width: gesture.translation.width, height: 0)

                        // Scale and fade based on drag distance
                        let dragPercentage = min(abs(gesture.translation.width / 300), 1.0)
                        cardScale = 1.0 - (0.05 * dragPercentage)
                        cardOpacity = 1.0 - (0.3 * dragPercentage)

                        // Add a subtle rotation effect based on drag direction
                        cardRotation = gesture.translation.width * 0.1
                    }
                    .onEnded { gesture in
                        // Determine if we should move to next card
                        if gesture.translation.width < -100 && currentIndex < viewModel.vocabularyCards.count - 1 {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                                offset = CGSize(width: -500, height: 0)
                                cardScale = 0.8
                                cardOpacity = 0
                                cardRotation = -15
                                isAnimating = true
                            }

                            // Delay to allow animation to complete
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                currentIndex += 1
                                offset = CGSize(width: 500, height: 0)
                                cardScale = 0.9
                                cardOpacity = 0.5

                                // Animate the new card in
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                                    offset = .zero
                                    cardScale = 1.0
                                    cardOpacity = 1.0
                                    cardRotation = 0
                                    isAnimating = false
                                }
                            }
                        } else if gesture.translation.width > 100 && currentIndex > 0 {
                            // Move to previous card
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                                offset = CGSize(width: 500, height: 0)
                                cardScale = 0.8
                                cardOpacity = 0
                                cardRotation = 15
                                isAnimating = true
                            }

                            // Delay to allow animation to complete
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                currentIndex -= 1
                                offset = CGSize(width: -500, height: 0)
                                cardScale = 0.9
                                cardOpacity = 0.5

                                // Animate the new card in
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                                    offset = .zero
                                    cardScale = 1.0
                                    cardOpacity = 1.0
                                    cardRotation = 0
                                    isAnimating = false
                                }
                            }
                        } else {
                            // Reset if not swiped far enough - use simpler animation
                            withAnimation(.easeOut(duration: 0.2)) {
                                offset = .zero
                                cardScale = 1.0
                                cardOpacity = 1.0
                                cardRotation = 0
                            }
                        }
                    }
            )

            // Navigation buttons with glass effect
            HStack(spacing: 40) {
                // Spacer to push buttons to center
                Spacer()
                Button(action: {
                    if currentIndex > 0 && !isAnimating {
                        // Move to previous card with animation
                        withAnimation(.easeOut(duration: 0.2)) {
                            offset = CGSize(width: 500, height: 0)
                            cardScale = 0.8
                            cardOpacity = 0
                            cardRotation = 15
                            isAnimating = true
                        }

                        // Delay to allow animation to complete - use shorter delay
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                            currentIndex -= 1
                            offset = CGSize(width: -500, height: 0)
                            cardScale = 0.9
                            cardOpacity = 0.5

                            // Animate the new card in with simpler animation
                            withAnimation(.easeOut(duration: 0.3)) {
                                offset = .zero
                                cardScale = 1.0
                                cardOpacity = 1.0
                                cardRotation = 0
                                isAnimating = false
                            }
                        }
                    }
                }) {
                    ZStack {
                        Circle()
                            .fill(colorScheme == .dark ? Color.black.opacity(0.3) : Color.white.opacity(0.8))
                            .frame(width: 50, height: 50)
                            .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 2)

                        Image(systemName: "arrow.left")
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(currentIndex > 0 ? .blue : .gray.opacity(0.5))
                    }
                }
                .disabled(currentIndex == 0 || isAnimating)

                Button(action: {
                    if currentIndex < viewModel.vocabularyCards.count - 1 && !isAnimating {
                        // Move to next card with animation
                        withAnimation(.easeOut(duration: 0.2)) {
                            offset = CGSize(width: -500, height: 0)
                            cardScale = 0.8
                            cardOpacity = 0
                            cardRotation = -15
                            isAnimating = true
                        }

                        // Delay to allow animation to complete - use shorter delay
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                            currentIndex += 1
                            offset = CGSize(width: 500, height: 0)
                            cardScale = 0.9
                            cardOpacity = 0.5

                            // Animate the new card in with simpler animation
                            withAnimation(.easeOut(duration: 0.3)) {
                                offset = .zero
                                cardScale = 1.0
                                cardOpacity = 1.0
                                cardRotation = 0
                                isAnimating = false
                            }
                        }
                    }
                }) {
                    ZStack {
                        Circle()
                            .fill(colorScheme == .dark ? Color.black.opacity(0.3) : Color.white.opacity(0.8))
                            .frame(width: 50, height: 50)
                            .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 2)

                        Image(systemName: "arrow.right")
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(currentIndex < viewModel.vocabularyCards.count - 1 ? .blue : .gray.opacity(0.5))
                    }
                }
                .disabled(currentIndex == viewModel.vocabularyCards.count - 1 || isAnimating)

                // Spacer to push buttons to center
                Spacer()
            }
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(colorScheme == .dark ? Color.black.opacity(0.2) : Color.white.opacity(0.5))
            )
            .padding(.horizontal)

            Spacer()

            // Add a refresh button at the bottom
            Button(action: {
                viewModel.forceRefresh()
                currentIndex = 0
            }) {
                Label(viewModel.isLoading ? "Loading..." : "New Words",
                      systemImage: viewModel.isLoading ? "hourglass" : "arrow.clockwise")
                    .font(.footnote)
                    .padding(.vertical, 8)
                    .padding(.horizontal, 16)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(20)
            }
            .disabled(viewModel.isLoading)
            .padding(.bottom, 8)

            // Show error message if present
            if !viewModel.errorMessage.isEmpty {
                Text(viewModel.errorMessage)
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding(.bottom, 8)
                    .multilineTextAlignment(.center)
                    .frame(maxWidth: .infinity)
            }
        }
        .navigationTitle("Build Your Vocabulary")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    viewModel.forceRefresh()
                    currentIndex = 0
                }) {
                    if viewModel.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(0.7)
                    } else {
                        Image(systemName: "arrow.clockwise")
                    }
                }
                .disabled(viewModel.isLoading)
            }
        }
    }
}

#Preview {
    NavigationStack {
        VocabularyCardListView()
    }
}

// MARK: - Press Action Modifier

// Custom view modifier to detect press and release actions
struct PressActionModifier: ViewModifier {
    var onPress: () -> Void
    var onRelease: () -> Void

    func body(content: Content) -> some View {
        content
            .simultaneousGesture(
                DragGesture(minimumDistance: 0)
                    .onChanged { _ in
                        onPress()
                    }
                    .onEnded { _ in
                        onRelease()
                    }
            )
    }
}

// Extension to make it easier to use
extension View {
    func pressAction(onPress: @escaping () -> Void, onRelease: @escaping () -> Void) -> some View {
        self.modifier(PressActionModifier(onPress: onPress, onRelease: onRelease))
    }
}

// MARK: - Custom TextEditor with Done Button

// UIKit wrapper for TextEditor with Done button
struct CustomTextEditorWithDoneButton: UIViewRepresentable {
    @Binding var text: String
    var textColor: Color
    var height: CGFloat

    func makeUIView(context: Context) -> UITextView {
        let textView = UITextView()
        textView.delegate = context.coordinator
        textView.font = UIFont.preferredFont(forTextStyle: .body)
        textView.backgroundColor = .clear
        textView.textColor = UIColor(textColor)
        textView.isScrollEnabled = true
        textView.isEditable = true
        textView.isUserInteractionEnabled = true
        textView.autocapitalizationType = .sentences
        textView.returnKeyType = .done // Set return key type to "Done"

        // Add toolbar with Done button
        let toolbar = UIToolbar()
        toolbar.sizeToFit()
        let flexSpace = UIBarButtonItem(barButtonSystemItem: .flexibleSpace, target: nil, action: nil)
        let doneButton = UIBarButtonItem(barButtonSystemItem: .done, target: context.coordinator, action: #selector(Coordinator.doneButtonTapped))
        toolbar.items = [flexSpace, doneButton]
        textView.inputAccessoryView = toolbar

        return textView
    }

    func updateUIView(_ uiView: UITextView, context: Context) {
        uiView.text = text
        uiView.textColor = UIColor(textColor)

        // Update height
        if let constraint = uiView.constraints.first(where: { $0.firstAttribute == .height }) {
            constraint.constant = height
        } else {
            uiView.heightAnchor.constraint(equalToConstant: height).isActive = true
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UITextViewDelegate {
        var parent: CustomTextEditorWithDoneButton

        init(_ parent: CustomTextEditorWithDoneButton) {
            self.parent = parent
        }

        func textViewDidChange(_ textView: UITextView) {
            parent.text = textView.text
        }

        @objc func doneButtonTapped() {
            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        }

        // Handle return key press
        func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
            // If return key is pressed, dismiss keyboard
            if text == "\n" {
                textView.resignFirstResponder()
                return false
            }
            return true
        }
    }
}
