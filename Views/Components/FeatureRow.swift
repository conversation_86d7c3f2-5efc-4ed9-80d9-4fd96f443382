import SwiftUI

// Feature row component for the premium feature list
struct FeatureRow: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .font(.system(size: 16))
                .frame(width: 24, height: 24)
            
            Text(text)
                .font(.subheadline)
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
}

#Preview {
    FeatureRow(icon: "star.fill", text: "Premium feature")
        .padding()
        .previewLayout(.sizeThatFits)
}
