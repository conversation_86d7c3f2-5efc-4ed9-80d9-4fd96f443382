import SwiftUI

struct CreatePostView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel = PostViewModel()
    @State private var postContent = ""
    @State private var isLoading = false
    @FocusState private var isTextFieldFocused: Bool

    var completion: (Bool, String?, Post?) -> Void

    var body: some View {
        NavigationStack {
            VStack(spacing: 16) {
                // Text editor
                ZStack(alignment: .topLeading) {
                    if postContent.isEmpty {
                        Text("Share your thoughts...")
                            .foregroundColor(.gray)
                            .padding(.top, 8)
                            .padding(.leading, 5)
                    }

                    TextEditor(text: $postContent)
                        .focused($isTextFieldFocused)
                        .padding(4)
                        .background(Color(.systemBackground))
                        .cornerRadius(8)
                        .frame(minHeight: 150)
                }
                .onAppear {
                    // Focus the text field when the view appears
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        isTextFieldFocused = true
                    }
                }

                Spacer()
            }
            .padding()
            .navigationTitle("Create Post")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Post") {
                        submitPost()
                    }
                    .disabled(postContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isLoading)
                    .opacity(postContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isLoading ? 0.6 : 1.0)
                }

                ToolbarItem(placement: .keyboard) {
                    HStack {
                        Spacer()
                        Button("Done") {
                            isTextFieldFocused = false
                        }
                    }
                }
            }
            .overlay {
                if isLoading {
                    Color(.systemBackground)
                        .opacity(0.6)
                        .ignoresSafeArea()

                    ProgressView("Posting...")
                        .padding()
                        .background(Color(.secondarySystemBackground))
                        .cornerRadius(10)
                }
            }
        }
    }

    private func submitPost() {
        guard !postContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }

        isLoading = true

        viewModel.createPost(content: postContent) { success, message, newPost in
            isLoading = false

            if success {
                // Ensure we fetch user posts to update the profile view
                viewModel.fetchUserPosts()

                // Pass the newly created post back to the parent view
                completion(true, nil, newPost)
                dismiss()
            } else {
                completion(false, message ?? "Unknown error occurred", nil)
            }
        }
    }
}

#Preview {
    CreatePostView { _, _, _ in }
        .environmentObject(AuthService.shared)
}
