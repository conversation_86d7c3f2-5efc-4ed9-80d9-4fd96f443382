import SwiftUI

struct StoryElementsView: View {
    // MARK: - Properties
    
    // Environment values
    @Environment(\.colorScheme) private var colorScheme
    
    // Dynamic colors based on color scheme
    private var textColor: Color {
        colorScheme == .dark ? .white : .black
    }
    
    private var subtleTextColor: Color {
        colorScheme == .dark ? .white.opacity(0.7) : .black.opacity(0.7)
    }
    
    // Story elements content
    private let storyElements = [
        "Character": "The individuals in your story. Strong characters have clear motivations, flaws, and growth.",
        "Setting": "The time and place where your story unfolds. A well-crafted setting enhances the mood and context.",
        "Plot": "The sequence of events that make up your story. A compelling plot has conflict, rising action, and resolution.",
        "Conflict": "The central problem or challenge that drives the story forward and creates tension.",
        "Theme": "The underlying message or central idea that the story conveys.",
        "Point of View": "The perspective from which the story is told (first person, third person, etc.).",
        "Dialogue": "The conversations between characters that reveal personality and advance the plot.",
        "Pacing": "The rhythm and speed at which the story unfolds."
    ]
    
    // MARK: - Body
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Title
                Text("Key Story Elements and Structure")
                    .font(.title)
                    .fontWeight(.bold)
                    .padding(.top)
                    .multilineTextAlignment(.center)
                
                // Audio player
                ReusableAudioPlayerView(
                    audioFileName: "Key Story Elements and Structure",
                    title: "Key Story Elements and Structure",
                    subtitle: "Listen to learn about essential storytelling elements",
                    accentColor: .purple
                )
                .padding(.horizontal)
                
                // Story elements section
                VStack(alignment: .leading, spacing: 16) {
                    Text("Essential Story Elements")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    ForEach(Array(storyElements.keys.sorted()), id: \.self) { element in
                        elementCard(title: element, description: storyElements[element] ?? "")
                    }
                }
                .padding(.top)
                
                // Story structure section
                VStack(alignment: .leading, spacing: 16) {
                    Text("Classic Story Structure")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    structureCard(
                        title: "Beginning (Setup)",
                        description: "Introduce the main characters, setting, and the initial situation. Establish the tone and hook the audience's interest."
                    )
                    
                    structureCard(
                        title: "Middle (Confrontation)",
                        description: "Develop conflicts and challenges. Characters face obstacles and make decisions that lead to the climax."
                    )
                    
                    structureCard(
                        title: "End (Resolution)",
                        description: "Resolve the main conflict. Show how characters have changed and what they've learned."
                    )
                }
                .padding(.top)
                
                // Tips section
                VStack(alignment: .leading, spacing: 16) {
                    Text("Storytelling Tips")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    tipCard(
                        tip: "Show, don't tell",
                        description: "Use descriptive language and action to let readers experience the story rather than simply being told what happens."
                    )
                    
                    tipCard(
                        tip: "Create relatable characters",
                        description: "Even fantastical characters need human traits and emotions that audiences can connect with."
                    )
                    
                    tipCard(
                        tip: "Maintain consistent voice",
                        description: "Keep your narrative voice and character voices consistent throughout the story."
                    )
                    
                    tipCard(
                        tip: "Edit ruthlessly",
                        description: "Remove anything that doesn't serve the story or advance the plot."
                    )
                }
                .padding(.top)
                
                Spacer(minLength: 40)
            }
            .padding(.bottom)
        }
    }
    
    // MARK: - Helper Views
    
    private func elementCard(title: String, description: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(.purple)
            
            Text(description)
                .font(.body)
                .foregroundColor(subtleTextColor)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        )
        .padding(.horizontal)
    }
    
    private func structureCard(title: String, description: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(.blue)
            
            Text(description)
                .font(.body)
                .foregroundColor(subtleTextColor)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        )
        .padding(.horizontal)
    }
    
    private func tipCard(tip: String, description: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(tip)
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(.green)
            
            Text(description)
                .font(.body)
                .foregroundColor(subtleTextColor)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        )
        .padding(.horizontal)
    }
}

#Preview {
    NavigationStack {
        StoryElementsView()
    }
}
