import SwiftUI
import AVFoundation

struct AudioWaveformView: View {
    @Binding var isPlaying: Bool
    @Binding var currentTime: TimeInterval
    @Binding var duration: TimeInterval
    
    // Number of bars in the waveform
    private let barCount = 30
    
    // Animation properties
    @State private var phase: CGFloat = 0
    @State private var animationTimer: Timer?
    
    var body: some View {
        GeometryReader { geometry in
            HStack(spacing: 4) {
                ForEach(0..<barCount, id: \.self) { index in
                    RoundedRectangle(cornerRadius: 3)
                        .fill(barColor(for: index))
                        .frame(width: barWidth(for: geometry.size.width), 
                               height: barHeight(for: index))
                        .animation(.easeInOut(duration: 0.2), value: isPlaying)
                }
            }
            .frame(height: geometry.size.height)
            .frame(maxWidth: .infinity)
        }
        .frame(height: 80)
        .onAppear {
            startAnimation()
        }
        .onDisappear {
            stopAnimation()
        }
        .onChange(of: isPlaying) { newValue in
            if newValue {
                startAnimation()
            } else {
                stopAnimation()
            }
        }
    }
    
    // Calculate the width of each bar based on available space
    private func barWidth(for totalWidth: CGFloat) -> CGFloat {
        let availableWidth = totalWidth - (CGFloat(barCount - 1) * 4) // Account for spacing
        return max(2, availableWidth / CGFloat(barCount))
    }
    
    // Calculate the height of each bar
    private func barHeight(for index: Int) -> CGFloat {
        if !isPlaying {
            // Static pattern when not playing
            return 20 + sin(CGFloat(index) / CGFloat(barCount) * .pi * 2) * 15
        } else {
            // Dynamic pattern when playing
            let normalizedIndex = CGFloat(index) / CGFloat(barCount)
            let progress = currentTime / max(duration, 1)
            
            // Create a wave pattern that moves with the phase
            let waveHeight = sin(normalizedIndex * .pi * 4 + phase) * 15
            
            // Make bars taller near the current playback position
            let positionEffect = 1.0 - min(1.0, abs(normalizedIndex - progress) * 3)
            let positionBoost = positionEffect * 20
            
            return 20 + waveHeight + positionBoost
        }
    }
    
    // Determine the color of each bar
    private func barColor(for index: Int) -> Color {
        if !isPlaying {
            return Color.gray
        }
        
        let normalizedIndex = CGFloat(index) / CGFloat(barCount)
        let progress = currentTime / max(duration, 1)
        
        // Highlight bars near the current playback position
        let distance = abs(normalizedIndex - progress)
        if distance < 0.1 {
            return Color.blue
        } else if distance < 0.2 {
            return Color.blue.opacity(0.7)
        } else {
            return Color.blue.opacity(0.5)
        }
    }
    
    // Start the animation timer
    private func startAnimation() {
        stopAnimation() // Ensure we don't have multiple timers
        
        animationTimer = Timer.scheduledTimer(withTimeInterval: 0.05, repeats: true) { _ in
            withAnimation {
                // Update the phase to create movement
                phase += isPlaying ? 0.1 : 0
            }
        }
    }
    
    // Stop the animation timer
    private func stopAnimation() {
        animationTimer?.invalidate()
        animationTimer = nil
    }
}

#Preview {
    AudioWaveformView(
        isPlaying: .constant(true),
        currentTime: .constant(30),
        duration: .constant(60)
    )
    .padding()
    .previewLayout(.sizeThatFits)
}
