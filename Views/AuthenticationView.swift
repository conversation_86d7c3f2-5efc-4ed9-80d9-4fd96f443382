import SwiftUI

struct AuthenticationView: View {
    @EnvironmentObject private var authService: AuthService
    @State private var firstName = ""
    @State private var lastName = ""
    @State private var email = ""
    @State private var password = ""
    @State private var isSignUp = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var dragOffset: CGFloat = 0
    @State private var animationAmount = 0.0

    // Add a debug flag to print information
    @State private var debugInfo = ""

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Colorful background with educational theme
                LinearGradient(
                    gradient: Gradient(colors: [Color.blue.opacity(0.7), Color.purple.opacity(0.6)]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .edgesIgnoringSafeArea(.all)

                // Animated background elements
                ZStack {
                    // Speech bubbles and educational elements in background
                    ForEach(0..<8) { i in
                        Image(systemName: ["text.bubble", "book", "pencil", "mic", "graduationcap", "person.2", "globe", "lightbulb"].randomElement()!)
                            .resizable()
                            .frame(width: 30, height: 30)
                            .foregroundStyle(.white.opacity(0.15))
                            .position(
                                x: CGFloat.random(in: 0...geometry.size.width),
                                y: CGFloat.random(in: 0...geometry.size.height)
                            )
                            .rotationEffect(.degrees(animationAmount + Double(i * 45)))
                    }
                }
                .onAppear {
                    withAnimation(Animation.linear(duration: 20).repeatForever(autoreverses: false)) {
                        animationAmount = 360
                    }
                }

                // Card view with glass effect
                VStack {
                    // Debug info text (only in development)
                    #if DEBUG
                    Text(debugInfo)
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(5)
                        .background(Color.black.opacity(0.5))
                        .cornerRadius(5)
                    #endif

                    // Cards container
                    ZStack {
                        // Sign In card
                        createAuthCard(
                            title: "Welcome Back",
                            buttonText: "Log In",
                            action: signIn,
                            showForgotPassword: true,
                            geometry: geometry
                        )
                        .offset(x: isSignUp ? -geometry.size.width + dragOffset : dragOffset)
                        .zIndex(isSignUp ? 0 : 1)

                        // Sign Up card
                        createAuthCard(
                            title: "Join Talk Maxer",
                            buttonText: "Sign Up",
                            action: signUp,
                            showForgotPassword: false,
                            geometry: geometry
                        )
                        .offset(x: isSignUp ? dragOffset : geometry.size.width + dragOffset)
                        .zIndex(isSignUp ? 1 : 0)
                    }
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSignUp)

                    // Page indicator
                    HStack(spacing: 10) {
                        Circle()
                            .fill(isSignUp ? Color.white.opacity(0.5) : Color.white)
                            .frame(width: 10, height: 10)

                        Circle()
                            .fill(isSignUp ? Color.white : Color.white.opacity(0.5))
                            .frame(width: 10, height: 10)
                    }
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSignUp)
                    .padding(.top, 20)
                }
                .gesture(
                    DragGesture(minimumDistance: 10, coordinateSpace: .local)
                        .onChanged { gesture in
                            // Limit the drag offset to prevent extreme values
                            let maxDrag = geometry.size.width * 0.8
                            dragOffset = max(-maxDrag, min(maxDrag, gesture.translation.width))

                            // Update debug info
                            debugInfo = "Drag: \(dragOffset), isSignUp: \(isSignUp)"
                        }
                        .onEnded { gesture in
                            let threshold = geometry.size.width * 0.2

                            // Determine if we should toggle based on gesture direction and current state
                            let shouldToggle: Bool
                            if isSignUp {
                                // If on sign up screen, swipe right to go to sign in
                                shouldToggle = gesture.translation.width > threshold
                            } else {
                                // If on sign in screen, swipe left to go to sign up
                                shouldToggle = gesture.translation.width < -threshold
                            }

                            // Use a more controlled animation
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                if shouldToggle {
                                    isSignUp.toggle()
                                    // Update debug info
                                    debugInfo = "Toggled to \(isSignUp ? "Sign Up" : "Sign In")"
                                }
                                // Always reset drag offset
                                dragOffset = 0
                            }
                        }
                )
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("Authentication"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
            .onChange(of: authService.authError) { _, error in
                if let error = error {
                    alertMessage = error.description
                    showingAlert = true
                }
            }
        }
    }

    private func createAuthCard(title: String, buttonText: String, action: @escaping () -> Void, showForgotPassword: Bool, geometry: GeometryProxy) -> some View {
        VStack(spacing: 20) {
            VStack(spacing: 5) {
                Image(systemName: "person.and.background.dotted")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 60, height: 60)
                    .foregroundColor(.white)

                Text(title)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            .padding(.bottom, 10)

            VStack(spacing: 15) {
                // First and Last Name fields (only for Sign Up)
                if !showForgotPassword { // Sign Up form
                    TextField("", text: $firstName)
                        .placeholder(when: firstName.isEmpty) {
                            Text("First Name").foregroundColor(.white.opacity(0.7))
                        }
                        .autocapitalization(.words)
                        .disableAutocorrection(true)
                        .padding()
                        .background(Color.white.opacity(0.15))
                        .cornerRadius(10)
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                        .foregroundColor(.white)

                    TextField("", text: $lastName)
                        .placeholder(when: lastName.isEmpty) {
                            Text("Last Name").foregroundColor(.white.opacity(0.7))
                        }
                        .autocapitalization(.words)
                        .disableAutocorrection(true)
                        .padding()
                        .background(Color.white.opacity(0.15))
                        .cornerRadius(10)
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                        .foregroundColor(.white)
                }

                TextField("", text: $email)
                    .placeholder(when: email.isEmpty) {
                        Text("Email").foregroundColor(.white.opacity(0.7))
                    }
                    .keyboardType(.emailAddress)
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                    .padding()
                    .background(Color.white.opacity(0.15))
                    .cornerRadius(10)
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                    )
                    .foregroundColor(.white)

                SecureField("", text: $password)
                    .placeholder(when: password.isEmpty) {
                        Text("Password").foregroundColor(.white.opacity(0.7))
                    }
                    .padding()
                    .background(Color.white.opacity(0.15))
                    .cornerRadius(10)
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                    )
                    .foregroundColor(.white)
            }

            if showForgotPassword {
                Button("Forgot Password?") {
                    resetPassword()
                }
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.9))
                .frame(maxWidth: .infinity, alignment: .trailing)
                .padding(.trailing, 4)
            }

            Button(action: action) {
                HStack {
                    Text(buttonText)
                        .fontWeight(.semibold)

                    if authService.isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .padding(.leading, 5)
                    }
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.white.opacity(0.2))
                .foregroundColor(.white)
                .cornerRadius(10)
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(Color.white.opacity(0.5), lineWidth: 1)
                )
            }
            .disabled(isSignUpButtonDisabled(showForgotPassword) || authService.isLoading)
            .opacity(isSignUpButtonDisabled(showForgotPassword) || authService.isLoading ? 0.6 : 1.0)

            Button(action: {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    isSignUp.toggle()
                    // Ensure dragOffset is reset when manually toggling
                    dragOffset = 0
                    // Update debug info
                    debugInfo = "Button toggled to \(isSignUp ? "Sign Up" : "Sign In")"
                }
            }) {
                Text(showForgotPassword ? "Don't have an account? Sign Up" : "Already have an account? Log In")
                    .fontWeight(.medium)
                    .foregroundColor(.white)
            }
            .padding(.top, 5)
        }
        .padding(30)
        .frame(width: geometry.size.width, height: geometry.size.height * 0.8)
        .background(
            RoundedRectangle(cornerRadius: 25)
                .fill(Color.blue.opacity(0.2))
                .background(
                    Color.white.opacity(0.05)
                        .blur(radius: 5)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 25)
                        .stroke(
                            LinearGradient(
                                gradient: Gradient(colors: [Color.white.opacity(0.5), Color.white.opacity(0.1)]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
        )
        .clipShape(RoundedRectangle(cornerRadius: 25))
        .shadow(color: Color.black.opacity(0.2), radius: 20, x: 0, y: 10)
    }

    private func signIn() {
        debugInfo = "Attempting sign in with email: \(email)"

        Task {
            do {
                try await authService.signIn(email: email, password: password)
                debugInfo = "Sign in successful"
            } catch {
                // Error is handled by the onChange listener
                debugInfo = "Sign in error: \(error.localizedDescription)"
            }
        }
    }

    private func signUp() {
        debugInfo = "Attempting sign up with email: \(email), firstName: \(firstName), lastName: \(lastName)"

        Task {
            do {
                try await authService.signUp(
                    firstName: firstName,
                    lastName: lastName,
                    email: email,
                    password: password
                )
                debugInfo = "Sign up successful"
            } catch {
                // Error is handled by the onChange listener
                debugInfo = "Sign up error: \(error.localizedDescription)"
            }
        }
    }

    // Helper method to determine if sign-up/sign-in button should be disabled
    private func isSignUpButtonDisabled(_ isSignIn: Bool) -> Bool {
        if isSignIn {
            // For sign in, only email and password are required
            return email.isEmpty || password.isEmpty
        } else {
            // For sign up, first name, last name, email, and password are required
            return firstName.isEmpty || lastName.isEmpty || email.isEmpty || password.isEmpty
        }
    }

    private func resetPassword() {
        if email.isEmpty {
            alertMessage = "Please enter your email address first"
            showingAlert = true
            return
        }

        Task {
            do {
                try await authService.resetPassword(for: email)
                alertMessage = "Password reset email sent. Please check your inbox."
                showingAlert = true
            } catch {
                alertMessage = "Failed to send password reset email. Please try again."
                showingAlert = true
            }
        }
    }
}

// Helper view extension
extension View {
    func placeholder<Content: View>(when shouldShow: Bool, alignment: Alignment = .leading, @ViewBuilder placeholder: () -> Content) -> some View {
        ZStack(alignment: alignment) {
            placeholder().opacity(shouldShow ? 1 : 0)
            self
        }
    }
}

#Preview {
    AuthenticationView()
        .environmentObject(AuthService.shared)
}
