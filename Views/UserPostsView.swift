import SwiftUI
import RevenueCat

struct UserPostsView: View {
    @StateObject private var viewModel = PostViewModel()
    @ObservedObject private var subscriptionService = SubscriptionService.shared
    @EnvironmentObject private var authService: AuthService
    @EnvironmentObject private var communicationViewModel: CommunicationViewModel
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var postToDelete: String? = nil
    @State private var showingDeleteConfirmation = false
    @State private var showingPaywall = false

    var body: some View {
        NavigationStack {
            ZStack {
                // Background
                Color(UIColor.systemBackground)
                    .ignoresSafeArea()

                // Content
                VStack {
                    // User profile header
                    if let user = authService.user {
                        VStack(spacing: 16) {
                            if let userProfile = authService.userProfile {
                                Text(userProfile.fullName)
                                    .font(.title2)
                                    .fontWeight(.semibold)
                            } else {
                                Text(user.email ?? "User")
                                    .font(.title2)
                                    .fontWeight(.semibold)
                            }

                            if let creationDate = user.metadata.creationDate {
                                Text("Member since: \(formattedDate(creationDate))")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding()
                    }

                    Divider()

                    // Posts section
                    if !subscriptionService.isPostFeatureAvailable() {
                        // Locked UI for premium feature
                        VStack(spacing: 24) {
                            Image(systemName: "lock.fill")
                                .font(.system(size: 70))
                                .foregroundColor(.orange)
                                .padding()
                                .background(
                                    Circle()
                                        .fill(Color.orange.opacity(0.2))
                                        .frame(width: 150, height: 150)
                                )

                            Text("Premium Feature")
                                .font(.title)
                                .fontWeight(.bold)
                                .multilineTextAlignment(.center)

                            Text("The Community Posts feature is available exclusively to premium subscribers.")
                                .font(.headline)
                                .foregroundStyle(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)

                            // Features List
                            VStack(alignment: .leading, spacing: 12) {
                                FeatureRow(icon: "text.bubble.fill", text: "Share your thoughts with the community")
                                FeatureRow(icon: "hand.thumbsup.fill", text: "Like and comment on posts")
                                FeatureRow(icon: "person.2.fill", text: "Connect with other learners")
                                FeatureRow(icon: "lightbulb.fill", text: "Get inspired by others' experiences")
                            }
                            .padding()
                            .background(Color(.systemGray6).opacity(0.7))
                            .cornerRadius(16)
                            .padding(.horizontal)

                            // Unlock button
                            Button(action: {
                                showingPaywall = true
                            }) {
                                HStack {
                                    Image(systemName: "star.fill")
                                        .foregroundColor(.yellow)
                                    Text("Unlock Premium")
                                        .fontWeight(.semibold)
                                        .foregroundColor(.white)
                                }
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(
                                    LinearGradient(
                                        gradient: Gradient(colors: [Color.blue, Color.purple]),
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .cornerRadius(12)
                            }
                            .padding(.horizontal)
                            .padding(.top, 16)
                        }
                        .padding()
                    } else {
                        VStack(alignment: .leading) {
                            Text("Your Posts")
                                .font(.headline)
                                .padding(.horizontal)

                            if viewModel.isLoading {
                                ProgressView("Loading your posts...")
                                    .padding()
                            } else if viewModel.userPosts.isEmpty {
                                VStack(spacing: 20) {
                                    Image(systemName: "text.bubble")
                                        .font(.system(size: 40))
                                        .foregroundColor(.gray)

                                    Text("You haven't posted anything yet")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                }
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 40)
                            } else {
                                ScrollView {
                                    LazyVStack(spacing: 16) {
                                        ForEach(viewModel.userPosts) { post in
                                            UserPostCardView(post: post, viewModel: viewModel) {
                                                postToDelete = post.id
                                                showingDeleteConfirmation = true
                                            }
                                            .padding(.horizontal)
                                            // Don't use .id modifier to prevent UI jumps
                                        }
                                    }
                                    .padding(.vertical)
                                }
                                .refreshable {
                                    viewModel.fetchUserPosts()
                                }
                            }
                        }
                    }
                }
            }
            .navigationTitle("Profile")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        if subscriptionService.isPostFeatureAvailable() {
                            viewModel.fetchUserPosts()
                        }
                    }) {
                        Image(systemName: "arrow.clockwise")
                    }
                    .disabled(!subscriptionService.isPostFeatureAvailable())
                    .opacity(subscriptionService.isPostFeatureAvailable() ? 1.0 : 0.5)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    NavigationLink {
                        SettingsView(viewModel: communicationViewModel)
                    } label: {
                        Image(systemName: "gearshape")
                    }
                }
            }
            .sheet(isPresented: $showingPaywall) {
                PaywallView()
                    .onDisappear {
                        // Refresh subscription status when paywall is dismissed
                        subscriptionService.forceCheckSubscriptionStatus()
                    }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text("Error"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
            .confirmationDialog(
                "Are you sure you want to delete this post?",
                isPresented: $showingDeleteConfirmation,
                titleVisibility: .visible
            ) {
                Button("Delete", role: .destructive) {
                    if let postId = postToDelete {
                        deletePost(postId: postId)
                    }
                }
                Button("Cancel", role: .cancel) {
                    postToDelete = nil
                }
            }
            .onAppear {
                // Only fetch posts if the feature is available
                if subscriptionService.isPostFeatureAvailable() {
                    // Force fetch user posts when the view appears
                    print("🔍 UserPostsView appeared - fetching user posts")
                    viewModel.fetchUserPosts()

                    // Also set up the user posts listener to ensure real-time updates
                    viewModel.setupUserPostsListener()
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("SubscriptionStatusChanged"))) { _ in
                // Refresh posts if subscription status changes to subscribed
                if subscriptionService.isPostFeatureAvailable() {
                    viewModel.fetchUserPosts()
                    viewModel.setupUserPostsListener()
                }
            }
        }
    }

    private func deletePost(postId: String) {
        viewModel.deletePost(postId: postId) { success, message in
            if !success, let message = message {
                alertMessage = message
                showingAlert = true
            }
            postToDelete = nil
        }
    }

    private func formattedDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }
}

struct UserPostCardView: View {
    // MARK: - Properties
    let post: Post
    let onDelete: () -> Void

    @ObservedObject var viewModel: PostViewModel
    @EnvironmentObject private var authService: AuthService

    // State variables
    @State private var localPost: Post
    @State private var showingComments = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var isUpdatingLike = false
    @State private var isUpdatingDislike = false
    @State private var animateLike = false

    // MARK: - Initialization
    init(post: Post, viewModel: PostViewModel, onDelete: @escaping () -> Void) {
        self.post = post
        self.viewModel = viewModel
        self.onDelete = onDelete
        // Initialize the local copy of the post
        _localPost = State(initialValue: post)
    }

    // MARK: - Computed Properties
    private var isLikedByCurrentUser: Bool {
        guard let userId = authService.user?.uid else { return false }
        return localPost.likedBy.contains(userId)
    }

    private var isDislikedByCurrentUser: Bool {
        guard let userId = authService.user?.uid else { return false }
        return localPost.dislikedBy.contains(userId)
    }

    // MARK: - Body
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Post header
            HStack {
                Text(localPost.timeAgo)
                    .font(.caption)
                    .foregroundColor(.secondary)

                Spacer()

                Button(action: onDelete) {
                    Image(systemName: "trash")
                        .foregroundColor(.red)
                }
            }

            // Post content
            Text(localPost.content)
                .font(.system(size: 16, weight: .regular, design: .rounded))
                .lineLimit(nil)
                .fixedSize(horizontal: false, vertical: true)
                .padding(.vertical, 4)

            // Interaction buttons
            HStack(spacing: 20) {
                // Like button
                Button(action: toggleLike) {
                    HStack(spacing: 4) {
                        if isUpdatingLike {
                            ProgressView()
                                .scaleEffect(0.7)
                                .frame(width: 16, height: 16)
                        } else {
                            Image(systemName: isLikedByCurrentUser ? "hand.thumbsup.fill" : "hand.thumbsup")
                                .foregroundColor(isLikedByCurrentUser ? .blue : .gray)
                                .scaleEffect(animateLike ? 1.5 : 1.0)
                                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: animateLike)
                        }

                        Text("\(localPost.likeCount)")
                            .font(.subheadline)
                            .foregroundColor(isLikedByCurrentUser ? .blue : .gray)
                    }
                }
                .disabled(isUpdatingLike || isUpdatingDislike)

                // Dislike button
                Button(action: toggleDislike) {
                    HStack(spacing: 4) {
                        if isUpdatingDislike {
                            ProgressView()
                                .scaleEffect(0.7)
                                .frame(width: 16, height: 16)
                        } else {
                            Image(systemName: isDislikedByCurrentUser ? "hand.thumbsdown.fill" : "hand.thumbsdown")
                                .foregroundColor(isDislikedByCurrentUser ? .red : .gray)
                        }

                        Text("\(localPost.dislikeCount)")
                            .font(.subheadline)
                            .foregroundColor(isDislikedByCurrentUser ? .red : .gray)
                    }
                }
                .disabled(isUpdatingLike || isUpdatingDislike)

                // Comment button
                Button(action: {
                    // Use a more refined spring animation with better parameters for smoother effect
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.75, blendDuration: 0.2)) {
                        showingComments.toggle()

                        // Fetch comments when showing comments section
                        if showingComments {
                            // Ensure we fetch the latest comments
                            viewModel.fetchComments(for: localPost.id ?? "")
                        }
                    }
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "bubble.left")
                            .foregroundColor(showingComments ? .blue : .gray)
                            // Add subtle scale animation to the icon
                            .scaleEffect(showingComments ? 1.1 : 1.0)
                            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: showingComments)

                        Text("\(localPost.commentCount)")
                            .font(.subheadline)
                            .foregroundColor(showingComments ? .blue : .gray)
                    }
                    // Add subtle scale animation to the entire button
                    .scaleEffect(showingComments ? 1.05 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.7), value: showingComments)
                }

                Spacer()
            }
            .padding(.top, 8)

            // Comments section (conditionally shown)
            if showingComments {
                CommentView(viewModel: viewModel, postId: localPost.id ?? "")
                    .frame(maxHeight: 400)
                    .padding(.top, 8)
                    // Simplified transition with smoother animation
                    .transition(.opacity.combined(with: .move(edge: .bottom)))
                    // Add a subtle shadow for depth
                    .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
                    // Update local post when the post in viewModel changes
                    .onChange(of: viewModel.posts) { newPosts in
                        if let postId = localPost.id, let updatedPost = newPosts.first(where: { $0.id == postId }) {
                            // Only update if the comment count has changed
                            if updatedPost.commentCount != localPost.commentCount {
                                print("📊 Updating localPost comment count from \(localPost.commentCount) to \(updatedPost.commentCount)")
                                localPost = updatedPost
                            }
                        }
                    }
                    // Also check userPosts for updates
                    .onChange(of: viewModel.userPosts) { newUserPosts in
                        if let postId = localPost.id, let updatedPost = newUserPosts.first(where: { $0.id == postId }) {
                            // Only update if the comment count has changed
                            if updatedPost.commentCount != localPost.commentCount {
                                print("📊 Updating localPost comment count from \(localPost.commentCount) to \(updatedPost.commentCount)")
                                localPost = updatedPost
                            }
                        }
                    }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.secondarySystemBackground))
        )
        .alert(isPresented: $showingAlert) {
            Alert(
                title: Text("Error"),
                message: Text(alertMessage),
                dismissButton: .default(Text("OK"))
            )
        }
        // Update comment count when posts or userPosts array changes
        .onChange(of: viewModel.posts) { newPosts in
            if let postId = localPost.id, let updatedPost = newPosts.first(where: { $0.id == postId }) {
                // Only update if the comment count has changed
                if updatedPost.commentCount != localPost.commentCount {
                    print("📊 Updating localPost comment count from \(localPost.commentCount) to \(updatedPost.commentCount)")
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        localPost = updatedPost
                    }
                }
            }
        }
        .onChange(of: viewModel.userPosts) { newUserPosts in
            if let postId = localPost.id, let updatedPost = newUserPosts.first(where: { $0.id == postId }) {
                // Only update if the comment count has changed
                if updatedPost.commentCount != localPost.commentCount {
                    print("📊 Updating localPost comment count from \(localPost.commentCount) to \(updatedPost.commentCount)")
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        localPost = updatedPost
                    }
                }
            }
        }
    }

    // MARK: - Methods
    private func toggleLike() {
        guard let postId = localPost.id, let userId = authService.user?.uid, !isUpdatingLike, !isUpdatingDislike else { return }

        // Always trigger animation for visual feedback
        animateLike = true

        // Reset animation after delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.animateLike = false
        }

        // Update local state immediately
        var updatedPost = localPost
        let alreadyLiked = updatedPost.likedBy.contains(userId)

        if alreadyLiked {
            // Remove like
            updatedPost.likeCount -= 1
            updatedPost.likedBy.removeAll { $0 == userId }
        } else {
            // Add like
            updatedPost.likeCount += 1
            updatedPost.likedBy.append(userId)

            // If user previously disliked, remove dislike
            if updatedPost.dislikedBy.contains(userId) {
                updatedPost.dislikeCount -= 1
                updatedPost.dislikedBy.removeAll { $0 == userId }
            }
        }

        // Update UI immediately with animation
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            localPost = updatedPost
        }

        // Set updating flag for UI
        isUpdatingLike = true

        // Send to server in the background
        viewModel.toggleLikeWithoutRefresh(postId: postId, userId: userId, isLiked: !alreadyLiked) { success, message in
            DispatchQueue.main.async {
                self.isUpdatingLike = false

                if !success, let message = message {
                    self.alertMessage = message
                    self.showingAlert = true

                    // Revert changes if server update fails
                    withAnimation {
                        self.localPost = self.post
                        self.animateLike = false
                    }
                }
            }
        }
    }

    private func toggleDislike() {
        guard let postId = localPost.id, let userId = authService.user?.uid, !isUpdatingLike, !isUpdatingDislike else { return }

        // Update local state immediately
        var updatedPost = localPost
        let alreadyDisliked = updatedPost.dislikedBy.contains(userId)

        if alreadyDisliked {
            // Remove dislike
            updatedPost.dislikeCount -= 1
            updatedPost.dislikedBy.removeAll { $0 == userId }
        } else {
            // Add dislike
            updatedPost.dislikeCount += 1
            updatedPost.dislikedBy.append(userId)

            // If user previously liked, remove like
            if updatedPost.likedBy.contains(userId) {
                updatedPost.likeCount -= 1
                updatedPost.likedBy.removeAll { $0 == userId }
            }
        }

        // Update UI immediately with animation
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            localPost = updatedPost
        }

        // Set updating flag for UI
        isUpdatingDislike = true

        // Send to server in the background
        viewModel.toggleDislikeWithoutRefresh(postId: postId, userId: userId, isDisliked: !alreadyDisliked) { success, message in
            DispatchQueue.main.async {
                self.isUpdatingDislike = false

                if !success, let message = message {
                    self.alertMessage = message
                    self.showingAlert = true

                    // Revert changes if server update fails
                    withAnimation {
                        self.localPost = self.post
                    }
                }
            }
        }
    }
}

#Preview {
    UserPostsView()
        .environmentObject(AuthService.shared)
        .environmentObject(CommunicationViewModel())
}
