import SwiftUI

struct MainTabView: View {
    @State private var selectedTab = 1 // Default to "Study" tab
    @EnvironmentObject private var viewModel: CommunicationViewModel
    @EnvironmentObject private var authService: AuthService

    var body: some View {
        TabView(selection: $selectedTab) {
            // Post Tab
            PostListView()
                .environmentObject(authService)
                .tabItem {
                    Label("Post", systemImage: "text.bubble.fill")
                }
                .tag(0)

            // Study Tab (Current ContentView)
            ContentView()
                .environmentObject(viewModel)
                .tabItem {
                    Label("Study", systemImage: "book.fill")
                }
                .tag(1)

            // Me Tab
            UserPostsView()
                .environmentObject(authService)
                .tabItem {
                    Label("Me", systemImage: "person.fill")
                }
                .tag(2)
        }
    }
}

#Preview {
    MainTabView()
        .environmentObject(CommunicationViewModel())
        .environmentObject(AuthService.shared)
}
